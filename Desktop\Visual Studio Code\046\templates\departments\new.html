{% extends "base.html" %}

{% block title %}قسم جديد - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus me-2"></i>
                إنشاء قسم جديد
            </h1>
            <a href="{{ url_for('departments.list_departments') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    بيانات القسم الجديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم القسم
                            </div>
                            <div class="form-text">
                                يجب أن يكون اسم القسم فريداً
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="manager_id" class="form-label">مدير القسم</label>
                            <select class="form-select" id="manager_id" name="manager_id">
                                <option value="">اختر مدير القسم (اختياري)</option>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.name }} ({{ user.role.value }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                يمكن تعيين مدير للقسم لاحقاً
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف القسم</label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="وصف مختصر عن القسم ومهامه (اختياري)"></textarea>
                        <div class="form-text">
                            وصف مختصر يساعد في فهم دور القسم ومسؤولياته
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                <i class="fas fa-toggle-on text-success me-1"></i>
                                القسم نشط
                            </label>
                        </div>
                        <div class="form-text">
                            الأقسام النشطة فقط تظهر في قوائم الاختيار
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong>
                        <ul class="mb-0 mt-2">
                            <li>سيتم استخدام هذا القسم في المراسلات الداخلية</li>
                            <li>يمكن تعديل بيانات القسم لاحقاً</li>
                            <li>مدير القسم سيحصل على صلاحيات إضافية للمراسلات</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إنشاء القسم
                        </button>
                        <a href="{{ url_for('departments.list_departments') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Department Guidelines -->
<div class="row mt-4 justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    إرشادات إنشاء الأقسام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i class="fas fa-check me-1"></i>
                            أفضل الممارسات
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                استخدم أسماء واضحة ومفهومة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                أضف وصفاً مفصلاً للقسم
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                عيّن مديراً مؤهلاً للقسم
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                تأكد من تفعيل القسم
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            تنبيهات مهمة
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-info-circle text-info me-2"></i>
                                لا يمكن حذف قسم له رسائل مرتبطة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-info-circle text-info me-2"></i>
                                اسم القسم يجب أن يكون فريداً
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-info-circle text-info me-2"></i>
                                مدير القسم يحصل على صلاحيات إضافية
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-info-circle text-info me-2"></i>
                                الأقسام غير النشطة لا تظهر في القوائم
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Toggle switch styling
    const toggleSwitch = document.getElementById('is_active');
    const toggleLabel = toggleSwitch.nextElementSibling;

    toggleSwitch.addEventListener('change', function() {
        const icon = toggleLabel.querySelector('i');
        if (this.checked) {
            icon.className = 'fas fa-toggle-on text-success me-1';
            toggleLabel.innerHTML = '<i class="fas fa-toggle-on text-success me-1"></i>القسم نشط';
        } else {
            icon.className = 'fas fa-toggle-off text-secondary me-1';
            toggleLabel.innerHTML = '<i class="fas fa-toggle-off text-secondary me-1"></i>القسم غير نشط';
        }
    });
});
</script>
{% endblock %}
