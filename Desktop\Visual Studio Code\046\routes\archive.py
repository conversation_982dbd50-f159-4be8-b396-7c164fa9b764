from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Message, Archive, ArchivedMessage, User, Department
from datetime import datetime, date
from sqlalchemy import and_, or_, extract
import os

archive_bp = Blueprint('archive', __name__, url_prefix='/archive')

@archive_bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للأرشيف"""
    archives = Archive.query.filter_by(is_active=True).order_by(Archive.created_at.desc()).all()
    
    # إحصائيات
    stats = {
        'total_archives': Archive.query.filter_by(is_active=True).count(),
        'total_archived_messages': ArchivedMessage.query.count(),
        'total_size_mb': sum([archive.get_size_mb() for archive in archives]),
        'active_messages': Message.query.count()
    }
    
    return render_template('archive/index.html', archives=archives, stats=stats)

@archive_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_archive():
    """إنشاء أرشيف جديد"""
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        archive_type = request.form.get('archive_type')
        
        if not name or not archive_type:
            flash('اسم الأرشيف ونوعه مطلوبان', 'error')
            return redirect(url_for('archive.new_archive'))
        
        # إنشاء الأرشيف
        archive = Archive(
            name=name,
            description=description,
            archive_type=archive_type,
            created_by=current_user.id
        )
        
        # إعداد التواريخ حسب نوع الأرشيف
        if archive_type == 'yearly':
            year = request.form.get('year', type=int)
            if year:
                archive.year = year
        elif archive_type == 'monthly':
            year = request.form.get('year', type=int)
            month = request.form.get('month', type=int)
            if year and month:
                archive.year = year
                archive.month = month
        elif archive_type == 'custom':
            start_date = request.form.get('start_date')
            end_date = request.form.get('end_date')
            if start_date and end_date:
                archive.start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                archive.end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        db.session.add(archive)
        db.session.commit()
        
        flash('تم إنشاء الأرشيف بنجاح', 'success')
        return redirect(url_for('archive.view_archive', archive_id=archive.id))
    
    return render_template('archive/new.html')

@archive_bp.route('/<int:archive_id>')
@login_required
def view_archive(archive_id):
    """عرض تفاصيل الأرشيف"""
    archive = Archive.query.get_or_404(archive_id)
    
    # الحصول على الرسائل المؤرشفة مع الترقيم
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    messages = ArchivedMessage.query.filter_by(archive_id=archive_id)\
        .order_by(ArchivedMessage.archived_at.desc())\
        .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('archive/view.html', archive=archive, messages=messages)

@archive_bp.route('/<int:archive_id>/add_messages', methods=['GET', 'POST'])
@login_required
def add_messages(archive_id):
    """إضافة رسائل إلى الأرشيف"""
    archive = Archive.query.get_or_404(archive_id)
    
    if request.method == 'POST':
        message_ids = request.form.getlist('message_ids')
        archive_notes = request.form.get('archive_notes', '')
        
        if not message_ids:
            flash('يرجى اختيار رسائل للأرشفة', 'error')
            return redirect(url_for('archive.add_messages', archive_id=archive_id))
        
        archived_count = 0
        for message_id in message_ids:
            message = Message.query.get(message_id)
            if message:
                # التحقق من عدم وجود الرسالة في الأرشيف مسبقاً
                existing = ArchivedMessage.query.filter_by(
                    archive_id=archive_id,
                    original_message_id=message.id
                ).first()
                
                if not existing:
                    archived_message = ArchivedMessage(
                        archive_id=archive_id,
                        original_message_id=message.id,
                        message_number=message.message_number,
                        source=message.source,
                        destination=message.destination,
                        subject=message.subject,
                        content=message.content,
                        message_date=message.message_date,
                        date_created=message.date_created,
                        creator_name=message.creator.name if message.creator else '',
                        creator_department=message.creator.department.name if message.creator and message.creator.department else '',
                        approver_name=message.approver.name if message.approver else '',
                        approval_date=message.approval_date,
                        approval_status=message.approval_status.value if message.approval_status else '',
                        attachment_name=message.attachment_name,
                        attachment_path=message.attachment_path,
                        archived_by=current_user.id,
                        archive_notes=archive_notes
                    )
                    
                    # حساب حجم المرفق
                    if message.attachment_path:
                        try:
                            file_path = os.path.join('uploads', message.attachment_path)
                            if os.path.exists(file_path):
                                archived_message.attachment_size = os.path.getsize(file_path)
                        except:
                            pass
                    
                    db.session.add(archived_message)
                    archived_count += 1
        
        db.session.commit()
        flash(f'تم أرشفة {archived_count} رسالة بنجاح', 'success')
        return redirect(url_for('archive.view_archive', archive_id=archive_id))
    
    # الحصول على الرسائل المتاحة للأرشفة
    query = Message.query
    
    # تصفية حسب نوع الأرشيف
    if archive.archive_type == 'yearly' and archive.year:
        query = query.filter(extract('year', Message.message_date) == archive.year)
    elif archive.archive_type == 'monthly' and archive.year and archive.month:
        query = query.filter(
            and_(
                extract('year', Message.message_date) == archive.year,
                extract('month', Message.message_date) == archive.month
            )
        )
    elif archive.archive_type == 'custom' and archive.start_date and archive.end_date:
        query = query.filter(
            and_(
                Message.message_date >= archive.start_date,
                Message.message_date <= archive.end_date
            )
        )
    
    # استبعاد الرسائل المؤرشفة مسبقاً
    archived_message_ids = [am.original_message_id for am in 
                           ArchivedMessage.query.filter_by(archive_id=archive_id).all()]
    if archived_message_ids:
        query = query.filter(~Message.id.in_(archived_message_ids))
    
    messages = query.order_by(Message.date_created.desc()).all()
    
    return render_template('archive/add_messages.html', archive=archive, messages=messages)

@archive_bp.route('/<int:archive_id>/export')
@login_required
def export_archive(archive_id):
    """تصدير الأرشيف"""
    archive = Archive.query.get_or_404(archive_id)
    
    # TODO: إضافة وظيفة التصدير (PDF, Excel, etc.)
    flash('ميزة التصدير ستكون متاحة قريباً', 'info')
    return redirect(url_for('archive.view_archive', archive_id=archive_id))

@archive_bp.route('/<int:archive_id>/delete', methods=['POST'])
@login_required
def delete_archive(archive_id):
    """حذف الأرشيف"""
    archive = Archive.query.get_or_404(archive_id)
    
    # التحقق من الصلاحيات
    if not current_user.can_delete_messages():
        flash('ليس لديك صلاحية لحذف الأرشيف', 'error')
        return redirect(url_for('archive.view_archive', archive_id=archive_id))
    
    # حذف الرسائل المؤرشفة أولاً
    ArchivedMessage.query.filter_by(archive_id=archive_id).delete()
    
    # حذف الأرشيف
    db.session.delete(archive)
    db.session.commit()
    
    flash('تم حذف الأرشيف بنجاح', 'success')
    return redirect(url_for('archive.index'))

@archive_bp.route('/message/<int:message_id>/remove', methods=['POST'])
@login_required
def remove_message(message_id):
    """إزالة رسالة من الأرشيف"""
    archived_message = ArchivedMessage.query.get_or_404(message_id)
    archive_id = archived_message.archive_id
    
    db.session.delete(archived_message)
    db.session.commit()
    
    flash('تم إزالة الرسالة من الأرشيف', 'success')
    return redirect(url_for('archive.view_archive', archive_id=archive_id))

@archive_bp.route('/search')
@login_required
def search():
    """البحث في الأرشيف"""
    query = request.args.get('q', '')
    archive_id = request.args.get('archive_id', type=int)
    
    results = []
    if query:
        search_query = ArchivedMessage.query
        
        if archive_id:
            search_query = search_query.filter_by(archive_id=archive_id)
        
        search_query = search_query.filter(
            or_(
                ArchivedMessage.subject.contains(query),
                ArchivedMessage.content.contains(query),
                ArchivedMessage.source.contains(query),
                ArchivedMessage.destination.contains(query),
                ArchivedMessage.message_number.contains(query)
            )
        )
        
        results = search_query.order_by(ArchivedMessage.archived_at.desc()).all()
    
    archives = Archive.query.filter_by(is_active=True).all()
    return render_template('archive/search.html', results=results, query=query, 
                         archives=archives, selected_archive=archive_id)
