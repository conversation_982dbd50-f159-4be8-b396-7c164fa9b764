#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء جدول الجهات في قاعدة البيانات
"""

from app import create_app
from models import db, Entity, User
from datetime import datetime

def create_entities_table():
    """إنشاء جدول الجهات وإضافة بيانات تجريبية"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 إنشاء جدول الجهات...")
            
            # إنشاء الجداول
            db.create_all()
            
            print("✅ تم إنشاء جدول الجهات بنجاح")
            
            # التحقق من وجود مستخدم admin
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ لم يتم العثور على مستخدم admin")
                return False
            
            # إضافة بيانات تجريبية
            print("\n📝 إضافة بيانات تجريبية...")
            
            sample_entities = [
                {
                    'name': 'وزارة الداخلية',
                    'name_en': 'Ministry of Interior',
                    'entity_type': 'government',
                    'category': 'ministry',
                    'address': 'الرياض، المملكة العربية السعودية',
                    'phone': '+966-11-4012345',
                    'email': '<EMAIL>',
                    'website': 'https://www.moi.gov.sa',
                    'contact_person': 'أحمد محمد السعيد',
                    'contact_title': 'مدير الإدارة العامة',
                    'contact_phone': '+966-11-4012346',
                    'contact_email': '<EMAIL>',
                    'reference_number': 'MOI-001',
                    'is_government': True,
                    'is_frequent': True,
                    'created_by': admin_user.id
                },
                {
                    'name': 'جامعة الملك سعود',
                    'name_en': 'King Saud University',
                    'entity_type': 'academic',
                    'category': 'university',
                    'address': 'الرياض، المملكة العربية السعودية',
                    'phone': '+966-11-4670000',
                    'email': '<EMAIL>',
                    'website': 'https://www.ksu.edu.sa',
                    'contact_person': 'د. فاطمة عبدالله',
                    'contact_title': 'عميد شؤون الطلاب',
                    'contact_phone': '+966-11-4670001',
                    'contact_email': '<EMAIL>',
                    'reference_number': 'KSU-001',
                    'is_government': True,
                    'is_frequent': True,
                    'created_by': admin_user.id
                },
                {
                    'name': 'شركة أرامكو السعودية',
                    'name_en': 'Saudi Aramco',
                    'entity_type': 'private',
                    'category': 'company',
                    'address': 'الظهران، المملكة العربية السعودية',
                    'phone': '+966-13-8760000',
                    'email': '<EMAIL>',
                    'website': 'https://www.aramco.com',
                    'contact_person': 'خالد أحمد الغامدي',
                    'contact_title': 'مدير العلاقات الحكومية',
                    'contact_phone': '+966-13-8760001',
                    'contact_email': '<EMAIL>',
                    'reference_number': 'ARAMCO-001',
                    'is_government': False,
                    'is_frequent': True,
                    'created_by': admin_user.id
                },
                {
                    'name': 'مستشفى الملك فيصل التخصصي',
                    'name_en': 'King Faisal Specialist Hospital',
                    'entity_type': 'government',
                    'category': 'hospital',
                    'address': 'الرياض، المملكة العربية السعودية',
                    'phone': '+966-11-4647272',
                    'email': '<EMAIL>',
                    'website': 'https://www.kfshrc.edu.sa',
                    'contact_person': 'د. سارة محمد النصر',
                    'contact_title': 'مدير الشؤون الإدارية',
                    'contact_phone': '+966-11-4647273',
                    'contact_email': '<EMAIL>',
                    'reference_number': 'KFSH-001',
                    'is_government': True,
                    'is_frequent': False,
                    'created_by': admin_user.id
                },
                {
                    'name': 'البنك الأهلي السعودي',
                    'name_en': 'National Commercial Bank',
                    'entity_type': 'private',
                    'category': 'bank',
                    'address': 'جدة، المملكة العربية السعودية',
                    'phone': '+966-12-6499999',
                    'email': '<EMAIL>',
                    'website': 'https://www.alahli.com',
                    'contact_person': 'عبدالرحمن سالم القحطاني',
                    'contact_title': 'مدير الخدمات المصرفية',
                    'contact_phone': '+966-12-6499998',
                    'contact_email': '<EMAIL>',
                    'reference_number': 'NCB-001',
                    'is_government': False,
                    'is_frequent': False,
                    'created_by': admin_user.id
                },
                {
                    'name': 'أمانة منطقة الرياض',
                    'name_en': 'Riyadh Municipality',
                    'entity_type': 'government',
                    'category': 'municipality',
                    'address': 'الرياض، المملكة العربية السعودية',
                    'phone': '+966-11-4456666',
                    'email': '<EMAIL>',
                    'website': 'https://www.alriyadh.gov.sa',
                    'contact_person': 'محمد عبدالعزيز الدوسري',
                    'contact_title': 'مدير إدارة التراخيص',
                    'contact_phone': '+966-11-4456667',
                    'contact_email': '<EMAIL>',
                    'reference_number': 'RYD-MUN-001',
                    'is_government': True,
                    'is_frequent': True,
                    'created_by': admin_user.id
                }
            ]
            
            # إضافة الجهات التجريبية
            for entity_data in sample_entities:
                # التحقق من عدم وجود الجهة مسبقاً
                existing_entity = Entity.query.filter_by(name=entity_data['name']).first()
                if not existing_entity:
                    entity = Entity(**entity_data)
                    db.session.add(entity)
                    print(f"  ✅ تم إضافة: {entity_data['name']}")
                else:
                    print(f"  ⚠️ موجود مسبقاً: {entity_data['name']}")
            
            db.session.commit()
            
            # عرض الإحصائيات
            total_entities = Entity.query.count()
            government_entities = Entity.query.filter_by(is_government=True).count()
            frequent_entities = Entity.query.filter_by(is_frequent=True).count()
            
            print(f"\n📊 إحصائيات الجهات:")
            print(f"  📋 إجمالي الجهات: {total_entities}")
            print(f"  🏛️ الجهات الحكومية: {government_entities}")
            print(f"  ⭐ متكررة التراسل: {frequent_entities}")
            
            print("\n🎉 تم إنشاء قسم الجهات بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الجهات: {str(e)}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("🏢 إنشاء قسم الجهات والمؤسسات")
    print("=" * 50)
    
    success = create_entities_table()
    
    if success:
        print("\n✅ تم إنشاء قسم الجهات بنجاح!")
        print("\n🌐 يمكنك الآن الوصول إلى:")
        print("  📋 قائمة الجهات: http://localhost:8585/entities")
        print("  ➕ إضافة جهة جديدة: http://localhost:8585/entities/new")
        print("  🔍 البحث في الجهات: http://localhost:8585/entities/api/search")
        print("  📊 إحصائيات الجهات: http://localhost:8585/entities/stats")
        print("\n💡 الميزات المتاحة:")
        print("  🏛️ تصنيف الجهات (حكومية، خاصة، أكاديمية، إلخ)")
        print("  📞 معلومات الاتصال الكاملة")
        print("  👤 بيانات الأشخاص المسؤولين")
        print("  🔍 البحث والفلترة المتقدمة")
        print("  ⭐ تمييز الجهات متكررة التراسل")
        print("  📊 إحصائيات وتقارير شاملة")
    else:
        print("\n❌ فشل في إنشاء قسم الجهات!")
        print("تحقق من:")
        print("  • اتصال قاعدة البيانات")
        print("  • وجود مستخدم admin")
        print("  • صحة النماذج")
