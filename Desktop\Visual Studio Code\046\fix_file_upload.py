#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة رفع الملفات
"""

import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def fix_file_upload():
    """إصلاح مشكلة رفع الملفات"""
    
    app = create_app()
    with app.app_context():
        print("🔧 إصلاح مشكلة رفع الملفات...")
        print("=" * 50)
        
        # التحقق من مجلد الرفع
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        print(f"📁 مجلد الرفع: {upload_folder}")
        
        # إنشاء مجلد الرفع إذا لم يكن موجوداً
        if not os.path.exists(upload_folder):
            os.makedirs(upload_folder, exist_ok=True)
            print(f"✅ تم إنشاء مجلد الرفع: {upload_folder}")
        else:
            print(f"✅ مجلد الرفع موجود: {upload_folder}")
        
        # إنشاء مجلدات فرعية للسنوات
        current_year = datetime.now().year
        for year in range(current_year - 2, current_year + 2):
            year_folder = os.path.join(upload_folder, str(year))
            if not os.path.exists(year_folder):
                os.makedirs(year_folder, exist_ok=True)
                print(f"✅ تم إنشاء مجلد السنة: {year_folder}")
        
        # إنشاء مجلد التوقيعات
        signatures_folder = os.path.join(upload_folder, 'signatures')
        if not os.path.exists(signatures_folder):
            os.makedirs(signatures_folder, exist_ok=True)
            print(f"✅ تم إنشاء مجلد التوقيعات: {signatures_folder}")
        
        # التحقق من الامتدادات المسموحة
        allowed_extensions = app.config.get('ALLOWED_EXTENSIONS', set())
        print(f"\n📋 الامتدادات المسموحة ({len(allowed_extensions)}):")
        
        # تجميع الامتدادات حسب النوع
        extension_groups = {
            'المستندات': ['txt', 'pdf', 'doc', 'docx', 'rtf', 'odt'],
            'جداول البيانات': ['xls', 'xlsx', 'ods', 'csv'],
            'العروض التقديمية': ['ppt', 'pptx', 'odp'],
            'الصور': ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'svg', 'webp'],
            'الأرشيف': ['zip', 'rar', '7z', 'tar', 'gz'],
            'الصوت والفيديو': ['mp3', 'wav', 'mp4', 'avi', 'mov', 'wmv', 'flv'],
            'أخرى': ['xml', 'json', 'html', 'css', 'js']
        }
        
        for group_name, extensions in extension_groups.items():
            supported = [ext for ext in extensions if ext in allowed_extensions]
            if supported:
                print(f"   ✅ {group_name}: {', '.join(supported)}")
            else:
                print(f"   ❌ {group_name}: غير مدعوم")
        
        # التحقق من الحد الأقصى لحجم الملف
        max_size = app.config.get('MAX_CONTENT_LENGTH', 0)
        if max_size:
            max_size_mb = max_size / (1024 * 1024)
            print(f"\n📏 الحد الأقصى لحجم الملف: {max_size_mb:.1f} MB")
        else:
            print("\n⚠️ لم يتم تحديد حد أقصى لحجم الملف")
        
        # اختبار رفع ملف تجريبي
        print(f"\n🧪 اختبار رفع الملفات...")
        
        # إنشاء ملف تجريبي
        test_file_path = os.path.join(upload_folder, 'test_file.txt')
        try:
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write('هذا ملف تجريبي لاختبار رفع الملفات\n')
                f.write(f'تم إنشاؤه في: {datetime.now()}\n')
            print(f"✅ تم إنشاء ملف تجريبي: {test_file_path}")
            
            # التحقق من وجود الملف
            if os.path.exists(test_file_path):
                file_size = os.path.getsize(test_file_path)
                print(f"✅ الملف موجود، الحجم: {file_size} بايت")
                
                # حذف الملف التجريبي
                os.remove(test_file_path)
                print("✅ تم حذف الملف التجريبي")
            else:
                print("❌ فشل في إنشاء الملف التجريبي")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار رفع الملفات: {e}")
        
        # نصائح لحل مشاكل رفع الملفات
        print(f"\n💡 نصائح لحل مشاكل رفع الملفات:")
        print("   1. تأكد من أن امتداد الملف مدعوم")
        print("   2. تأكد من أن حجم الملف أقل من الحد الأقصى")
        print("   3. تأكد من وجود صلاحيات الكتابة في مجلد الرفع")
        print("   4. تأكد من أن اسم الملف لا يحتوي على أحرف خاصة")
        print("   5. جرب رفع ملف بامتداد مختلف للتأكد")
        
        print(f"\n✅ تم الانتهاء من فحص نظام رفع الملفات")

if __name__ == '__main__':
    fix_file_upload()
