#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لجميع الإصلاحات والتحديثات
"""

import requests
import sys

def test_all_fixes():
    """اختبار شامل لجميع الإصلاحات"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار لوحة التحكم مع التحديثات الجديدة
        print("\n📊 اختبار لوحة التحكم المحدثة...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            dashboard_checks = [
                ('الحالة', 'عمود الحالة'),
                ('الأيام', 'عمود الأيام'),
                ('منذ الإنشاء', 'نص الأيام'),
                ('badge', 'الشارات الملونة'),
                ('get_status_display', 'دالة عرض الحالة')
            ]
            
            for check, description in dashboard_checks:
                if check in content:
                    print(f"✅ {description}")
                else:
                    print(f"⚠️ {description} (قد يكون مخفي)")
        else:
            print(f"❌ فشل الوصول للوحة التحكم: {response.status_code}")
            return False
        
        # اختبار قائمة الرسائل المحدثة
        print("\n📋 اختبار قائمة الرسائل المحدثة...")
        response = session.get(f"{base_url}/messages")
        if response.status_code == 200:
            content = response.text
            messages_checks = [
                ('الحالة', 'عمود الحالة في قائمة الرسائل'),
                ('الأيام', 'عمود الأيام في قائمة الرسائل'),
                ('get_priority_display', 'دالة عرض الأولوية'),
                ('badge', 'الشارات الملونة')
            ]
            
            for check, description in messages_checks:
                if check in content:
                    print(f"✅ {description}")
                else:
                    print(f"⚠️ {description} (قد يكون مخفي)")
        else:
            print(f"❌ فشل الوصول لقائمة الرسائل: {response.status_code}")
            return False
        
        # اختبار قسم الجهات
        print("\n🏢 اختبار قسم الجهات...")
        response = session.get(f"{base_url}/departments")
        if response.status_code == 200:
            print("✅ قسم الجهات يعمل بنجاح")
        else:
            print(f"❌ فشل في الوصول لقسم الجهات: {response.status_code}")
            return False
        
        # اختبار قسم المستخدمين (للتأكد من إصلاح خطأ Role)
        print("\n👥 اختبار قسم المستخدمين...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            print("✅ قسم المستخدمين يعمل بنجاح (تم إصلاح خطأ Role)")
        else:
            print(f"❌ فشل في الوصول لقسم المستخدمين: {response.status_code}")
            return False
        
        # اختبار إنشاء مستخدم جديد (للتأكد من عدم وجود خطأ Role)
        print("\n➕ اختبار إنشاء مستخدم جديد...")
        response = session.get(f"{base_url}/users/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء مستخدم جديد تعمل بنجاح")
        else:
            print(f"❌ فشل في الوصول لصفحة إنشاء مستخدم: {response.status_code}")
            return False
        
        # اختبار قسم الصلاحيات
        print("\n🔐 اختبار قسم الصلاحيات...")
        response = session.get(f"{base_url}/permissions")
        if response.status_code == 200:
            print("✅ قسم الصلاحيات يعمل بنجاح")
        else:
            print(f"❌ فشل في الوصول لقسم الصلاحيات: {response.status_code}")
            return False
        
        # اختبار قسم الأرشيف
        print("\n📁 اختبار قسم الأرشيف...")
        response = session.get(f"{base_url}/archive")
        if response.status_code == 200:
            print("✅ قسم الأرشيف يعمل بنجاح")
        else:
            print(f"❌ فشل في الوصول لقسم الأرشيف: {response.status_code}")
            return False
        
        # اختبار التوقيع الإلكتروني
        print("\n🖋️ اختبار التوقيع الإلكتروني...")
        response = session.get(f"{base_url}/users/signature")
        if response.status_code == 200:
            print("✅ قسم التوقيع الإلكتروني يعمل بنجاح")
        else:
            print(f"❌ فشل في الوصول لقسم التوقيع: {response.status_code}")
            return False
        
        # اختبار إنشاء رسالة جديدة
        print("\n📝 اختبار إنشاء رسالة جديدة...")
        response = session.get(f"{base_url}/messages/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء رسالة جديدة تعمل بنجاح")
        else:
            print(f"❌ فشل في الوصول لصفحة إنشاء رسالة: {response.status_code}")
            return False
        
        # اختبار البحث
        print("\n🔍 اختبار البحث...")
        response = session.get(f"{base_url}/search")
        if response.status_code == 200:
            print("✅ صفحة البحث تعمل بنجاح")
        else:
            print(f"❌ فشل في الوصول لصفحة البحث: {response.status_code}")
            return False
        
        print("\n🎉 تم اكتمال جميع الاختبارات بنجاح!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل لجميع الإصلاحات والتحديثات")
    print("=" * 60)
    
    success = test_all_fixes()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت! النظام يعمل بشكل مثالي!")
        print("\n📋 الإصلاحات والتحديثات المطبقة:")
        print("\n🔧 الإصلاحات:")
        print("  ✅ إصلاح خطأ NameError: name 'Role' is not defined")
        print("  ✅ إضافة استيراد Role في routes/users.py")
        print("  ✅ التأكد من عمل جميع الأقسام بدون أخطاء")
        print("\n🆕 التحديثات الجديدة:")
        print("  ✅ إضافة عمود الحالة في لوحة التحكم وقائمة الرسائل")
        print("  ✅ إضافة عمود الأيام منذ الإنشاء")
        print("  ✅ عرض الأولوية بألوان مختلفة")
        print("  ✅ عرض حالة الاعتماد")
        print("  ✅ تحسين التصميم المتجاوب")
        print("  ✅ إضافة دوال مساعدة في النموذج")
        print("\n🏢 قسم الجهات:")
        print("  ✅ إدارة الجهات/الأقسام")
        print("  ✅ تعيين المستخدمين للجهات")
        print("  ✅ إحصائيات الجهات")
        print("  ✅ APIs للجهات والمستخدمين")
        print("\n🌐 جميع الأقسام تعمل بنجاح:")
        print("  ✅ لوحة التحكم - http://localhost:8585")
        print("  ✅ إدارة الرسائل - http://localhost:8585/messages")
        print("  ✅ إدارة المستخدمين - http://localhost:8585/users")
        print("  ✅ إدارة الجهات - http://localhost:8585/departments")
        print("  ✅ إدارة الصلاحيات - http://localhost:8585/permissions")
        print("  ✅ التوقيع الإلكتروني - http://localhost:8585/users/signature")
        print("  ✅ نظام الأرشيف - http://localhost:8585/archive")
        print("  ✅ البحث - http://localhost:8585/search")
        print("\n🎯 النظام جاهز للاستخدام بجميع ميزاته!")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض الاختبارات!")
        sys.exit(1)
