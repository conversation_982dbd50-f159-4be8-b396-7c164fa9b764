#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
عارض ملفات مبسط
"""

import os
import base64
import mimetypes

def get_file_info(file_path, filename):
    """الحصول على معلومات الملف"""
    if not os.path.exists(file_path):
        return None
    
    file_size = os.path.getsize(file_path)
    extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    return {
        'name': filename,
        'size': file_size,
        'extension': extension,
        'mime_type': mimetypes.guess_type(filename)[0] or 'application/octet-stream'
    }

def can_view_in_browser(filename):
    """التحقق من إمكانية عرض الملف في المتصفح"""
    if not filename or '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    viewable_extensions = [
        'pdf', 'txt', 'html', 'xml', 'json', 'css', 'js',
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp',
        'mp3', 'wav', 'mp4', 'webm'
    ]
    
    return extension in viewable_extensions

def get_file_icon_class(filename):
    """الحصول على فئة أيقونة الملف"""
    if not filename or '.' not in filename:
        return 'fa-file'
    
    extension = filename.rsplit('.', 1)[1].lower()
    
    icon_map = {
        'pdf': 'fa-file-pdf',
        'doc': 'fa-file-word', 'docx': 'fa-file-word',
        'xls': 'fa-file-excel', 'xlsx': 'fa-file-excel',
        'ppt': 'fa-file-powerpoint', 'pptx': 'fa-file-powerpoint',
        'txt': 'fa-file-alt', 'rtf': 'fa-file-alt',
        'jpg': 'fa-file-image', 'jpeg': 'fa-file-image', 'png': 'fa-file-image',
        'gif': 'fa-file-image', 'bmp': 'fa-file-image', 'svg': 'fa-file-image',
        'mp3': 'fa-file-audio', 'wav': 'fa-file-audio',
        'mp4': 'fa-file-video', 'avi': 'fa-file-video',
        'zip': 'fa-file-archive', 'rar': 'fa-file-archive',
        'html': 'fa-file-code', 'css': 'fa-file-code', 'js': 'fa-file-code'
    }
    
    return icon_map.get(extension, 'fa-file')

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"
