{% extends "base.html" %}

{% block title %}عرض المستخدم {{ user.name }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user me-2"></i>
                تفاصيل المستخدم
            </h1>
            <div>
                <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
                {% if current_user.is_manager() or current_user.id == user.id %}
                <a href="{{ url_for('users.edit_user', id=user.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                {% endif %}
                {% if false %}
                <!-- Signature management temporarily disabled -->
                <a href="{{ url_for('users.signature') }}" class="btn btn-info">
                    <i class="fas fa-signature me-2"></i>
                    إدارة التوقيع
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">معلومات المستخدم</h5>
                <div>
                    {% if user.role.value == 'manager' %}
                        <span class="badge bg-danger fs-6">مدير</span>
                    {% elif user.role.value == 'secretary' %}
                        <span class="badge bg-warning fs-6">سكرتير</span>
                    {% else %}
                        <span class="badge bg-info fs-6">موظف</span>
                    {% endif %}
                    
                    {% if user.is_active %}
                        <span class="badge bg-success fs-6">نشط</span>
                    {% else %}
                        <span class="badge bg-secondary fs-6">غير نشط</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الاسم الكامل:</strong>
                        <p class="mb-0">{{ user.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>اسم المستخدم:</strong>
                        <p class="mb-0">{{ user.username }}</p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الدور:</strong>
                        <p class="mb-0">
                            {% if user.role.value == 'manager' %}
                                مدير النظام
                            {% elif user.role.value == 'secretary' %}
                                سكرتير
                            {% else %}
                                موظف
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <strong>القسم:</strong>
                        <p class="mb-0">
                            {% if user.department %}
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-building text-primary me-2"></i>
                                    <span>{{ user.department.name }}</span>
                                    {% if user.department.manager_id == user.id %}
                                        <span class="badge bg-warning ms-2">مدير القسم</span>
                                    {% endif %}
                                </div>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الحالة:</strong>
                        <p class="mb-0">
                            {% if user.is_active %}
                                <span class="text-success">نشط</span>
                            {% else %}
                                <span class="text-muted">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الإنشاء:</strong>
                        <p class="mb-0">{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>عدد الرسائل المنشأة:</strong>
                        <p class="mb-0">{{ user.created_messages.count() }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>المراسلات المكلف بها:</strong>
                        <p class="mb-0">{{ user.assigned_messages.count() }}</p>
                    </div>
                </div>

                {% if false %}
                <!-- Signature section temporarily disabled -->
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>التوقيع الإلكتروني:</strong>
                        <p class="mb-0">
                            <span class="badge bg-secondary">
                                <i class="fas fa-times me-1"></i>
                                غير متوفر حالياً
                            </span>
                        </p>
                    </div>
                </div>
                {% endif %}
                
                <!-- Permissions -->
                <div class="mb-3">
                    <strong>الصلاحيات:</strong>
                    <div class="mt-2">
                        {% if user.role.value == 'manager' %}
                        <span class="badge bg-success me-1">جميع الصلاحيات</span>
                        <span class="badge bg-primary me-1">إدارة المستخدمين</span>
                        <span class="badge bg-primary me-1">حذف الرسائل</span>
                        <span class="badge bg-primary me-1">إدارة النظام</span>
                        {% elif user.role.value == 'secretary' %}
                        <span class="badge bg-warning me-1">إدارة المستخدمين</span>
                        <span class="badge bg-info me-1">إدارة الرسائل</span>
                        <span class="badge bg-info me-1">عرض التقارير</span>
                        {% else %}
                        <span class="badge bg-secondary me-1">عرض الرسائل</span>
                        <span class="badge bg-secondary me-1">إنشاء الرسائل</span>
                        <span class="badge bg-secondary me-1">تعديل رسائله فقط</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Messages -->
        {% if user.created_messages.count() > 0 %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">الرسائل الحديثة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>النوع</th>
                                <th>الموضوع</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in user.created_messages.order_by(user.created_messages.c.date_created.desc()).limit(5) %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.registration_number }}
                                    </a>
                                </td>
                                <td>
                                    {% if message.is_incoming() %}
                                        <span class="badge bg-success">واردة</span>
                                    {% else %}
                                        <span class="badge bg-info">صادرة</span>
                                    {% endif %}
                                </td>
                                <td>{{ message.subject[:30] }}{% if message.subject|length > 30 %}...{% endif %}</td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('messages.list_messages') }}?created_by={{ user.id }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع رسائل هذا المستخدم
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        {% if false %}
        <!-- Signature Preview temporarily disabled -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-signature me-2"></i>
                    معاينة التوقيع الإلكتروني
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    ميزة التوقيع الإلكتروني غير متوفرة حالياً
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الإجراءات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('users.edit_user', id=user.id) }}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المستخدم
                    </a>
                    
                    {% if current_user.is_manager() %}
                    <form method="POST" action="{{ url_for('users.toggle_user_status', id=user.id) }}">
                        <button type="submit" class="btn btn-outline-{{ 'secondary' if user.is_active else 'success' }} w-100"
                                {% if user.is_manager() and user.is_active %}
                                onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المدير؟')"
                                {% endif %}>
                            <i class="fas fa-{{ 'ban' if user.is_active else 'check' }} me-2"></i>
                            {{ 'إلغاء التفعيل' if user.is_active else 'تفعيل' }}
                        </button>
                    </form>
                    {% endif %}
                    
                    <a href="{{ url_for('messages.list_messages') }}?created_by={{ user.id }}" class="btn btn-outline-info">
                        <i class="fas fa-envelope me-2"></i>
                        رسائل المستخدم
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">الإحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ user.created_messages.filter_by(message_type='incoming').count() }}</h4>
                        <small class="text-muted">رسائل واردة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ user.created_messages.filter_by(message_type='outgoing').count() }}</h4>
                        <small class="text-muted">رسائل صادرة</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-12">
                        <h4 class="text-success">{{ user.created_messages.count() }}</h4>
                        <small class="text-muted">إجمالي الرسائل</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if false %}
<!-- Signature JavaScript temporarily disabled -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Signature functionality disabled
});
</script>
{% endif %}
{% endblock %}
