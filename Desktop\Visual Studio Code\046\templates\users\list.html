{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users me-2"></i>
                إدارة المستخدمين
            </h1>
            <a href="{{ url_for('users.new_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>
                مستخدم جديد
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الاسم</th>
                                <th>اسم المستخدم</th>
                                <th>الدور</th>
                                <th>القسم</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <strong>{{ user.name }}</strong>
                                    </div>
                                </td>
                                <td>{{ user.username }}</td>
                                <td>
                                    {% if user.role.value == 'ADMIN' %}
                                        <span class="badge bg-dark">مدير النظام</span>
                                    {% elif user.role.value == 'DIRECTOR' %}
                                        <span class="badge bg-purple">مدير عام</span>
                                    {% elif user.role.value == 'DEPARTMENT_HEAD' %}
                                        <span class="badge bg-primary">رئيس قسم</span>
                                    {% elif user.role.value == 'MANAGER' %}
                                        <span class="badge bg-danger">مدير</span>
                                    {% elif user.role.value == 'SUPERVISOR' %}
                                        <span class="badge bg-success">مشرف</span>
                                    {% elif user.role.value == 'SECRETARY' %}
                                        <span class="badge bg-warning">سكرتير</span>
                                    {% else %}
                                        <span class="badge bg-info">موظف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.department %}
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-building text-primary me-1"></i>
                                            <span>{{ user.department.name }}</span>
                                            {% if user.department.manager_id == user.id %}
                                                <span class="badge bg-warning ms-1" title="مدير القسم">
                                                    <i class="fas fa-crown"></i>
                                                </span>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('users.view_user', id=user.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('users.edit_user', id=user.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if current_user.is_manager() %}
                                        <form method="POST" action="{{ url_for('users.toggle_user_status', id=user.id) }}" 
                                              style="display: inline;">
                                            <button type="submit" 
                                                    class="btn btn-outline-{{ 'secondary' if user.is_active else 'success' }} btn-sm"
                                                    title="{{ 'إلغاء التفعيل' if user.is_active else 'تفعيل' }}"
                                                    {% if user.is_manager() and user.is_active %}
                                                    onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المدير؟')"
                                                    {% endif %}>
                                                <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-users fa-4x mb-3"></i>
                    <h4>لا يوجد مستخدمون</h4>
                    <p>لم يتم إنشاء أي مستخدمين حتى الآن</p>
                    <a href="{{ url_for('users.new_user') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إنشاء أول مستخدم
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-2">
        <div class="card bg-dark text-white">
            <div class="card-body text-center">
                <h4>{{ users|selectattr('role.value', 'equalto', 'ADMIN')|list|length }}</h4>
                <p class="mb-0 small">مدراء النظام</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-purple text-white">
            <div class="card-body text-center">
                <h4>{{ users|selectattr('role.value', 'equalto', 'DIRECTOR')|list|length }}</h4>
                <p class="mb-0 small">مدراء عامون</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4>{{ users|selectattr('role.value', 'equalto', 'DEPARTMENT_HEAD')|list|length }}</h4>
                <p class="mb-0 small">رؤساء أقسام</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4>{{ users|selectattr('role.value', 'equalto', 'MANAGER')|list|length }}</h4>
                <p class="mb-0 small">مدراء</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ users|selectattr('role.value', 'equalto', 'SUPERVISOR')|list|length }}</h4>
                <p class="mb-0 small">مشرفون</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4>{{ users|selectattr('role.value', 'equalto', 'SECRETARY')|list|length }}</h4>
                <p class="mb-0 small">سكرتارية</p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ users|selectattr('role.value', 'equalto', 'EMPLOYEE')|list|length }}</h3>
                <p class="mb-0">الموظفون</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ users|selectattr('is_active', 'equalto', true)|list|length }}</h3>
                <p class="mb-0">النشطون</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h3>{{ users|length }}</h3>
                <p class="mb-0">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.bg-purple {
    background-color: #6f42c1 !important;
}
.badge.bg-purple {
    background-color: #6f42c1 !important;
}
.avatar-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}
</style>
{% endblock %}
