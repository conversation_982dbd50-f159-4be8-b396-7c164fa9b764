{% extends "base.html" %}

{% block title %}تعديل الرسالة {{ message.registration_number }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-edit me-2"></i>
                تعديل الرسالة
            </h1>
            <div>
                <a href="{{ url_for('messages.view_message', id=message.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للرسالة
                </a>
                <a href="{{ url_for('messages.list_messages') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-list me-2"></i>
                    قائمة الرسائل
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تعديل بيانات الرسالة: {{ message.registration_number }}</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="message_type" class="form-label">نوع الرسالة <span class="text-danger">*</span></label>
                            <select class="form-select" id="message_type" name="message_type" required disabled>
                                <option value="incoming" {% if message.message_type.value == 'incoming' %}selected{% endif %}>واردة</option>
                                <option value="outgoing" {% if message.message_type.value == 'outgoing' %}selected{% endif %}>صادرة</option>
                            </select>
                            <div class="form-text">لا يمكن تغيير نوع الرسالة بعد الإنشاء</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="registration_number" class="form-label">رقم التسجيل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="registration_number" name="registration_number" 
                                   value="{{ message.registration_number }}" readonly>
                            <div class="form-text">لا يمكن تغيير رقم التسجيل بعد الإنشاء</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="destination" class="form-label">الجهة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="destination" name="destination" 
                                   value="{{ message.destination }}" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم الجهة
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="message_date" class="form-label">تاريخ الرسالة <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="message_date" name="message_date" 
                                   value="{{ message.message_date.strftime('%Y-%m-%d') }}" readonly>
                            <div class="form-text">لا يمكن تغيير تاريخ الرسالة بعد الإنشاء</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject" class="form-label">الموضوع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="subject" name="subject" 
                               value="{{ message.subject }}" required>
                        <div class="invalid-feedback">
                            يرجى إدخال موضوع الرسالة
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">المحتوى</label>
                        <textarea class="form-control" id="content" name="content" rows="5" 
                                  placeholder="محتوى الرسالة (اختياري)">{{ message.content or '' }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">القسم</label>
                            <input type="text" class="form-control" id="department" name="department" 
                                   value="{{ message.department or '' }}" placeholder="اسم القسم (اختياري)">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="normal" {% if message.priority == 'normal' %}selected{% endif %}>عادي</option>
                                <option value="high" {% if message.priority == 'high' %}selected{% endif %}>مهم</option>
                                <option value="urgent" {% if message.priority == 'urgent' %}selected{% endif %}>عاجل</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Current Attachment -->
                    {% if message.attachment_name %}
                    <div class="mb-3">
                        <label class="form-label">المرفق الحالي</label>
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="fas {{ get_file_icon(message.attachment_name) }} me-2 text-primary"></i>
                                    <span>{{ message.attachment_name }}</span>
                                </div>
                                <div>
                                    <a href="{{ url_for('messages.download_attachment', filename=message.attachment_path) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download me-1"></i>تحميل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- New Attachment -->
                    <div class="mb-4">
                        <label class="form-label">
                            {% if message.attachment_name %}
                            استبدال المرفق
                            {% else %}
                            إضافة مرفق
                            {% endif %}
                        </label>
                        <div class="file-upload-area">
                            <input type="file" name="attachment" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt" style="display: none;">
                            <i class="fas fa-cloud-upload-alt fa-2x mb-2 text-muted"></i>
                            <p class="mb-0">اسحب الملف هنا أو انقر للاختيار</p>
                            <small class="text-muted">
                                {% if message.attachment_name %}
                                سيتم استبدال المرفق الحالي بالملف الجديد
                                {% else %}
                                الملفات المدعومة: PDF, Word, صور, نصوص (حد أقصى 16 ميجابايت)
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التعديلات
                        </button>
                        <div>
                            <a href="{{ url_for('messages.view_message', id=message.id) }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            {% if current_user.can_delete_messages() %}
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash me-2"></i>
                                حذف الرسالة
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </form>
                
                {% if current_user.can_delete_messages() %}
                <!-- Hidden delete form -->
                <form id="deleteForm" method="POST" action="{{ url_for('messages.delete_message', id=message.id) }}" style="display: none;">
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Edit History Card -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">معلومات الرسالة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <strong>منشئ الرسالة:</strong>
                        <p class="mb-0">{{ message.creator.name }}</p>
                    </div>
                    <div class="col-md-4">
                        <strong>تاريخ الإنشاء:</strong>
                        <p class="mb-0">{{ message.date_created.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-4">
                        <strong>السنة:</strong>
                        <p class="mb-0">{{ message.year }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف هذه الرسالة؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع المرفقات المرتبطة بالرسالة.')) {
        document.getElementById('deleteForm').submit();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Highlight changed fields
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[type="text"], textarea, select');
    
    inputs.forEach(input => {
        const originalValue = input.value;
        input.addEventListener('input', function() {
            if (this.value !== originalValue) {
                this.classList.add('border-warning');
            } else {
                this.classList.remove('border-warning');
            }
        });
    });
});
</script>
{% endblock %}
