#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل مع تفاصيل التشخيص
"""

import sys
import traceback
import os

def main():
    print("🔍 تشخيص مفصل لتشغيل النظام")
    print("=" * 50)
    
    try:
        print("1️⃣ فحص المجلد الحالي...")
        print(f"   📁 المجلد: {os.getcwd()}")
        print(f"   📋 الملفات: {os.listdir('.')}")
        
        print("\n2️⃣ فحص Python...")
        print(f"   🐍 الإصدار: {sys.version}")
        print(f"   📍 المسار: {sys.executable}")
        
        print("\n3️⃣ محاولة استيراد Flask...")
        import flask
        print(f"   ✅ Flask {flask.__version__}")
        
        print("\n4️⃣ محاولة استيراد التطبيق...")
        from app import create_app
        print("   ✅ تم استيراد create_app")
        
        print("\n5️⃣ محاولة إنشاء التطبيق...")
        app = create_app()
        print("   ✅ تم إنشاء التطبيق")
        
        print("\n6️⃣ فحص قاعدة البيانات...")
        if os.path.exists('correspondence.db'):
            print("   ✅ قاعدة البيانات موجودة")
        else:
            print("   ⚠️ قاعدة البيانات غير موجودة، سيتم إنشاؤها")
        
        print("\n7️⃣ تشغيل الخادم...")
        print("   🌐 الرابط: http://localhost:8585")
        print("   🔐 المستخدم: admin")
        print("   🔐 كلمة المرور: admin123")
        print("   🛑 اضغط Ctrl+C للإيقاف")
        print("=" * 50)
        
        app.run(
            host='0.0.0.0',
            port=8585,
            debug=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"\n❌ خطأ في الاستيراد:")
        print(f"   📋 التفاصيل: {str(e)}")
        print(f"   📍 النوع: {type(e).__name__}")
        traceback.print_exc()
        
        print(f"\n💡 حلول مقترحة:")
        print(f"   • تثبيت المتطلبات: pip install flask flask-sqlalchemy flask-login")
        print(f"   • التحقق من وجود الملفات المطلوبة")
        
    except Exception as e:
        print(f"\n❌ خطأ عام:")
        print(f"   📋 التفاصيل: {str(e)}")
        print(f"   📍 النوع: {type(e).__name__}")
        traceback.print_exc()
        
        print(f"\n💡 حلول مقترحة:")
        print(f"   • التحقق من صحة الملفات")
        print(f"   • التحقق من الصلاحيات")
        print(f"   • تشغيل كمدير")
    
    input("\n⏸️ اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
