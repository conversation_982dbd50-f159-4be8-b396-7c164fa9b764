{% extends "base.html" %}

{% block title %}{{ archive.name }} - الأرشيف{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-archive me-2"></i>
                {{ archive.name }}
            </h1>
            <div>
                <a href="{{ url_for('archive.index') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للأرشيف
                </a>
                <a href="{{ url_for('archive.add_messages', archive_id=archive.id) }}" class="btn btn-success me-2">
                    <i class="fas fa-plus me-2"></i>
                    إضافة رسائل
                </a>
                <a href="{{ url_for('archive.export_archive', archive_id=archive.id) }}" class="btn btn-info">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Archive Info -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الأرشيف
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>اسم الأرشيف:</strong> {{ archive.name }}
                    </div>
                    <div class="col-md-6">
                        <strong>النوع:</strong>
                        {% if archive.archive_type == 'yearly' %}
                            <span class="badge bg-primary">سنوي</span>
                        {% elif archive.archive_type == 'monthly' %}
                            <span class="badge bg-success">شهري</span>
                        {% elif archive.archive_type == 'custom' %}
                            <span class="badge bg-info">مخصص</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>المنشئ:</strong> {{ archive.creator.name }}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الإنشاء:</strong> {{ archive.created_at.strftime('%Y-%m-%d') }}
                    </div>
                </div>
                {% if archive.description %}
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>الوصف:</strong> {{ archive.description }}
                    </div>
                </div>
                {% endif %}
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>الفترة:</strong>
                        {% if archive.archive_type == 'yearly' and archive.year %}
                            {{ archive.year }}
                        {% elif archive.archive_type == 'monthly' and archive.year and archive.month %}
                            {{ archive.month }}/{{ archive.year }}
                        {% elif archive.archive_type == 'custom' and archive.start_date and archive.end_date %}
                            {{ archive.start_date.strftime('%Y-%m-%d') }} - {{ archive.end_date.strftime('%Y-%m-%d') }}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ archive.get_messages_count() }}</h4>
                        <small class="text-muted">رسالة مؤرشفة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ "%.1f"|format(archive.get_size_mb()) }} MB</h4>
                        <small class="text-muted">حجم الأرشيف</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Archived Messages -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل المؤرشفة ({{ messages.total }})
                </h5>
                <div>
                    <input type="text" class="form-control form-control-sm" id="searchMessages" 
                           placeholder="البحث في الرسائل..." style="width: 200px;">
                </div>
            </div>
            <div class="card-body">
                {% if messages.items %}
                <div class="table-responsive">
                    <table class="table table-hover" id="messagesTable">
                        <thead>
                            <tr>
                                <th>رقم الرسالة</th>
                                <th>الموضوع</th>
                                <th>المصدر</th>
                                <th>الوجهة</th>
                                <th>تاريخ الرسالة</th>
                                <th>المنشئ</th>
                                <th>تاريخ الأرشفة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in messages.items %}
                            <tr>
                                <td>
                                    <strong>{{ message.message_number or 'غير محدد' }}</strong>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="{{ message.subject }}">
                                        {{ message.subject }}
                                    </div>
                                </td>
                                <td>{{ message.source }}</td>
                                <td>{{ message.destination }}</td>
                                <td>
                                    {% if message.message_date %}
                                        {{ message.message_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </td>
                                <td>{{ message.creator_name or 'غير محدد' }}</td>
                                <td>{{ message.archived_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#viewMessageModal{{ message.id }}"
                                                title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if message.attachment_name %}
                                        <button type="button" class="btn btn-outline-success" 
                                                title="مرفق">
                                            <i class="fas fa-paperclip"></i>
                                        </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#removeMessageModal{{ message.id }}"
                                                title="إزالة من الأرشيف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if messages.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if messages.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('archive.view_archive', archive_id=archive.id, page=messages.prev_num) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in messages.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != messages.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('archive.view_archive', archive_id=archive.id, page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if messages.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('archive.view_archive', archive_id=archive.id, page=messages.next_num) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد رسائل مؤرشفة</h5>
                    <p class="text-muted">ابدأ بإضافة رسائل إلى هذا الأرشيف</p>
                    <a href="{{ url_for('archive.add_messages', archive_id=archive.id) }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة رسائل
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- View Message Modals -->
{% for message in messages.items %}
<div class="modal fade" id="viewMessageModal{{ message.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الرسالة: {{ message.message_number or 'غير محدد' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم الرسالة:</strong> {{ message.message_number or 'غير محدد' }}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الرسالة:</strong> 
                        {% if message.message_date %}
                            {{ message.message_date.strftime('%Y-%m-%d') }}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>المصدر:</strong> {{ message.source }}
                    </div>
                    <div class="col-md-6">
                        <strong>الوجهة:</strong> {{ message.destination }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>الموضوع:</strong>
                        <p>{{ message.subject }}</p>
                    </div>
                </div>
                {% if message.content %}
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>المحتوى:</strong>
                        <div class="border rounded p-3 bg-light">
                            {{ message.content|nl2br }}
                        </div>
                    </div>
                </div>
                {% endif %}
                {% if message.attachment_name %}
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>المرفق:</strong>
                        <p>{{ message.attachment_name }}</p>
                    </div>
                </div>
                {% endif %}
                <div class="row">
                    <div class="col-md-6">
                        <strong>المنشئ:</strong> {{ message.creator_name or 'غير محدد' }}
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الأرشفة:</strong> {{ message.archived_at.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Remove Message Modals -->
<div class="modal fade" id="removeMessageModal{{ message.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إزالة من الأرشيف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إزالة الرسالة <strong>{{ message.message_number or message.subject[:50] }}</strong> من الأرشيف؟</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ستتم إزالة الرسالة من الأرشيف فقط، الرسالة الأصلية ستبقى في النظام.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('archive.remove_message', message_id=message.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-warning">إزالة من الأرشيف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchMessages');
    const table = document.getElementById('messagesTable');
    
    if (searchInput && table) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });
    }
});
</script>
{% endblock %}
