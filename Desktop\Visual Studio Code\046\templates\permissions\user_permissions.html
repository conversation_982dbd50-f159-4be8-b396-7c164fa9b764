{% extends "base.html" %}

{% block title %}صلاحيات المستخدم - {{ user.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user-shield me-2"></i>
                    صلاحيات المستخدم: {{ user.name }}
                </h2>
                <div>
                    <a href="{{ url_for('users.list_users') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للمستخدمين
                    </a>
                </div>
            </div>

            <!-- معلومات المستخدم -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>معلومات المستخدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>الاسم:</strong> {{ user.name }}
                        </div>
                        <div class="col-md-3">
                            <strong>اسم المستخدم:</strong> {{ user.username }}
                        </div>
                        <div class="col-md-3">
                            <strong>الدور الأساسي:</strong> 
                            <span class="badge bg-primary">{{ user.role.value }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>القسم:</strong> 
                            {{ user.department.name if user.department else 'غير محدد' }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج تعديل الصلاحيات -->
            <form method="POST">
                <div class="row">
                    <!-- الدور المخصص -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-tag me-2"></i>الدور المخصص
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="role_id" class="form-label">اختيار دور مخصص (اختياري)</label>
                                    <select class="form-select" id="role_id" name="role_id">
                                        <option value="">بدون دور مخصص</option>
                                        {% for role in roles %}
                                        <option value="{{ role.id }}" 
                                                {% if user.custom_role and user.custom_role.id == role.id %}selected{% endif %}>
                                            {{ role.display_name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">
                                        الدور المخصص يضيف صلاحيات إضافية للمستخدم بجانب صلاحيات الدور الأساسي
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الصلاحيات الإضافية -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-plus-circle me-2"></i>صلاحيات إضافية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-text mb-3">
                                    اختر صلاحيات إضافية للمستخدم (بجانب صلاحيات الدور الأساسي والمخصص)
                                </div>
                                <div class="row">
                                    {% for permission in permissions %}
                                    <div class="col-12 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="perm_{{ permission.id }}" 
                                                   name="additional_permissions" 
                                                   value="{{ permission.id }}"
                                                   {% if permission in user.additional_permissions %}checked{% endif %}>
                                            <label class="form-check-label" for="perm_{{ permission.id }}">
                                                <strong>{{ permission.display_name }}</strong>
                                                <small class="text-muted d-block">{{ permission.description }}</small>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                    </div>
                </div>
            </form>

            <!-- عرض جميع صلاحيات المستخدم الحالية -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>جميع صلاحيات المستخدم الحالية
                    </h5>
                </div>
                <div class="card-body">
                    {% if user_permissions %}
                    <div class="row">
                        {% for permission in user_permissions %}
                        <div class="col-md-4 mb-2">
                            <span class="badge bg-primary">{{ permission }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">لا توجد صلاحيات محددة للمستخدم</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.form-check {
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.form-check:hover {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}
</style>
{% endblock %}
