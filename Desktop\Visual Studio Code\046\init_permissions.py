#!/usr/bin/env python3
"""
Script to initialize permissions and roles in the database
"""

from app import create_app
from models import db, Permission, Role, User, UserRole

def init_permissions():
    """Initialize default permissions"""
    app = create_app()
    
    with app.app_context():
        # Create tables if they don't exist
        db.create_all()
        
        # Default permissions
        default_permissions = [
            # صلاحيات الرسائل
            ('create_message', 'إنشاء رسالة', 'إمكانية إنشاء رسائل جديدة', 'الرسائل'),
            ('view_message', 'عرض الرسائل', 'إمكانية عرض الرسائل', 'الرسائل'),
            ('edit_message', 'تعديل الرسائل', 'إمكانية تعديل الرسائل', 'الرسائل'),
            ('delete_message', 'حذف الرسائل', 'إمكانية حذف الرسائل', 'الرسائل'),
            ('approve_message', 'اعتماد الرسائل', 'إمكانية اعتماد الرسائل', 'الرسائل'),
            ('archive_message', 'أرشفة الرسائل', 'إمكانية أرشفة الرسائل', 'الرسائل'),
            
            # صلاحيات المستخدمين
            ('create_user', 'إنشاء مستخدم', 'إمكانية إنشاء مستخدمين جدد', 'المستخدمين'),
            ('view_user', 'عرض المستخدمين', 'إمكانية عرض بيانات المستخدمين', 'المستخدمين'),
            ('edit_user', 'تعديل المستخدمين', 'إمكانية تعديل بيانات المستخدمين', 'المستخدمين'),
            ('delete_user', 'حذف المستخدمين', 'إمكانية حذف المستخدمين', 'المستخدمين'),
            ('manage_roles', 'إدارة الأدوار', 'إمكانية إدارة أدوار المستخدمين', 'المستخدمين'),
            
            # صلاحيات الأقسام
            ('create_department', 'إنشاء قسم', 'إمكانية إنشاء أقسام جديدة', 'الأقسام'),
            ('view_department', 'عرض الأقسام', 'إمكانية عرض الأقسام', 'الأقسام'),
            ('edit_department', 'تعديل الأقسام', 'إمكانية تعديل الأقسام', 'الأقسام'),
            ('delete_department', 'حذف الأقسام', 'إمكانية حذف الأقسام', 'الأقسام'),
            
            # صلاحيات النظام
            ('view_reports', 'عرض التقارير', 'إمكانية عرض التقارير', 'النظام'),
            ('manage_system', 'إدارة النظام', 'إمكانية إدارة إعدادات النظام', 'النظام'),
            ('view_logs', 'عرض السجلات', 'إمكانية عرض سجلات النظام', 'النظام'),
            ('backup_system', 'نسخ احتياطي', 'إمكانية إنشاء نسخ احتياطية', 'النظام'),
        ]
        
        print("Creating permissions...")
        permissions_created = 0
        
        for name, display_name, description, category in default_permissions:
            if not Permission.query.filter_by(name=name).first():
                permission = Permission(
                    name=name,
                    display_name=display_name,
                    description=description,
                    category=category
                )
                db.session.add(permission)
                permissions_created += 1
                print(f"  ✓ Created permission: {display_name}")
        
        # Default roles
        default_roles = [
            ('admin_role', 'مدير النظام', 'دور مدير النظام مع جميع الصلاحيات', True, [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user', 'delete_user', 'manage_roles',
                'create_department', 'view_department', 'edit_department', 'delete_department',
                'view_reports', 'manage_system', 'view_logs', 'backup_system'
            ]),
            ('manager_role', 'مدير', 'دور المدير مع صلاحيات إدارية', True, [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user',
                'view_department', 'edit_department',
                'view_reports'
            ]),
            ('secretary_role', 'سكرتير', 'دور السكرتير مع صلاحيات محدودة', True, [
                'create_message', 'view_message', 'edit_message', 'archive_message',
                'view_user', 'edit_user',
                'view_department'
            ]),
            ('employee_role', 'موظف', 'دور الموظف العادي', True, [
                'create_message', 'view_message', 'edit_message',
                'view_user', 'view_department'
            ])
        ]
        
        print("\nCreating roles...")
        roles_created = 0
        
        for name, display_name, description, is_system, permission_names in default_roles:
            if not Role.query.filter_by(name=name).first():
                role = Role(
                    name=name,
                    display_name=display_name,
                    description=description,
                    is_system_role=is_system
                )
                
                # Add permissions to role
                for permission_name in permission_names:
                    permission = Permission.query.filter_by(name=permission_name).first()
                    if permission:
                        role.add_permission(permission)
                
                db.session.add(role)
                roles_created += 1
                print(f"  ✓ Created role: {display_name} with {len(permission_names)} permissions")
        
        # Commit changes
        try:
            db.session.commit()
            print(f"\n✅ Successfully initialized:")
            print(f"   - {permissions_created} permissions")
            print(f"   - {roles_created} roles")
            
            # Update existing admin users
            admin_users = User.query.filter_by(role=UserRole.ADMIN).all()
            if admin_users:
                admin_role = Role.query.filter_by(name='admin_role').first()
                if admin_role:
                    for user in admin_users:
                        user.role_id = admin_role.id
                    db.session.commit()
                    print(f"   - Updated {len(admin_users)} admin users with new role")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error: {str(e)}")
            return False
        
        return True

if __name__ == '__main__':
    print("🚀 Initializing permissions and roles...")
    if init_permissions():
        print("🎉 Initialization completed successfully!")
    else:
        print("💥 Initialization failed!")
