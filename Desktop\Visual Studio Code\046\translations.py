"""
نظام الترجمة متعدد اللغات
Multi-language Translation System
"""

from flask import session, request

class TranslationManager:
    def __init__(self):
        self.translations = {
            'ar': {
                # Navigation
                'dashboard': 'لوحة التحكم',
                'messages': 'الرسائل',
                'all_messages': 'جميع الرسائل',
                'new_message': 'رسالة جديدة',
                'incoming_messages': 'الرسائل الواردة',
                'outgoing_messages': 'الرسائل الصادرة',
                'internal_messages': 'المراسلات الداخلية',
                'my_internal_messages': 'مراسلاتي الداخلية',
                'users': 'المستخدمون',
                'all_users': 'جميع المستخدمين',
                'new_user': 'مستخدم جديد',
                'departments': 'الأقسام',
                'all_departments': 'جميع الأقسام',
                'new_department': 'قسم جديد',
                'settings': 'الإعدادات',
                'logout': 'تسجيل الخروج',
                'login': 'تسجيل الدخول',
                
                # Common
                'save': 'حفظ',
                'cancel': 'إلغاء',
                'edit': 'تعديل',
                'delete': 'حذف',
                'view': 'عرض',
                'search': 'بحث',
                'filter': 'فلترة',
                'export': 'تصدير',
                'print': 'طباعة',
                'download': 'تحميل',
                'upload': 'رفع',
                'back': 'العودة',
                'next': 'التالي',
                'previous': 'السابق',
                'submit': 'إرسال',
                'confirm': 'تأكيد',
                'yes': 'نعم',
                'no': 'لا',
                'ok': 'موافق',
                'close': 'إغلاق',
                'loading': 'جاري التحميل...',
                'success': 'نجح',
                'error': 'خطأ',
                'warning': 'تحذير',
                'info': 'معلومات',
                
                # Messages
                'message_type': 'نوع الرسالة',
                'incoming': 'واردة',
                'outgoing': 'صادرة',
                'internal': 'داخلية',
                'registration_number': 'رقم التسجيل',
                'subject': 'الموضوع',
                'content': 'المحتوى',
                'destination': 'الجهة',
                'message_date': 'تاريخ الرسالة',
                'department': 'القسم',
                'priority': 'الأولوية',
                'normal': 'عادي',
                'high': 'مهم',
                'urgent': 'عاجل',
                'status': 'الحالة',
                'active': 'نشط',
                'archived': 'مؤرشف',
                'attachment': 'مرفق',
                'from_department': 'من القسم',
                'to_department': 'إلى القسم',
                'assigned_to': 'مكلف بالرد',
                'due_date': 'تاريخ الاستحقاق',
                'requires_response': 'يتطلب رد',
                'is_urgent': 'عاجل',
                'response': 'الرد',
                'respond': 'رد',
                'approve': 'موافقة',
                'reject': 'رفض',
                'pending': 'معلق',
                'approved': 'موافق عليه',
                'rejected': 'مرفوض',
                
                # Users
                'name': 'الاسم',
                'username': 'اسم المستخدم',
                'password': 'كلمة المرور',
                'role': 'الدور',
                'manager': 'مدير',
                'secretary': 'سكرتير',
                'employee': 'موظف',
                'created_at': 'تاريخ الإنشاء',
                'last_login': 'آخر دخول',
                'is_active': 'نشط',
                
                # Departments
                'department_name': 'اسم القسم',
                'department_description': 'وصف القسم',
                'department_manager': 'مدير القسم',
                'employees': 'الموظفون',
                'manage_employees': 'إدارة الموظفين',
                
                # Statistics
                'total': 'الإجمالي',
                'total_messages': 'إجمالي الرسائل',
                'total_users': 'إجمالي المستخدمين',
                'total_departments': 'إجمالي الأقسام',
                'active_users': 'المستخدمون النشطون',
                'active_departments': 'الأقسام النشطة',
                'pending_responses': 'بانتظار الرد',
                'urgent_messages': 'الرسائل العاجلة',
                'overdue_messages': 'الرسائل المتأخرة',
                
                # Approval System
                'approval_section': 'قسم الاعتماد والموافقة',
                'approval_status': 'حالة الاعتماد',
                'approved_by': 'اعتمد من قبل',
                'approval_date': 'تاريخ الاعتماد',
                'approval_notes': 'ملاحظات الاعتماد',
                'requires_approval': 'يتطلب اعتماد',
                'approval_workflow': 'سير عمل الاعتماد',
                'approve_message': 'اعتماد الرسالة',
                'reject_message': 'رفض الرسالة',
                'approval_history': 'تاريخ الاعتمادات',
                'awaiting_approval': 'في انتظار الاعتماد',
                'approval_required': 'مطلوب اعتماد',
                'auto_approved': 'معتمد تلقائياً',
                'manual_approval': 'اعتماد يدوي',
                
                # Language
                'language': 'اللغة',
                'arabic': 'العربية',
                'english': 'الإنجليزية',
                'change_language': 'تغيير اللغة',
                'language_changed': 'تم تغيير اللغة بنجاح',
                'actions': 'الإجراءات',
                'electronic_correspondence_system': 'نظام المراسلات الإلكترونية',
            },
            'en': {
                # Navigation
                'dashboard': 'Dashboard',
                'messages': 'Messages',
                'all_messages': 'All Messages',
                'new_message': 'New Message',
                'incoming_messages': 'Incoming Messages',
                'outgoing_messages': 'Outgoing Messages',
                'internal_messages': 'Internal Messages',
                'my_internal_messages': 'My Internal Messages',
                'users': 'Users',
                'all_users': 'All Users',
                'new_user': 'New User',
                'departments': 'Departments',
                'all_departments': 'All Departments',
                'new_department': 'New Department',
                'settings': 'Settings',
                'logout': 'Logout',
                'login': 'Login',
                
                # Common
                'save': 'Save',
                'cancel': 'Cancel',
                'edit': 'Edit',
                'delete': 'Delete',
                'view': 'View',
                'search': 'Search',
                'filter': 'Filter',
                'export': 'Export',
                'print': 'Print',
                'download': 'Download',
                'upload': 'Upload',
                'back': 'Back',
                'next': 'Next',
                'previous': 'Previous',
                'submit': 'Submit',
                'confirm': 'Confirm',
                'yes': 'Yes',
                'no': 'No',
                'ok': 'OK',
                'close': 'Close',
                'loading': 'Loading...',
                'success': 'Success',
                'error': 'Error',
                'warning': 'Warning',
                'info': 'Information',
                
                # Messages
                'message_type': 'Message Type',
                'incoming': 'Incoming',
                'outgoing': 'Outgoing',
                'internal': 'Internal',
                'registration_number': 'Registration Number',
                'subject': 'Subject',
                'content': 'Content',
                'destination': 'Destination',
                'message_date': 'Message Date',
                'department': 'Department',
                'priority': 'Priority',
                'normal': 'Normal',
                'high': 'High',
                'urgent': 'Urgent',
                'status': 'Status',
                'active': 'Active',
                'archived': 'Archived',
                'attachment': 'Attachment',
                'from_department': 'From Department',
                'to_department': 'To Department',
                'assigned_to': 'Assigned To',
                'due_date': 'Due Date',
                'requires_response': 'Requires Response',
                'is_urgent': 'Urgent',
                'response': 'Response',
                'respond': 'Respond',
                'approve': 'Approve',
                'reject': 'Reject',
                'pending': 'Pending',
                'approved': 'Approved',
                'rejected': 'Rejected',
                
                # Users
                'name': 'Name',
                'username': 'Username',
                'password': 'Password',
                'role': 'Role',
                'manager': 'Manager',
                'secretary': 'Secretary',
                'employee': 'Employee',
                'created_at': 'Created At',
                'last_login': 'Last Login',
                'is_active': 'Active',
                
                # Departments
                'department_name': 'Department Name',
                'department_description': 'Department Description',
                'department_manager': 'Department Manager',
                'employees': 'Employees',
                'manage_employees': 'Manage Employees',
                
                # Statistics
                'total': 'Total',
                'total_messages': 'Total Messages',
                'total_users': 'Total Users',
                'total_departments': 'Total Departments',
                'active_users': 'Active Users',
                'active_departments': 'Active Departments',
                'pending_responses': 'Pending Responses',
                'urgent_messages': 'Urgent Messages',
                'overdue_messages': 'Overdue Messages',
                
                # Approval System
                'approval_section': 'Approval Section',
                'approval_status': 'Approval Status',
                'approved_by': 'Approved By',
                'approval_date': 'Approval Date',
                'approval_notes': 'Approval Notes',
                'requires_approval': 'Requires Approval',
                'approval_workflow': 'Approval Workflow',
                'approve_message': 'Approve Message',
                'reject_message': 'Reject Message',
                'approval_history': 'Approval History',
                'awaiting_approval': 'Awaiting Approval',
                'approval_required': 'Approval Required',
                'auto_approved': 'Auto Approved',
                'manual_approval': 'Manual Approval',
                
                # Language
                'language': 'Language',
                'arabic': 'Arabic',
                'english': 'English',
                'change_language': 'Change Language',
                'language_changed': 'Language changed successfully',
                'actions': 'Actions',
                'electronic_correspondence_system': 'Electronic Correspondence System',
            }
        }
    
    def get_current_language(self):
        """Get current language from session or default to Arabic"""
        return session.get('language', 'ar')
    
    def set_language(self, language):
        """Set current language in session"""
        if language in self.translations:
            session['language'] = language
            return True
        return False
    
    def translate(self, key, language=None):
        """Get translation for a key"""
        if language is None:
            language = self.get_current_language()
        
        return self.translations.get(language, {}).get(key, key)
    
    def get_direction(self, language=None):
        """Get text direction for language"""
        if language is None:
            language = self.get_current_language()
        
        return 'rtl' if language == 'ar' else 'ltr'
    
    def get_language_name(self, language=None):
        """Get language display name"""
        if language is None:
            language = self.get_current_language()
        
        names = {
            'ar': 'العربية',
            'en': 'English'
        }
        return names.get(language, language)

# Global translation manager instance
translation_manager = TranslationManager()

def get_translation(key, language=None):
    """Helper function to get translation"""
    return translation_manager.translate(key, language)

def get_current_language():
    """Helper function to get current language"""
    return translation_manager.get_current_language()

def get_text_direction():
    """Helper function to get text direction"""
    return translation_manager.get_direction()
