#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صلاحيات المدير
"""

import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, UserRole, Permission, Role

def test_admin_permissions():
    """اختبار صلاحيات المدير"""
    
    app = create_app()
    with app.app_context():
        print("🔍 اختبار صلاحيات المدير...")
        print("=" * 50)
        
        # البحث عن المدير
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            print("❌ لم يتم العثور على المدير")
            return
        
        print(f"👤 المستخدم: {admin.name}")
        print(f"🔑 اسم المستخدم: {admin.username}")
        print(f"👑 الدور: {admin.role.value}")
        print(f"✅ نشط: {'نعم' if admin.is_active else 'لا'}")
        print()
        
        # اختبار الدوال الأساسية
        print("🧪 اختبار الدوال الأساسية:")
        print(f"   is_admin(): {admin.is_admin()}")
        print(f"   is_manager(): {admin.is_manager()}")
        print(f"   is_secretary(): {admin.is_secretary()}")
        print(f"   can_manage_users(): {admin.can_manage_users()}")
        print(f"   can_delete_messages(): {admin.can_delete_messages()}")
        print()
        
        # اختبار الصلاحيات المحددة
        print("🔐 اختبار الصلاحيات المحددة:")
        test_permissions = [
            'create_message', 'view_message', 'edit_message', 'delete_message',
            'approve_message', 'archive_message',
            'create_user', 'view_user', 'edit_user', 'delete_user', 'manage_roles',
            'create_department', 'view_department', 'edit_department', 'delete_department',
            'view_reports', 'manage_system', 'view_logs', 'backup_system'
        ]
        
        for permission in test_permissions:
            has_perm = admin.has_permission(permission)
            status = "✅" if has_perm else "❌"
            print(f"   {status} {permission}: {has_perm}")
        
        print()
        
        # عرض جميع صلاحيات المدير
        all_permissions = admin.get_all_permissions()
        print(f"📋 إجمالي الصلاحيات: {len(all_permissions)}")
        print("   الصلاحيات:")
        for i, perm in enumerate(sorted(all_permissions), 1):
            print(f"   {i:2d}. {perm}")
        
        print()
        
        # اختبار الصلاحيات الإضافية والأدوار المخصصة
        print("🎭 الأدوار والصلاحيات الإضافية:")
        print(f"   الدور المخصص: {admin.custom_role.display_name if admin.custom_role else 'لا يوجد'}")
        print(f"   الصلاحيات الإضافية: {len(admin.additional_permissions)}")
        if admin.additional_permissions:
            for perm in admin.additional_permissions:
                print(f"     - {perm.display_name}")
        
        print()
        
        # إحصائيات النظام
        print("📊 إحصائيات النظام:")
        total_users = User.query.count()
        total_permissions = Permission.query.count()
        total_roles = Role.query.count()
        
        print(f"   إجمالي المستخدمين: {total_users}")
        print(f"   إجمالي الصلاحيات: {total_permissions}")
        print(f"   إجمالي الأدوار: {total_roles}")
        
        print()
        print("✅ تم الانتهاء من اختبار صلاحيات المدير")

if __name__ == '__main__':
    test_admin_permissions()
