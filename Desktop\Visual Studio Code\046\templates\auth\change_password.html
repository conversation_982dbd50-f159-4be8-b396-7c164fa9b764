{% extends "base.html" %}

{% block title %}تغيير كلمة المرور - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-key me-2"></i>
                تغيير كلمة المرور
            </h1>
            <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للملف الشخصي
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    تحديث كلمة المرور
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تعليمات الأمان:</strong>
                    <ul class="mb-0 mt-2">
                        <li>يجب أن تكون كلمة المرور 6 أحرف على الأقل</li>
                        <li>استخدم مزيجاً من الأحرف والأرقام</li>
                        <li>تجنب استخدام معلومات شخصية واضحة</li>
                        <li>لا تشارك كلمة المرور مع أي شخص آخر</li>
                    </ul>
                </div>
                
                <form method="POST" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password_icon"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            يرجى إدخال كلمة المرور الحالية
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-key"></i>
                            </span>
                            <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password_icon"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            كلمة المرور يجب أن تكون 6 أحرف على الأقل
                        </div>
                        <div class="form-text">
                            <div id="password_strength" class="mt-2"></div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-check"></i>
                            </span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password_icon"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            كلمات المرور غير متطابقة
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            تحديث كلمة المرور
                        </button>
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Security Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح الأمان
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">
                            <i class="fas fa-check me-1"></i>
                            افعل
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check-circle text-success me-2"></i>استخدم كلمة مرور قوية</li>
                            <li><i class="fas fa-check-circle text-success me-2"></i>غيّر كلمة المرور بانتظام</li>
                            <li><i class="fas fa-check-circle text-success me-2"></i>استخدم أحرف وأرقام ورموز</li>
                            <li><i class="fas fa-check-circle text-success me-2"></i>سجّل الخروج بعد الانتهاء</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">
                            <i class="fas fa-times me-1"></i>
                            لا تفعل
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-times-circle text-danger me-2"></i>لا تشارك كلمة المرور</li>
                            <li><i class="fas fa-times-circle text-danger me-2"></i>لا تستخدم معلومات شخصية</li>
                            <li><i class="fas fa-times-circle text-danger me-2"></i>لا تكتبها في مكان ظاهر</li>
                            <li><i class="fas fa-times-circle text-danger me-2"></i>لا تستخدم نفس كلمة المرور</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    if (password.length >= 6) strength++;
    else feedback.push('على الأقل 6 أحرف');
    
    if (/[a-z]/.test(password)) strength++;
    else feedback.push('أحرف صغيرة');
    
    if (/[A-Z]/.test(password)) strength++;
    else feedback.push('أحرف كبيرة');
    
    if (/[0-9]/.test(password)) strength++;
    else feedback.push('أرقام');
    
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    else feedback.push('رموز خاصة');
    
    const strengthDiv = document.getElementById('password_strength');
    let strengthText = '';
    let strengthClass = '';
    
    if (strength < 2) {
        strengthText = 'ضعيفة';
        strengthClass = 'text-danger';
    } else if (strength < 4) {
        strengthText = 'متوسطة';
        strengthClass = 'text-warning';
    } else {
        strengthText = 'قوية';
        strengthClass = 'text-success';
    }
    
    strengthDiv.innerHTML = `
        <small class="${strengthClass}">
            <strong>قوة كلمة المرور: ${strengthText}</strong>
            ${feedback.length > 0 ? '<br>يُنصح بإضافة: ' + feedback.join(', ') : ''}
        </small>
    `;
}

document.addEventListener('DOMContentLoaded', function() {
    const newPasswordField = document.getElementById('new_password');
    const confirmPasswordField = document.getElementById('confirm_password');
    
    newPasswordField.addEventListener('input', function() {
        checkPasswordStrength(this.value);
        validatePasswords();
    });
    
    confirmPasswordField.addEventListener('input', validatePasswords);
    
    function validatePasswords() {
        if (newPasswordField.value !== confirmPasswordField.value) {
            confirmPasswordField.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            confirmPasswordField.setCustomValidity('');
        }
    }
});
</script>
{% endblock %}
