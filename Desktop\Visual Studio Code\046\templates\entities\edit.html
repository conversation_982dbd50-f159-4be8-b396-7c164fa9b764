{% extends "base.html" %}

{% block title %}تعديل {{ entity.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل {{ entity.name }}
                </h2>
                <div>
                    <a href="{{ url_for('entities.view_entity', id=entity.id) }}" class="btn btn-outline-info">
                        <i class="fas fa-eye me-1"></i>عرض
                    </a>
                    <a href="{{ url_for('entities.list_entities') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <!-- المعلومات الأساسية -->
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">اسم الجهة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="{{ entity.name }}" required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال اسم الجهة
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="name_en" class="form-label">الاسم بالإنجليزية</label>
                                        <input type="text" class="form-control" id="name_en" name="name_en" 
                                               value="{{ entity.name_en or '' }}">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="entity_type" class="form-label">نوع الجهة <span class="text-danger">*</span></label>
                                        <select class="form-select" id="entity_type" name="entity_type" required>
                                            <option value="">اختر نوع الجهة</option>
                                            <option value="government" {% if entity.entity_type == 'government' %}selected{% endif %}>جهة حكومية</option>
                                            <option value="private" {% if entity.entity_type == 'private' %}selected{% endif %}>جهة خاصة</option>
                                            <option value="individual" {% if entity.entity_type == 'individual' %}selected{% endif %}>فرد</option>
                                            <option value="ngo" {% if entity.entity_type == 'ngo' %}selected{% endif %}>منظمة غير ربحية</option>
                                            <option value="international" {% if entity.entity_type == 'international' %}selected{% endif %}>جهة دولية</option>
                                            <option value="academic" {% if entity.entity_type == 'academic' %}selected{% endif %}>جهة أكاديمية</option>
                                            <option value="other" {% if entity.entity_type == 'other' %}selected{% endif %}>أخرى</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            يرجى اختيار نوع الجهة
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="category" class="form-label">فئة الجهة</label>
                                        <select class="form-select" id="category" name="category">
                                            <option value="">اختر فئة الجهة</option>
                                            <option value="ministry" {% if entity.category == 'ministry' %}selected{% endif %}>وزارة</option>
                                            <option value="authority" {% if entity.category == 'authority' %}selected{% endif %}>هيئة</option>
                                            <option value="company" {% if entity.category == 'company' %}selected{% endif %}>شركة</option>
                                            <option value="university" {% if entity.category == 'university' %}selected{% endif %}>جامعة</option>
                                            <option value="hospital" {% if entity.category == 'hospital' %}selected{% endif %}>مستشفى</option>
                                            <option value="bank" {% if entity.category == 'bank' %}selected{% endif %}>بنك</option>
                                            <option value="embassy" {% if entity.category == 'embassy' %}selected{% endif %}>سفارة</option>
                                            <option value="court" {% if entity.category == 'court' %}selected{% endif %}>محكمة</option>
                                            <option value="municipality" {% if entity.category == 'municipality' %}selected{% endif %}>بلدية</option>
                                            <option value="other" {% if entity.category == 'other' %}selected{% endif %}>أخرى</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="reference_number" class="form-label">الرقم المرجعي</label>
                                        <input type="text" class="form-control" id="reference_number" name="reference_number" 
                                               value="{{ entity.reference_number or '' }}">
                                        <div class="form-text">رقم مرجعي فريد للجهة (اختياري)</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="3">{{ entity.address or '' }}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ entity.notes or '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-phone me-2"></i>معلومات الاتصال
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="{{ entity.phone or '' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="fax" class="form-label">الفاكس</label>
                                        <input type="tel" class="form-control" id="fax" name="fax" 
                                               value="{{ entity.fax or '' }}">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="{{ entity.email or '' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="website" class="form-label">الموقع الإلكتروني</label>
                                        <input type="url" class="form-control" id="website" name="website" 
                                               value="{{ entity.website or '' }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الشخص المسؤول -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user me-2"></i>الشخص المسؤول
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_person" class="form-label">اسم الشخص المسؤول</label>
                                        <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                               value="{{ entity.contact_person or '' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_title" class="form-label">المنصب</label>
                                        <input type="text" class="form-control" id="contact_title" name="contact_title" 
                                               value="{{ entity.contact_title or '' }}">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_phone" class="form-label">هاتف الشخص المسؤول</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                                               value="{{ entity.contact_phone or '' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_email" class="form-label">بريد الشخص المسؤول</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                               value="{{ entity.contact_email or '' }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإعدادات -->
                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>الإعدادات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if entity.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="fas fa-check-circle me-1"></i>نشط
                                    </label>
                                    <div class="form-text">تفعيل أو إلغاء تفعيل الجهة</div>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_government" name="is_government" 
                                           {% if entity.is_government %}checked{% endif %}>
                                    <label class="form-check-label" for="is_government">
                                        <i class="fas fa-university me-1"></i>جهة حكومية
                                    </label>
                                    <div class="form-text">تحديد الجهة كجهة حكومية رسمية</div>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_frequent" name="is_frequent" 
                                           {% if entity.is_frequent %}checked{% endif %}>
                                    <label class="form-check-label" for="is_frequent">
                                        <i class="fas fa-star me-1"></i>متكررة التراسل
                                    </label>
                                    <div class="form-text">جهة يتم التراسل معها بشكل متكرر</div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات التتبع -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>معلومات التتبع
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label text-muted">أنشئت بواسطة</label>
                                    <p class="mb-1">{{ entity.creator.name }}</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-muted">تاريخ الإنشاء</label>
                                    <p class="mb-1">{{ entity.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-muted">آخر تحديث</label>
                                    <p class="mb-1">{{ entity.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>حفظ التغييرات
                                    </button>
                                    <a href="{{ url_for('entities.view_entity', id=entity.id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}

{% block extra_css %}
<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.text-danger {
    color: #dc3545 !important;
}

.form-check-label {
    font-weight: 500;
}

.btn {
    border-radius: 6px;
}
</style>
{% endblock %}
