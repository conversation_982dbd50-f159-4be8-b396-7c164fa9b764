#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل نظام المراسلات الإلكترونية
مع دعم عرض جميع أنواع الملفات
"""

print("🏢 نظام المراسلات الإلكترونية")
print("📧 Electronic Correspondence System")
print("=" * 50)

try:
    print("🔧 تحميل النظام...")
    from app import create_app
    
    print("✅ تم تحميل النظام بنجاح")
    
    app = create_app()
    
    print("=" * 50)
    print("🎉 النظام جاهز للاستخدام!")
    print("")
    print("🌐 الرابط: http://127.0.0.1:8585")
    print("👤 اسم المستخدم: admin")
    print("🔐 كلمة المرور: admin123")
    print("")
    print("📄 أنواع الملفات المدعومة:")
    print("  • ملفات Word (DOCX) - عرض متقدم")
    print("  • ملفات PDF - عرض مباشر")
    print("  • الصور (JPG, PNG, GIF) - معاينة")
    print("  • ملفات الصوت والفيديو - تشغيل")
    print("  • ملفات JSON, XML, CSV - عرض منسق")
    print("  • جميع الأنواع الأخرى - تحميل")
    print("")
    print("🛑 لإيقاف النظام: اضغط Ctrl+C")
    print("=" * 50)
    
    # تشغيل الخادم
    app.run(
        host='127.0.0.1',
        port=8585,
        debug=False,
        use_reloader=False
    )
    
except KeyboardInterrupt:
    print("\n🛑 تم إيقاف النظام بنجاح")
    print("👋 شكراً لاستخدام النظام")
    
except ImportError as e:
    print(f"❌ خطأ في تحميل المكتبات: {e}")
    print("\n🔧 لحل المشكلة:")
    print("pip install flask flask-sqlalchemy flask-login")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل النظام: {e}")
    print("\n🔧 تحقق من:")
    print("  • وجود جميع ملفات النظام")
    print("  • صحة قاعدة البيانات")
    print("  • عدم حجز المنفذ 8585")

input("\n⏸️ اضغط Enter للخروج...")
