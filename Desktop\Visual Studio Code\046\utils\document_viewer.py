#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
عارض المستندات المتقدم
Document Viewer for all file types
"""

import os
import io
import base64
import zipfile
import json
from pathlib import Path
from flask import current_app, render_template_string
from docx import Document
from docx.shared import Inches
import PyPDF2
from PIL import Image
import mimetypes

class DocumentViewer:
    """فئة عرض المستندات"""
    
    def __init__(self):
        self.supported_formats = {
            # Text documents
            'docx': self.view_docx,
            'doc': self.view_doc_fallback,
            'txt': self.view_text,
            'rtf': self.view_rtf,
            
            # PDFs
            'pdf': self.view_pdf,
            
            # Images
            'jpg': self.view_image,
            'jpeg': self.view_image,
            'png': self.view_image,
            'gif': self.view_image,
            'bmp': self.view_image,
            'webp': self.view_image,
            'svg': self.view_svg,
            
            # Spreadsheets
            'xlsx': self.view_excel,
            'xls': self.view_excel_fallback,
            'csv': self.view_csv,
            
            # Presentations
            'pptx': self.view_powerpoint,
            'ppt': self.view_powerpoint_fallback,
            
            # Code files
            'html': self.view_html,
            'xml': self.view_xml,
            'json': self.view_json,
            'css': self.view_css,
            'js': self.view_javascript,
            
            # Archives
            'zip': self.view_zip,
            'rar': self.view_archive_info,
            
            # Audio/Video
            'mp3': self.view_audio,
            'wav': self.view_audio,
            'mp4': self.view_video,
            'webm': self.view_video,
        }
    
    def can_view(self, filename):
        """التحقق من إمكانية عرض الملف"""
        if not filename or '.' not in filename:
            return False
        
        extension = filename.rsplit('.', 1)[1].lower()
        return extension in self.supported_formats
    
    def view_file(self, file_path, filename):
        """عرض الملف حسب نوعه"""
        if not os.path.exists(file_path):
            return self.error_view("الملف غير موجود")
        
        if not filename or '.' not in filename:
            return self.error_view("نوع الملف غير مدعوم")
        
        extension = filename.rsplit('.', 1)[1].lower()
        
        if extension not in self.supported_formats:
            return self.download_view(filename)
        
        try:
            return self.supported_formats[extension](file_path, filename)
        except Exception as e:
            return self.error_view(f"خطأ في عرض الملف: {str(e)}")
    
    def view_docx(self, file_path, filename):
        """عرض ملفات Word DOCX"""
        try:
            doc = Document(file_path)
            
            # استخراج النص والتنسيق
            content = []
            
            # معلومات المستند
            doc_info = {
                'title': doc.core_properties.title or 'غير محدد',
                'author': doc.core_properties.author or 'غير محدد',
                'created': doc.core_properties.created.strftime('%Y-%m-%d %H:%M:%S') if doc.core_properties.created else 'غير محدد',
                'modified': doc.core_properties.modified.strftime('%Y-%m-%d %H:%M:%S') if doc.core_properties.modified else 'غير محدد',
                'paragraphs_count': len(doc.paragraphs),
                'tables_count': len(doc.tables)
            }
            
            # استخراج الفقرات
            for para in doc.paragraphs:
                if para.text.strip():
                    para_info = {
                        'text': para.text,
                        'style': para.style.name if para.style else 'Normal',
                        'alignment': str(para.alignment) if para.alignment else 'LEFT'
                    }
                    content.append(para_info)
            
            # استخراج الجداول
            tables_data = []
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text.strip())
                    table_data.append(row_data)
                tables_data.append(table_data)
            
            return self.render_docx_template(filename, doc_info, content, tables_data)
            
        except Exception as e:
            return self.error_view(f"خطأ في قراءة ملف Word: {str(e)}")
    
    def view_pdf(self, file_path, filename):
        """عرض ملفات PDF"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # معلومات PDF
                pdf_info = {
                    'pages_count': len(pdf_reader.pages),
                    'title': pdf_reader.metadata.title if pdf_reader.metadata and pdf_reader.metadata.title else 'غير محدد',
                    'author': pdf_reader.metadata.author if pdf_reader.metadata and pdf_reader.metadata.author else 'غير محدد',
                    'creator': pdf_reader.metadata.creator if pdf_reader.metadata and pdf_reader.metadata.creator else 'غير محدد'
                }
                
                # استخراج النص من الصفحات الأولى (أول 5 صفحات)
                pages_text = []
                max_pages = min(5, len(pdf_reader.pages))
                
                for i in range(max_pages):
                    try:
                        page = pdf_reader.pages[i]
                        text = page.extract_text()
                        if text.strip():
                            pages_text.append({
                                'page_number': i + 1,
                                'text': text[:1000] + ('...' if len(text) > 1000 else '')
                            })
                    except:
                        pages_text.append({
                            'page_number': i + 1,
                            'text': 'لا يمكن استخراج النص من هذه الصفحة'
                        })
                
                return self.render_pdf_template(filename, pdf_info, pages_text)
                
        except Exception as e:
            return self.error_view(f"خطأ في قراءة ملف PDF: {str(e)}")
    
    def view_image(self, file_path, filename):
        """عرض الصور"""
        try:
            # تحويل الصورة إلى base64 للعرض
            with open(file_path, 'rb') as img_file:
                img_data = base64.b64encode(img_file.read()).decode()
            
            # معلومات الصورة
            try:
                with Image.open(file_path) as img:
                    img_info = {
                        'width': img.width,
                        'height': img.height,
                        'format': img.format,
                        'mode': img.mode,
                        'size': f"{img.width} × {img.height}"
                    }
            except:
                img_info = {'error': 'لا يمكن قراءة معلومات الصورة'}
            
            # تحديد نوع MIME
            mime_type = mimetypes.guess_type(filename)[0] or 'image/jpeg'
            
            return self.render_image_template(filename, img_info, img_data, mime_type)
            
        except Exception as e:
            return self.error_view(f"خطأ في عرض الصورة: {str(e)}")
    
    def view_text(self, file_path, filename):
        """عرض الملفات النصية"""
        try:
            # محاولة قراءة الملف بترميزات مختلفة
            encodings = ['utf-8', 'utf-16', 'windows-1256', 'iso-8859-1']
            content = None
            used_encoding = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                        used_encoding = encoding
                        break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                return self.error_view("لا يمكن قراءة الملف النصي")
            
            # معلومات الملف
            file_info = {
                'encoding': used_encoding,
                'lines_count': len(content.splitlines()),
                'chars_count': len(content),
                'size': os.path.getsize(file_path)
            }
            
            return self.render_text_template(filename, file_info, content)
            
        except Exception as e:
            return self.error_view(f"خطأ في قراءة الملف النصي: {str(e)}")
    
    def view_csv(self, file_path, filename):
        """عرض ملفات CSV"""
        try:
            import csv
            
            # قراءة CSV
            rows = []
            with open(file_path, 'r', encoding='utf-8-sig') as file:
                csv_reader = csv.reader(file)
                for i, row in enumerate(csv_reader):
                    if i < 100:  # عرض أول 100 صف فقط
                        rows.append(row)
                    else:
                        break
            
            csv_info = {
                'rows_count': len(rows),
                'columns_count': len(rows[0]) if rows else 0,
                'has_header': True if rows else False
            }
            
            return self.render_csv_template(filename, csv_info, rows)
            
        except Exception as e:
            return self.error_view(f"خطأ في قراءة ملف CSV: {str(e)}")
    
    def view_json(self, file_path, filename):
        """عرض ملفات JSON"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            
            # تنسيق JSON للعرض
            formatted_json = json.dumps(data, ensure_ascii=False, indent=2)
            
            json_info = {
                'type': type(data).__name__,
                'size': len(formatted_json),
                'keys_count': len(data) if isinstance(data, dict) else 'غير محدد'
            }
            
            return self.render_json_template(filename, json_info, formatted_json)
            
        except Exception as e:
            return self.error_view(f"خطأ في قراءة ملف JSON: {str(e)}")
    
    def view_zip(self, file_path, filename):
        """عرض محتويات ملف ZIP"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                files_list = []
                total_size = 0
                
                for info in zip_file.filelist:
                    files_list.append({
                        'name': info.filename,
                        'size': info.file_size,
                        'compressed_size': info.compress_size,
                        'date': f"{info.date_time[0]}-{info.date_time[1]:02d}-{info.date_time[2]:02d}",
                        'is_dir': info.is_dir()
                    })
                    total_size += info.file_size
                
                zip_info = {
                    'files_count': len(files_list),
                    'total_size': total_size,
                    'compression_ratio': f"{((total_size - sum(f['compressed_size'] for f in files_list)) / total_size * 100):.1f}%" if total_size > 0 else "0%"
                }
                
                return self.render_zip_template(filename, zip_info, files_list)
                
        except Exception as e:
            return self.error_view(f"خطأ في قراءة ملف ZIP: {str(e)}")
    
    def view_audio(self, file_path, filename):
        """عرض ملفات الصوت"""
        return self.render_audio_template(filename, file_path)
    
    def view_video(self, file_path, filename):
        """عرض ملفات الفيديو"""
        return self.render_video_template(filename, file_path)
    
    def error_view(self, message):
        """عرض رسالة خطأ"""
        return f"""
        <div class="alert alert-danger text-center">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <h4>خطأ في عرض الملف</h4>
            <p>{message}</p>
        </div>
        """
    
    def download_view(self, filename):
        """عرض رسالة تحميل للملفات غير المدعومة"""
        return f"""
        <div class="alert alert-info text-center">
            <i class="fas fa-download fa-3x mb-3"></i>
            <h4>ملف غير قابل للعرض</h4>
            <p>الملف <strong>{filename}</strong> غير مدعوم للعرض المباشر.</p>
            <p>يمكنك تحميله لفتحه في التطبيق المناسب.</p>
        </div>
        """
    
    def render_docx_template(self, filename, doc_info, content, tables):
        """قالب عرض ملفات Word"""
        return render_template_string("""
        <div class="document-viewer">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-word text-primary me-2"></i>{{ filename }}</h3>
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr><td><strong>العنوان:</strong></td><td>{{ doc_info.title }}</td></tr>
                            <tr><td><strong>المؤلف:</strong></td><td>{{ doc_info.author }}</td></tr>
                            <tr><td><strong>تاريخ الإنشاء:</strong></td><td>{{ doc_info.created }}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr><td><strong>تاريخ التعديل:</strong></td><td>{{ doc_info.modified }}</td></tr>
                            <tr><td><strong>عدد الفقرات:</strong></td><td>{{ doc_info.paragraphs_count }}</td></tr>
                            <tr><td><strong>عدد الجداول:</strong></td><td>{{ doc_info.tables_count }}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="document-content">
                <h4>المحتوى:</h4>
                {% for para in content %}
                <div class="paragraph mb-2 p-2 border-start border-primary border-3">
                    <small class="text-muted">{{ para.style }} - {{ para.alignment }}</small>
                    <p>{{ para.text }}</p>
                </div>
                {% endfor %}
                
                {% if tables %}
                <h4 class="mt-4">الجداول:</h4>
                {% for table in tables %}
                <div class="table-responsive mb-3">
                    <table class="table table-bordered table-sm">
                        {% for row in table %}
                        <tr>
                            {% for cell in row %}
                            <td>{{ cell }}</td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </table>
                </div>
                {% endfor %}
                {% endif %}
            </div>
        </div>
        """, filename=filename, doc_info=doc_info, content=content, tables=tables)
    
    def render_pdf_template(self, filename, pdf_info, pages_text):
        """قالب عرض ملفات PDF"""
        return render_template_string("""
        <div class="document-viewer">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-pdf text-danger me-2"></i>{{ filename }}</h3>
                <div class="row">
                    <div class="col-md-4">
                        <strong>عدد الصفحات:</strong> {{ pdf_info.pages_count }}
                    </div>
                    <div class="col-md-4">
                        <strong>العنوان:</strong> {{ pdf_info.title }}
                    </div>
                    <div class="col-md-4">
                        <strong>المؤلف:</strong> {{ pdf_info.author }}
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                يتم عرض النص المستخرج من أول 5 صفحات فقط. لعرض كامل، يُنصح بتحميل الملف.
            </div>
            
            <div class="document-content">
                {% for page in pages_text %}
                <div class="page-content mb-4 p-3 border rounded">
                    <h5 class="text-primary">صفحة {{ page.page_number }}</h5>
                    <pre class="text-wrap">{{ page.text }}</pre>
                </div>
                {% endfor %}
            </div>
        </div>
        """, filename=filename, pdf_info=pdf_info, pages_text=pages_text)
    
    def render_image_template(self, filename, img_info, img_data, mime_type):
        """قالب عرض الصور"""
        return render_template_string("""
        <div class="document-viewer text-center">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-image text-success me-2"></i>{{ filename }}</h3>
                {% if not img_info.error %}
                <div class="row justify-content-center">
                    <div class="col-auto">
                        <table class="table table-sm">
                            <tr><td><strong>الأبعاد:</strong></td><td>{{ img_info.size }}</td></tr>
                            <tr><td><strong>التنسيق:</strong></td><td>{{ img_info.format }}</td></tr>
                            <tr><td><strong>النمط:</strong></td><td>{{ img_info.mode }}</td></tr>
                        </table>
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="image-container">
                <img src="data:{{ mime_type }};base64,{{ img_data }}" 
                     class="img-fluid border rounded shadow" 
                     style="max-width: 100%; max-height: 600px;"
                     alt="{{ filename }}">
            </div>
        </div>
        """, filename=filename, img_info=img_info, img_data=img_data, mime_type=mime_type)
    
    def render_text_template(self, filename, file_info, content):
        """قالب عرض الملفات النصية"""
        return render_template_string("""
        <div class="document-viewer">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-alt text-info me-2"></i>{{ filename }}</h3>
                <div class="row">
                    <div class="col-md-3">
                        <strong>الترميز:</strong> {{ file_info.encoding }}
                    </div>
                    <div class="col-md-3">
                        <strong>عدد الأسطر:</strong> {{ file_info.lines_count }}
                    </div>
                    <div class="col-md-3">
                        <strong>عدد الأحرف:</strong> {{ file_info.chars_count }}
                    </div>
                    <div class="col-md-3">
                        <strong>الحجم:</strong> {{ file_info.size }} بايت
                    </div>
                </div>
            </div>
            
            <div class="document-content">
                <pre class="bg-light p-3 rounded" style="white-space: pre-wrap; max-height: 600px; overflow-y: auto;">{{ content }}</pre>
            </div>
        </div>
        """, filename=filename, file_info=file_info, content=content)
    
    # طرق أخرى للأنواع المختلفة...
    def view_doc_fallback(self, file_path, filename):
        return self.download_view(filename)
    
    def view_rtf(self, file_path, filename):
        return self.view_text(file_path, filename)
    
    def view_excel(self, file_path, filename):
        return self.download_view(filename)
    
    def view_excel_fallback(self, file_path, filename):
        return self.download_view(filename)
    
    def view_powerpoint(self, file_path, filename):
        return self.download_view(filename)
    
    def view_powerpoint_fallback(self, file_path, filename):
        return self.download_view(filename)
    
    def view_svg(self, file_path, filename):
        return self.view_text(file_path, filename)
    
    def view_html(self, file_path, filename):
        return self.view_text(file_path, filename)
    
    def view_xml(self, file_path, filename):
        return self.view_text(file_path, filename)
    
    def view_css(self, file_path, filename):
        return self.view_text(file_path, filename)
    
    def view_javascript(self, file_path, filename):
        return self.view_text(file_path, filename)
    
    def view_archive_info(self, file_path, filename):
        return self.download_view(filename)
    
    def render_csv_template(self, filename, csv_info, rows):
        """قالب عرض ملفات CSV"""
        return render_template_string("""
        <div class="document-viewer">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-csv text-success me-2"></i>{{ filename }}</h3>
                <div class="row">
                    <div class="col-md-4">
                        <strong>عدد الصفوف:</strong> {{ csv_info.rows_count }}
                    </div>
                    <div class="col-md-4">
                        <strong>عدد الأعمدة:</strong> {{ csv_info.columns_count }}
                    </div>
                    <div class="col-md-4">
                        <strong>يحتوي على رأس:</strong> {{ 'نعم' if csv_info.has_header else 'لا' }}
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-sm">
                    {% for row in rows %}
                    <tr>
                        {% for cell in row %}
                        <td>{{ cell }}</td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </table>
            </div>
        </div>
        """, filename=filename, csv_info=csv_info, rows=rows)
    
    def render_json_template(self, filename, json_info, formatted_json):
        """قالب عرض ملفات JSON"""
        return render_template_string("""
        <div class="document-viewer">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-code text-warning me-2"></i>{{ filename }}</h3>
                <div class="row">
                    <div class="col-md-4">
                        <strong>النوع:</strong> {{ json_info.type }}
                    </div>
                    <div class="col-md-4">
                        <strong>الحجم:</strong> {{ json_info.size }} حرف
                    </div>
                    <div class="col-md-4">
                        <strong>عدد المفاتيح:</strong> {{ json_info.keys_count }}
                    </div>
                </div>
            </div>
            
            <div class="document-content">
                <pre class="bg-dark text-light p-3 rounded" style="max-height: 600px; overflow-y: auto;"><code>{{ formatted_json }}</code></pre>
            </div>
        </div>
        """, filename=filename, json_info=json_info, formatted_json=formatted_json)
    
    def render_zip_template(self, filename, zip_info, files_list):
        """قالب عرض ملفات ZIP"""
        return render_template_string("""
        <div class="document-viewer">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-archive text-secondary me-2"></i>{{ filename }}</h3>
                <div class="row">
                    <div class="col-md-4">
                        <strong>عدد الملفات:</strong> {{ zip_info.files_count }}
                    </div>
                    <div class="col-md-4">
                        <strong>الحجم الكلي:</strong> {{ zip_info.total_size }} بايت
                    </div>
                    <div class="col-md-4">
                        <strong>نسبة الضغط:</strong> {{ zip_info.compression_ratio }}
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>الحجم</th>
                            <th>الحجم المضغوط</th>
                            <th>التاريخ</th>
                            <th>النوع</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for file in files_list %}
                        <tr>
                            <td>
                                <i class="fas {{ 'fa-folder' if file.is_dir else 'fa-file' }} me-2"></i>
                                {{ file.name }}
                            </td>
                            <td>{{ file.size }} بايت</td>
                            <td>{{ file.compressed_size }} بايت</td>
                            <td>{{ file.date }}</td>
                            <td>{{ 'مجلد' if file.is_dir else 'ملف' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        """, filename=filename, zip_info=zip_info, files_list=files_list)
    
    def render_audio_template(self, filename, file_path):
        """قالب عرض ملفات الصوت"""
        return render_template_string("""
        <div class="document-viewer text-center">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-audio text-info me-2"></i>{{ filename }}</h3>
            </div>
            
            <div class="audio-player">
                <audio controls class="w-100" style="max-width: 500px;">
                    <source src="{{ url_for('messages.view_attachment', filename=file_path.split('/')[-1]) }}" type="audio/mpeg">
                    متصفحك لا يدعم تشغيل الملفات الصوتية.
                </audio>
            </div>
        </div>
        """, filename=filename, file_path=file_path)
    
    def render_video_template(self, filename, file_path):
        """قالب عرض ملفات الفيديو"""
        return render_template_string("""
        <div class="document-viewer text-center">
            <div class="document-header mb-4">
                <h3><i class="fas fa-file-video text-danger me-2"></i>{{ filename }}</h3>
            </div>
            
            <div class="video-player">
                <video controls class="w-100" style="max-width: 800px; max-height: 600px;">
                    <source src="{{ url_for('messages.view_attachment', filename=file_path.split('/')[-1]) }}" type="video/mp4">
                    متصفحك لا يدعم تشغيل ملفات الفيديو.
                </video>
            </div>
        </div>
        """, filename=filename, file_path=file_path)

# إنشاء مثيل عام للاستخدام
document_viewer = DocumentViewer()
