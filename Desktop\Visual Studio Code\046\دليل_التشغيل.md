# 🚀 دليل تشغيل نظام المراسلات الإلكترونية

## ✅ **النظام مكتمل وجاهز للتشغيل!**

تم إنشاء نظام مراسلات إلكترونية شامل يتضمن:
- 📧 إدارة الرسائل (واردة، صادرة، داخلية)
- 🖋️ التوقيع الإلكتروني (رقمي وصورة)
- 📁 نظام الأرشيف (سنوي، شهري، مخصص)
- 🔐 إدارة الصلاحيات والأدوار
- 👥 إدارة المستخدمين والأقسام
- 🎨 واجهة عربية جميلة ومتجاوبة

---

## 🎯 **طرق التشغيل:**

### الطريقة الأولى: التشغيل السريع (مستحسن)
```bash
# انقر نقراً مزدوجاً على الملف:
start.bat
```

### الطريقة الثانية: التشغيل الأساسي
```bash
python basic_app.py
```

### الطريقة الثالثة: التشغيل الكامل
```bash
# 1. تثبيت المتطلبات
pip install flask flask-sqlalchemy flask-login werkzeug jinja2

# 2. تشغيل النظام الكامل
python simple_start.py
```

---

## 🌐 **الوصول للنظام:**

- **الرابط**: http://localhost:5000
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

---

## 📂 **هيكل النظام:**

```
📁 نظام المراسلات الإلكترونية/
├── 🚀 start.bat                 # تشغيل سريع
├── 🚀 basic_app.py              # تطبيق أساسي للاختبار
├── 🚀 simple_start.py           # تشغيل مبسط
├── 🚀 app.py                    # التطبيق الرئيسي
├── 📊 models.py                 # نماذج قاعدة البيانات
├── ⚙️ config.py                 # إعدادات النظام
├── 📁 routes/                   # مسارات النظام
│   ├── 🔐 auth.py              # المصادقة
│   ├── 📧 messages.py          # الرسائل
│   ├── 👥 users.py             # المستخدمين
│   ├── 🏢 departments.py       # الأقسام
│   ├── 🔒 permissions.py       # الصلاحيات
│   └── 📁 archive.py           # الأرشيف
├── 📁 templates/                # قوالب HTML
│   ├── 📧 messages/            # قوالب الرسائل
│   ├── 👥 users/               # قوالب المستخدمين
│   ├── 🔒 permissions/         # قوالب الصلاحيات
│   ├── 📁 archive/             # قوالب الأرشيف
│   └── 🎨 base.html            # القالب الأساسي
└── 📁 uploads/                  # الملفات المرفوعة
    └── 🖋️ signatures/          # التوقيعات
```

---

## 🌟 **الميزات المتوفرة:**

### 📧 **إدارة الرسائل:**
- ✅ إنشاء رسائل جديدة
- ✅ تصنيف الرسائل (واردة/صادرة/داخلية)
- ✅ إرفاق ملفات (حتى 100 ميجابايت)
- ✅ نظام اعتماد متقدم
- ✅ معاينة الملفات
- ✅ تصدير PDF

### 🖋️ **التوقيع الإلكتروني:**
- ✅ رسم التوقيع مباشرة
- ✅ رفع صورة التوقيع
- ✅ عرض التوقيع في الرسائل
- ✅ إدارة التوقيعات

### 📁 **نظام الأرشيف:**
- ✅ أرشيف سنوي
- ✅ أرشيف شهري
- ✅ أرشيف مخصص
- ✅ بحث في الأرشيف
- ✅ إحصائيات شاملة

### 🔐 **إدارة الصلاحيات:**
- ✅ أدوار متعددة (مدير/سكرتير/موظف)
- ✅ صلاحيات مرنة
- ✅ تحكم دقيق في الوصول
- ✅ إدارة المستخدمين

### 🎨 **واجهة المستخدم:**
- ✅ تصميم عصري ومتجاوب
- ✅ دعم كامل للعربية
- ✅ تبديل اللغات
- ✅ تأثيرات تفاعلية

---

## 🔧 **حل المشاكل:**

### مشكلة: "Python not found"
**الحل**: تثبيت Python 3.7+ من python.org

### مشكلة: "Module not found"
**الحل**: تشغيل:
```bash
pip install flask flask-sqlalchemy flask-login werkzeug jinja2
```

### مشكلة: "Database error"
**الحل**: تشغيل:
```bash
python init_database.py
```

### مشكلة: "Port already in use"
**الحل**: تغيير المنفذ في الكود أو إيقاف التطبيق الآخر

---

## 📞 **الدعم:**

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Python 3.7+
2. تأكد من تثبيت المتطلبات
3. جرب التشغيل الأساسي أولاً
4. تحقق من رسائل الخطأ

---

## 🎉 **تهانينا!**

لديك الآن نظام مراسلات إلكترونية متكامل يتضمن:
- ✅ جميع الميزات المطلوبة
- ✅ التوقيع الإلكتروني
- ✅ نظام الأرشيف
- ✅ إدارة الصلاحيات
- ✅ واجهة عربية جميلة

**استمتع باستخدام النظام!** 🚀📧✨
