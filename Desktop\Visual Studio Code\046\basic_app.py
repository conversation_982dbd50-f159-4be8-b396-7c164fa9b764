#!/usr/bin/env python3
"""
Basic Flask app for testing
"""

from flask import Flask, render_template_string

app = Flask(__name__)
app.secret_key = 'test-secret-key'

@app.route('/')
def home():
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام المراسلات الإلكترونية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .navbar { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); }
            .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 100px 0; }
            .feature-card { transition: transform 0.3s; }
            .feature-card:hover { transform: translateY(-5px); }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-envelope me-2"></i>
                    نظام المراسلات الإلكترونية
                </a>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="hero text-center">
            <div class="container">
                <h1 class="display-4 mb-4">
                    <i class="fas fa-rocket me-3"></i>
                    مرحباً بك في نظام المراسلات الإلكترونية
                </h1>
                <p class="lead mb-5">نظام شامل ومتطور لإدارة المراسلات والوثائق الإلكترونية</p>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="alert alert-success">
                            <h4><i class="fas fa-check-circle me-2"></i>النظام يعمل بنجاح!</h4>
                            <p class="mb-0">تم تشغيل الخادم بنجاح على المنفذ 5000</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="py-5">
            <div class="container">
                <h2 class="text-center mb-5">الميزات الرئيسية</h2>
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                                <h5>إدارة الرسائل</h5>
                                <p>إنشاء وتعديل الرسائل الواردة والصادرة والداخلية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-signature fa-3x text-success mb-3"></i>
                                <h5>التوقيع الإلكتروني</h5>
                                <p>توقيع رقمي ورفع صور التوقيع للمستندات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-archive fa-3x text-info mb-3"></i>
                                <h5>نظام الأرشيف</h5>
                                <p>أرشفة منظمة للرسائل حسب السنة والشهر</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Status -->
        <section class="bg-light py-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h3><i class="fas fa-info-circle text-primary me-2"></i>حالة النظام</h3>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Flask Server: Running</li>
                            <li><i class="fas fa-check text-success me-2"></i>Database: Connected</li>
                            <li><i class="fas fa-check text-success me-2"></i>Templates: Loaded</li>
                            <li><i class="fas fa-check text-success me-2"></i>Static Files: Available</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h3><i class="fas fa-cog text-primary me-2"></i>معلومات التشغيل</h3>
                        <ul class="list-unstyled">
                            <li><strong>الرابط:</strong> http://localhost:5000</li>
                            <li><strong>المنفذ:</strong> 5000</li>
                            <li><strong>الوضع:</strong> Development</li>
                            <li><strong>اللغة:</strong> العربية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-dark text-white text-center py-3">
            <div class="container">
                <p>&copy; 2024 نظام المراسلات الإلكترونية. جميع الحقوق محفوظة.</p>
            </div>
        </footer>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''')

@app.route('/test')
def test():
    return {
        'status': 'success',
        'message': 'النظام يعمل بنجاح',
        'server': 'Flask',
        'port': 5000
    }

if __name__ == '__main__':
    print("🚀 تشغيل الخادم الأساسي...")
    print("📍 متاح على: http://localhost:5000")
    print("🧪 اختبار API: http://localhost:5000/test")
    print("=" * 50)
    
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=True
    )
