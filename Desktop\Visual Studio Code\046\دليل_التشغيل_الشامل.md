# 🏢 دليل التشغيل الشامل - نظام المراسلات الإلكترونية

## 📋 المحتويات
1. [متطلبات النظام](#متطلبات-النظام)
2. [طرق التشغيل](#طرق-التشغيل)
3. [استكشاف الأخطاء](#استكشاف-الأخطاء)
4. [الميزات المتاحة](#الميزات-المتاحة)
5. [معلومات تسجيل الدخول](#معلومات-تسجيل-الدخول)

---

## 🔧 متطلبات النظام

### ✅ المتطلبات الأساسية:
- **Python 3.8+** (يُنصح بـ 3.9 أو أحدث)
- **نظا<PERSON> التشغيل**: Windows 10/11, macOS, Linux
- **الذاكرة**: 512 MB RAM كحد أدنى
- **المساحة**: 100 MB مساحة فارغة

### 📦 الحزم المطلوبة:
```bash
flask>=2.0.0
flask-sqlalchemy>=3.0.0
flask-login>=0.6.0
werkzeug>=2.0.0
python-docx>=0.8.11
fpdf>=2.5.0
PyPDF2>=3.0.0
requests>=2.28.0
```

---

## 🚀 طرق التشغيل

### 🎯 الطريقة الأولى: التشغيل السريع (مُوصى بها)

#### على Windows:
```batch
# انقر نقراً مزدوجاً على الملف
run.bat

# أو من سطر الأوامر
python debug_start.py
```

#### على macOS/Linux:
```bash
python3 debug_start.py
```

### 🎯 الطريقة الثانية: التشغيل المبسط
```bash
python simple_start.py
```

### 🎯 الطريقة الثالثة: التشغيل التقليدي
```bash
python app.py
```

### 🎯 الطريقة الرابعة: مع التشخيص
```bash
python troubleshoot.py
```

---

## 🔍 استكشاف الأخطاء

### ❌ مشكلة: "Python غير موجود"
**الحل:**
1. تثبيت Python من [python.org](https://python.org)
2. التأكد من إضافة Python إلى PATH
3. إعادة تشغيل سطر الأوامر

### ❌ مشكلة: "حزم مفقودة"
**الحل:**
```bash
# تثبيت جميع المتطلبات
pip install flask flask-sqlalchemy flask-login werkzeug

# أو من ملف المتطلبات
pip install -r requirements.txt
```

### ❌ مشكلة: "المنفذ محجوز"
**الحل:**
1. تغيير المنفذ في الملف
2. أو إيقاف التطبيق الآخر
3. أو استخدام منفذ آخر (8586, 8587, إلخ)

### ❌ مشكلة: "خطأ في قاعدة البيانات"
**الحل:**
```bash
# إنشاء قاعدة بيانات جديدة
python create_fresh_db.py

# أو إعادة تهيئة النظام
python init_database.py
```

### ❌ مشكلة: "ملفات مفقودة"
**الحل:**
1. التأكد من وجود جميع الملفات
2. تحميل النظام مرة أخرى
3. التحقق من الصلاحيات

---

## 🌟 الميزات المتاحة

### 📧 إدارة المراسلات
- ✅ إنشاء وتحرير الرسائل
- ✅ رفع المرفقات (PDF, DOCX, صور)
- ✅ تتبع حالة الرسائل
- ✅ نظام الموافقات
- ✅ الأرشفة والبحث

### 👥 إدارة المستخدمين
- ✅ إنشاء وتحرير المستخدمين
- ✅ نظام الأدوار والصلاحيات
- ✅ إدارة الأقسام
- ✅ تتبع النشاطات

### 🏢 إدارة الجهات
- ✅ إضافة وتحرير الجهات
- ✅ تصنيف الجهات
- ✅ معلومات الاتصال
- ✅ ربط الرسائل بالجهات

### ⚙️ الإعدادات المتقدمة
- ✅ تخصيص الألوان
- ✅ إعدادات العرض
- ✅ عرض الأيام كأرقام فقط
- ✅ تنسيق التاريخ والوقت
- ✅ الإعدادات العامة

### 📊 التقارير والإحصائيات
- ✅ لوحة المعلومات
- ✅ إحصائيات الرسائل
- ✅ تقارير النشاط
- ✅ الرسائل الحديثة

---

## 🔐 معلومات تسجيل الدخول

### 👤 المستخدم الافتراضي:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الدور**: مدير النظام
- **الصلاحيات**: جميع الصلاحيات

### 🌐 روابط النظام:
- **الصفحة الرئيسية**: http://localhost:8585
- **تسجيل الدخول**: http://localhost:8585/auth/login
- **لوحة المعلومات**: http://localhost:8585/
- **الرسائل**: http://localhost:8585/messages
- **المستخدمين**: http://localhost:8585/users
- **الأقسام**: http://localhost:8585/departments
- **الجهات**: http://localhost:8585/entities
- **الإعدادات**: http://localhost:8585/settings

---

## 📱 واجهة النظام

### 🎨 الألوان والتصميم:
- تصميم عربي احترافي
- دعم كامل للغة العربية
- ألوان قابلة للتخصيص
- واجهة متجاوبة مع جميع الأجهزة

### 📊 لوحة المعلومات:
- إحصائيات شاملة
- الرسائل الحديثة
- تنبيهات النظام
- روابط سريعة

### 🔍 البحث والتصفية:
- بحث متقدم في الرسائل
- تصفية حسب التاريخ والحالة
- ترتيب النتائج
- تصدير البيانات

---

## 🛠️ الصيانة والتحديث

### 🔄 النسخ الاحتياطي:
```bash
# نسخ قاعدة البيانات
cp correspondence.db backup_$(date +%Y%m%d).db

# نسخ الملفات المرفوعة
cp -r uploads uploads_backup_$(date +%Y%m%d)
```

### 📈 تحديث النظام:
```bash
# تحديث الحزم
pip install --upgrade flask flask-sqlalchemy flask-login

# تحديث قاعدة البيانات
python update_database.py
```

### 🧹 تنظيف النظام:
```bash
# حذف الملفات المؤقتة
rm -rf __pycache__
rm -rf *.pyc

# تنظيف قاعدة البيانات
python clean_database.py
```

---

## 📞 الدعم والمساعدة

### 🆘 في حالة المشاكل:
1. **تشغيل أداة التشخيص**: `python troubleshoot.py`
2. **فحص ملفات السجل**: تحقق من رسائل الخطأ
3. **إعادة تشغيل النظام**: أوقف وشغل مرة أخرى
4. **إعادة تهيئة قاعدة البيانات**: `python create_fresh_db.py`

### 💡 نصائح للأداء الأمثل:
- استخدم Python 3.9+ للأداء الأفضل
- تأكد من وجود مساحة كافية للملفات المرفوعة
- قم بعمل نسخ احتياطية دورية
- راقب استخدام الذاكرة والمعالج

---

## 🎯 الميزات الجديدة

### 📊 عرض الأيام كأرقام فقط:
- **الوصف**: عرض "3" بدلاً من "منذ 3 أيام"
- **التفعيل**: الإعدادات > إعدادات العرض
- **الفائدة**: واجهة أكثر إيجازاً ووضوحاً

### 🎨 تخصيص الألوان:
- **10 ألوان قابلة للتخصيص**
- **معاينة مباشرة للتغييرات**
- **إعادة تعيين للقيم الافتراضية**
- **تطبيق فوري على جميع الصفحات**

### ⚙️ إعدادات متقدمة:
- **تنسيق التاريخ والوقت**
- **عدد العناصر في الصفحة**
- **الوضع المضغوط**
- **إعدادات الواجهة**

---

## ✅ قائمة التحقق السريع

### قبل التشغيل:
- [ ] Python 3.8+ مثبت
- [ ] جميع الحزم مثبتة
- [ ] المنفذ 8585 متاح
- [ ] صلاحيات الكتابة متوفرة

### بعد التشغيل:
- [ ] النظام يعمل على http://localhost:8585
- [ ] تسجيل الدخول يعمل (admin/admin123)
- [ ] جميع الصفحات تحمل بشكل صحيح
- [ ] رفع الملفات يعمل
- [ ] الإعدادات قابلة للتعديل

---

**🎉 مبروك! نظام المراسلات الإلكترونية جاهز للاستخدام!**

للمساعدة الإضافية، شغل: `python troubleshoot.py`
