{% extends "base.html" %}

{% block title %}إعدادات الألوان{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-palette me-2"></i>
                    إعدادات الألوان
                </h2>
                <div>
                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للإعدادات
                    </a>
                </div>
            </div>

            <form method="POST" id="colorForm">
                <div class="row">
                    <!-- الألوان الأساسية -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-paint-brush me-2"></i>الألوان الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="primary_color" class="form-label">اللون الأساسي</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="primary_color" name="primary_color" 
                                                   value="{{ colors.primary_color }}" title="اختر اللون الأساسي">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.primary_color }}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="secondary_color" class="form-label">اللون الثانوي</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="secondary_color" name="secondary_color" 
                                                   value="{{ colors.secondary_color }}" title="اختر اللون الثانوي">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.secondary_color }}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="success_color" class="form-label">لون النجاح</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="success_color" name="success_color" 
                                                   value="{{ colors.success_color }}" title="اختر لون النجاح">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.success_color }}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="danger_color" class="form-label">لون الخطر</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="danger_color" name="danger_color" 
                                                   value="{{ colors.danger_color }}" title="اختر لون الخطر">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.danger_color }}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="warning_color" class="form-label">لون التحذير</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="warning_color" name="warning_color" 
                                                   value="{{ colors.warning_color }}" title="اختر لون التحذير">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.warning_color }}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="info_color" class="form-label">لون المعلومات</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="info_color" name="info_color" 
                                                   value="{{ colors.info_color }}" title="اختر لون المعلومات">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.info_color }}" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ألوان الخلفية -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-fill me-2"></i>ألوان الخلفية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="sidebar_bg" class="form-label">خلفية الشريط الجانبي</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="sidebar_bg" name="sidebar_bg" 
                                                   value="{{ colors.sidebar_bg }}" title="اختر خلفية الشريط الجانبي">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.sidebar_bg }}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="navbar_bg" class="form-label">خلفية شريط التنقل</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="navbar_bg" name="navbar_bg" 
                                                   value="{{ colors.navbar_bg }}" title="اختر خلفية شريط التنقل">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.navbar_bg }}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="card_bg" class="form-label">خلفية البطاقات</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="card_bg" name="card_bg" 
                                                   value="{{ colors.card_bg }}" title="اختر خلفية البطاقات">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.card_bg }}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="text_color" class="form-label">لون النص</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="text_color" name="text_color" 
                                                   value="{{ colors.text_color }}" title="اختر لون النص">
                                            <input type="text" class="form-control" 
                                                   value="{{ colors.text_color }}" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعاينة والأدوات -->
                    <div class="col-lg-6">
                        <!-- معاينة الألوان -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-eye me-2"></i>معاينة الألوان
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="colorPreview">
                                    <!-- أزرار المعاينة -->
                                    <div class="mb-3">
                                        <h6>الأزرار:</h6>
                                        <button type="button" class="btn me-2 mb-2" id="preview-primary">أساسي</button>
                                        <button type="button" class="btn me-2 mb-2" id="preview-secondary">ثانوي</button>
                                        <button type="button" class="btn me-2 mb-2" id="preview-success">نجاح</button>
                                        <button type="button" class="btn me-2 mb-2" id="preview-danger">خطر</button>
                                        <button type="button" class="btn me-2 mb-2" id="preview-warning">تحذير</button>
                                        <button type="button" class="btn me-2 mb-2" id="preview-info">معلومات</button>
                                    </div>

                                    <!-- تنبيهات المعاينة -->
                                    <div class="mb-3">
                                        <h6>التنبيهات:</h6>
                                        <div class="alert mb-2" id="preview-alert-success">تنبيه نجاح</div>
                                        <div class="alert mb-2" id="preview-alert-danger">تنبيه خطر</div>
                                        <div class="alert mb-2" id="preview-alert-warning">تنبيه تحذير</div>
                                        <div class="alert mb-0" id="preview-alert-info">تنبيه معلومات</div>
                                    </div>

                                    <!-- بطاقة معاينة -->
                                    <div class="mb-3">
                                        <h6>البطاقات:</h6>
                                        <div class="card" id="preview-card">
                                            <div class="card-header" id="preview-card-header">
                                                عنوان البطاقة
                                            </div>
                                            <div class="card-body" id="preview-card-body">
                                                <p class="card-text" id="preview-text">هذا نص تجريبي لمعاينة الألوان</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أدوات الألوان -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-tools me-2"></i>أدوات الألوان
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-outline-primary" onclick="resetToDefault()">
                                        <i class="fas fa-undo me-1"></i>إعادة للألوان الافتراضية
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="generatePalette()">
                                        <i class="fas fa-magic me-1"></i>توليد لوحة ألوان
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="copyColors()">
                                        <i class="fas fa-copy me-1"></i>نسخ الألوان
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="exportColors()">
                                        <i class="fas fa-download me-1"></i>تصدير الألوان
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- حفظ الإعدادات -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-1"></i>حفظ الألوان
                                    </button>
                                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحديث المعاينة عند تغيير الألوان
document.addEventListener('DOMContentLoaded', function() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    
    colorInputs.forEach(input => {
        input.addEventListener('change', function() {
            updatePreview();
            updateTextInput(this);
        });
    });
    
    // تحديث المعاينة الأولية
    updatePreview();
});

function updateTextInput(colorInput) {
    const textInput = colorInput.nextElementSibling;
    textInput.value = colorInput.value;
}

function updatePreview() {
    const colors = {
        primary: document.getElementById('primary_color').value,
        secondary: document.getElementById('secondary_color').value,
        success: document.getElementById('success_color').value,
        danger: document.getElementById('danger_color').value,
        warning: document.getElementById('warning_color').value,
        info: document.getElementById('info_color').value,
        cardBg: document.getElementById('card_bg').value,
        textColor: document.getElementById('text_color').value
    };
    
    // تحديث الأزرار
    document.getElementById('preview-primary').style.backgroundColor = colors.primary;
    document.getElementById('preview-secondary').style.backgroundColor = colors.secondary;
    document.getElementById('preview-success').style.backgroundColor = colors.success;
    document.getElementById('preview-danger').style.backgroundColor = colors.danger;
    document.getElementById('preview-warning').style.backgroundColor = colors.warning;
    document.getElementById('preview-info').style.backgroundColor = colors.info;
    
    // تحديث التنبيهات
    document.getElementById('preview-alert-success').style.backgroundColor = colors.success + '20';
    document.getElementById('preview-alert-success').style.borderColor = colors.success;
    document.getElementById('preview-alert-danger').style.backgroundColor = colors.danger + '20';
    document.getElementById('preview-alert-danger').style.borderColor = colors.danger;
    document.getElementById('preview-alert-warning').style.backgroundColor = colors.warning + '20';
    document.getElementById('preview-alert-warning').style.borderColor = colors.warning;
    document.getElementById('preview-alert-info').style.backgroundColor = colors.info + '20';
    document.getElementById('preview-alert-info').style.borderColor = colors.info;
    
    // تحديث البطاقة
    document.getElementById('preview-card').style.backgroundColor = colors.cardBg;
    document.getElementById('preview-card-header').style.backgroundColor = colors.primary;
    document.getElementById('preview-text').style.color = colors.textColor;
}

function resetToDefault() {
    const defaultColors = {
        'primary_color': '#0d6efd',
        'secondary_color': '#6c757d',
        'success_color': '#198754',
        'danger_color': '#dc3545',
        'warning_color': '#ffc107',
        'info_color': '#0dcaf0',
        'sidebar_bg': '#343a40',
        'navbar_bg': '#0d6efd',
        'card_bg': '#ffffff',
        'text_color': '#212529'
    };
    
    Object.keys(defaultColors).forEach(key => {
        const input = document.getElementById(key);
        if (input) {
            input.value = defaultColors[key];
            updateTextInput(input);
        }
    });
    
    updatePreview();
}

function generatePalette() {
    // توليد لوحة ألوان بناءً على اللون الأساسي
    const primaryColor = document.getElementById('primary_color').value;
    // يمكن إضافة منطق توليد الألوان هنا
    alert('سيتم إضافة ميزة توليد لوحة الألوان قريباً');
}

function copyColors() {
    const colors = {};
    const colorInputs = document.querySelectorAll('input[type="color"]');
    
    colorInputs.forEach(input => {
        colors[input.name] = input.value;
    });
    
    navigator.clipboard.writeText(JSON.stringify(colors, null, 2)).then(() => {
        alert('تم نسخ الألوان إلى الحافظة');
    });
}

function exportColors() {
    const colors = {};
    const colorInputs = document.querySelectorAll('input[type="color"]');
    
    colorInputs.forEach(input => {
        colors[input.name] = input.value;
    });
    
    const dataStr = JSON.stringify(colors, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'colors.json';
    link.click();
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.form-control-color {
    width: 60px;
    height: 38px;
    border-radius: 0.375rem 0 0 0.375rem;
}

.input-group .form-control-color + .form-control {
    border-radius: 0 0.375rem 0.375rem 0;
}

#colorPreview .btn {
    color: white;
    border: none;
}

#colorPreview .alert {
    border-width: 1px;
    border-style: solid;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}
</style>
{% endblock %}
