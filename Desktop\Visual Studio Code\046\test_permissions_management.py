#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إدارة الصلاحيات
"""

import requests
import sys

def test_permissions_management():
    """اختبار إدارة الصلاحيات"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة إدارة الصلاحيات الرئيسية
        print("\n🏠 اختبار صفحة إدارة الصلاحيات الرئيسية...")
        response = session.get(f"{base_url}/permissions")
        if response.status_code == 200:
            print("✅ صفحة إدارة الصلاحيات الرئيسية تعمل")
            
            content = response.text
            features_found = []
            
            if 'إدارة الأدوار' in content:
                features_found.append("رابط إدارة الأدوار")
            
            if 'إدارة الصلاحيات' in content:
                features_found.append("رابط إدارة الصلاحيات")
            
            if 'إدارة المستخدمين' in content:
                features_found.append("رابط إدارة المستخدمين")
            
            if 'تهيئة الصلاحيات' in content:
                features_found.append("زر تهيئة الصلاحيات")
            
            if 'إجمالي الأدوار' in content:
                features_found.append("إحصائيات الأدوار")
            
            if 'إجمالي الصلاحيات' in content:
                features_found.append("إحصائيات الصلاحيات")
            
            print(f"✅ الميزات الموجودة: {', '.join(features_found)}")
        else:
            print(f"❌ فشل في الوصول لصفحة إدارة الصلاحيات الرئيسية: {response.status_code}")
            return False
        
        # اختبار صفحة قائمة الصلاحيات
        print("\n🔑 اختبار صفحة قائمة الصلاحيات...")
        response = session.get(f"{base_url}/permissions/permissions")
        if response.status_code == 200:
            print("✅ صفحة قائمة الصلاحيات تعمل")
            
            content = response.text
            permissions_features = []
            
            if 'صلاحيات الرسائل' in content:
                permissions_features.append("صلاحيات الرسائل")
            
            if 'صلاحيات المستخدمين' in content:
                permissions_features.append("صلاحيات المستخدمين")
            
            if 'صلاحيات الأقسام' in content:
                permissions_features.append("صلاحيات الأقسام")
            
            if 'صلاحيات النظام' in content:
                permissions_features.append("صلاحيات النظام")
            
            if 'فئات الصلاحيات' in content:
                permissions_features.append("إحصائيات الفئات")
            
            if 'الاسم التقني' in content:
                permissions_features.append("تفاصيل الصلاحيات")
            
            print(f"✅ ميزات الصلاحيات: {', '.join(permissions_features)}")
        else:
            print(f"❌ فشل في الوصول لصفحة قائمة الصلاحيات: {response.status_code}")
            return False
        
        # اختبار تهيئة الصلاحيات الافتراضية
        print("\n⚙️ اختبار تهيئة الصلاحيات الافتراضية...")
        response = session.post(f"{base_url}/permissions/init-permissions")
        if response.status_code == 200:
            print("✅ تم تهيئة الصلاحيات الافتراضية بنجاح")
        else:
            print(f"⚠️ مشكلة في تهيئة الصلاحيات: {response.status_code}")
        
        # اختبار صفحة قائمة الصلاحيات مرة أخرى بعد التهيئة
        print("\n🔄 اختبار صفحة الصلاحيات بعد التهيئة...")
        response = session.get(f"{base_url}/permissions/permissions")
        if response.status_code == 200:
            print("✅ صفحة الصلاحيات تعمل بعد التهيئة")
            
            content = response.text
            if 'لا توجد صلاحيات محددة' not in content:
                print("✅ تم العثور على صلاحيات بعد التهيئة")
            else:
                print("⚠️ لم يتم العثور على صلاحيات بعد التهيئة")
        else:
            print(f"❌ فشل في الوصول لصفحة الصلاحيات بعد التهيئة: {response.status_code}")
            return False
        
        # اختبار صفحة قائمة الأدوار
        print("\n👥 اختبار صفحة قائمة الأدوار...")
        response = session.get(f"{base_url}/permissions/roles")
        if response.status_code == 200:
            print("✅ صفحة قائمة الأدوار تعمل")
        else:
            print(f"❌ فشل في الوصول لصفحة قائمة الأدوار: {response.status_code}")
            return False
        
        # اختبار صفحة إنشاء دور جديد
        print("\n➕ اختبار صفحة إنشاء دور جديد...")
        response = session.get(f"{base_url}/permissions/roles/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء دور جديد تعمل")
        else:
            print(f"❌ فشل في الوصول لصفحة إنشاء دور: {response.status_code}")
            return False
        
        # اختبار إنشاء دور جديد
        print("\n📝 اختبار إنشاء دور جديد...")
        new_role_data = {
            'name': 'test_permissions_role',
            'display_name': 'دور اختبار الصلاحيات',
            'description': 'دور لاختبار إدارة الصلاحيات',
            'permissions': ['1', '2', '3']
        }
        
        response = session.post(f"{base_url}/permissions/roles/new", data=new_role_data)
        if response.status_code == 200:
            print("✅ تم إنشاء دور جديد بنجاح")
        else:
            print(f"⚠️ مشكلة في إنشاء دور جديد: {response.status_code}")
        
        # اختبار الروابط في الصفحة الرئيسية
        print("\n🔗 اختبار الروابط في الصفحة الرئيسية...")
        
        # اختبار رابط إدارة الأدوار من الصفحة الرئيسية
        response = session.get(f"{base_url}/permissions")
        if response.status_code == 200:
            content = response.text
            if 'url_for(\'permissions.list_roles\')' in content or 'permissions/roles' in content:
                print("✅ رابط إدارة الأدوار موجود")
            else:
                print("⚠️ رابط إدارة الأدوار غير واضح")
        
        # اختبار رابط إدارة الصلاحيات من الصفحة الرئيسية
        if 'url_for(\'permissions.list_permissions\')' in content or 'permissions/permissions' in content:
            print("✅ رابط إدارة الصلاحيات موجود")
        else:
            print("⚠️ رابط إدارة الصلاحيات غير واضح")
        
        print("\n🎉 تم اكتمال اختبار إدارة الصلاحيات!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار إدارة الصلاحيات")
    print("=" * 60)
    
    success = test_permissions_management()
    
    if success:
        print("\n✅ جميع وظائف إدارة الصلاحيات تعمل بنجاح!")
        print("\n📋 الوظائف المتاحة:")
        print("  ✅ عرض الصفحة الرئيسية لإدارة الصلاحيات")
        print("  ✅ عرض قائمة الصلاحيات مجمعة حسب الفئة")
        print("  ✅ تهيئة الصلاحيات الافتراضية")
        print("  ✅ إحصائيات الصلاحيات والأدوار")
        print("  ✅ الروابط السريعة للأقسام المختلفة")
        print("  ✅ إدارة الأدوار")
        print("  ✅ إنشاء أدوار جديدة")
        print("\n🔑 فئات الصلاحيات المتاحة:")
        print("  📧 صلاحيات الرسائل")
        print("  👥 صلاحيات المستخدمين")
        print("  🏢 صلاحيات الأقسام")
        print("  ⚙️ صلاحيات النظام")
        print("\n🌐 للوصول لإدارة الصلاحيات:")
        print("  http://localhost:8585/permissions")
        print("  http://localhost:8585/permissions/permissions")
        print("  http://localhost:8585/permissions/roles")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n🎯 إدارة الصلاحيات مُفعلة وتعمل بشكل مثالي!")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض وظائف إدارة الصلاحيات!")
        sys.exit(1)
