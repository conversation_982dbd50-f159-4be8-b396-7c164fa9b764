{% extends "base.html" %}

{% block title %}إعدادات النظام{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات النظام
                </h2>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي الإعدادات</h6>
                                    <h3 class="mb-0">{{ stats.total }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-cog fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">الإعدادات النشطة</h6>
                                    <h3 class="mb-0">{{ stats.active }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إعدادات الألوان</h6>
                                    <h3 class="mb-0">{{ stats.colors }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-palette fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إعدادات العرض</h6>
                                    <h3 class="mb-0">{{ stats.display }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-desktop fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- أقسام الإعدادات -->
                <div class="col-lg-8">
                    <div class="row">
                        <!-- إعدادات الألوان -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-palette me-2"></i>إعدادات الألوان
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تخصيص ألوان النظام والواجهة</p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>الألوان الأساسية</li>
                                        <li><i class="fas fa-check text-success me-2"></i>ألوان الخلفية</li>
                                        <li><i class="fas fa-check text-success me-2"></i>ألوان النصوص</li>
                                        <li><i class="fas fa-check text-success me-2"></i>ألوان الأزرار</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('settings.colors') }}" class="btn btn-primary">
                                        <i class="fas fa-edit me-1"></i>تعديل الألوان
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات العرض -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-desktop me-2"></i>إعدادات العرض
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تخصيص طريقة عرض البيانات والواجهة</p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>تنسيق التاريخ والوقت</li>
                                        <li><i class="fas fa-check text-success me-2"></i>عرض الأيام كأرقام</li>
                                        <li><i class="fas fa-check text-success me-2"></i>عدد العناصر في الصفحة</li>
                                        <li><i class="fas fa-check text-success me-2"></i>الوضع المضغوط</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('settings.display') }}" class="btn btn-info">
                                        <i class="fas fa-edit me-1"></i>تعديل العرض
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- الإعدادات العامة -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-cogs me-2"></i>الإعدادات العامة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">إعدادات النظام العامة والمعلومات الأساسية</p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>اسم النظام</li>
                                        <li><i class="fas fa-check text-success me-2"></i>معلومات الاتصال</li>
                                        <li><i class="fas fa-check text-success me-2"></i>الشعار والهوية</li>
                                        <li><i class="fas fa-check text-success me-2"></i>الوصف والعنوان</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('settings.general') }}" class="btn btn-success">
                                        <i class="fas fa-edit me-1"></i>تعديل العامة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- أدوات الإدارة -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-tools me-2"></i>أدوات الإدارة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">أدوات إدارة وصيانة الإعدادات</p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>إعادة تعيين الإعدادات</li>
                                        <li><i class="fas fa-check text-success me-2"></i>نسخ احتياطي</li>
                                        <li><i class="fas fa-check text-success me-2"></i>استيراد/تصدير</li>
                                        <li><i class="fas fa-check text-success me-2"></i>سجل التغييرات</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#resetModal">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الشريط الجانبي -->
                <div class="col-lg-4">
                    <!-- الإعدادات الحديثة -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>التغييرات الحديثة
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if recent_settings %}
                            <div class="list-group list-group-flush">
                                {% for setting in recent_settings %}
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">{{ setting.display_name or setting.setting_key }}</h6>
                                            <p class="mb-1 text-muted small">{{ setting.setting_value[:50] }}{% if setting.setting_value|length > 50 %}...{% endif %}</p>
                                            <small class="text-muted">{{ setting.updated_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                        </div>
                                        <span class="badge bg-{{ 'primary' if setting.category == 'colors' else 'info' if setting.category == 'display' else 'success' }}">
                                            {{ setting.category }}
                                        </span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-center py-3">
                                <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد تغييرات حديثة</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- روابط سريعة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-link me-2"></i>روابط سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('settings.colors') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-palette me-1"></i>إعدادات الألوان
                                </a>
                                <a href="{{ url_for('settings.display') }}" class="btn btn-outline-info">
                                    <i class="fas fa-desktop me-1"></i>إعدادات العرض
                                </a>
                                <a href="{{ url_for('settings.general') }}" class="btn btn-outline-success">
                                    <i class="fas fa-cogs me-1"></i>الإعدادات العامة
                                </a>
                                <a href="{{ url_for('settings.api_colors') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-code me-1"></i>API الألوان
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إعادة التعيين -->
<div class="modal fade" id="resetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تعيين الإعدادات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>اختر نوع الإعدادات التي تريد إعادة تعيينها:</p>
                <form id="resetForm" method="POST" action="{{ url_for('settings.reset_settings') }}">
                    <div class="mb-3">
                        <label for="category" class="form-label">فئة الإعدادات</label>
                        <select class="form-select" id="category" name="category" required>
                            <option value="colors">إعدادات الألوان</option>
                            <option value="display">إعدادات العرض</option>
                            <option value="general">الإعدادات العامة</option>
                            <option value="all">جميع الإعدادات</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء سيحذف جميع الإعدادات المخصصة ويعيدها للقيم الافتراضية.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="resetForm" class="btn btn-danger">إعادة تعيين</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.list-group-item {
    border-bottom: 1px solid #e9ecef !important;
}

.list-group-item:last-child {
    border-bottom: none !important;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
