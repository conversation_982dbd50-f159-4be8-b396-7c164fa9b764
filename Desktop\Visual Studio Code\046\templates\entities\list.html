{% extends "base.html" %}

{% block title %}إدارة الجهات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-building me-2"></i>
                    إدارة الجهات والمؤسسات
                </h2>
                {% if current_user.is_admin() or current_user.is_director() or current_user.is_department_head() or current_user.is_manager() %}
                <div>
                    <a href="{{ url_for('entities.new_entity') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>جهة جديدة
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي الجهات</h6>
                                    <h3 class="mb-0">{{ stats.total }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-building fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">الجهات النشطة</h6>
                                    <h3 class="mb-0">{{ stats.active }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">الجهات الحكومية</h6>
                                    <h3 class="mb-0">{{ stats.government }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-university fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">متكررة التراسل</h6>
                                    <h3 class="mb-0">{{ stats.frequent }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search }}" placeholder="اسم الجهة أو الرقم المرجعي">
                        </div>
                        <div class="col-md-2">
                            <label for="type" class="form-label">نوع الجهة</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">جميع الأنواع</option>
                                <option value="government" {% if entity_type == 'government' %}selected{% endif %}>جهة حكومية</option>
                                <option value="private" {% if entity_type == 'private' %}selected{% endif %}>جهة خاصة</option>
                                <option value="individual" {% if entity_type == 'individual' %}selected{% endif %}>فرد</option>
                                <option value="ngo" {% if entity_type == 'ngo' %}selected{% endif %}>منظمة غير ربحية</option>
                                <option value="international" {% if entity_type == 'international' %}selected{% endif %}>جهة دولية</option>
                                <option value="academic" {% if entity_type == 'academic' %}selected{% endif %}>جهة أكاديمية</option>
                                <option value="other" {% if entity_type == 'other' %}selected{% endif %}>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">جميع الفئات</option>
                                <option value="ministry" {% if category == 'ministry' %}selected{% endif %}>وزارة</option>
                                <option value="authority" {% if category == 'authority' %}selected{% endif %}>هيئة</option>
                                <option value="company" {% if category == 'company' %}selected{% endif %}>شركة</option>
                                <option value="university" {% if category == 'university' %}selected{% endif %}>جامعة</option>
                                <option value="hospital" {% if category == 'hospital' %}selected{% endif %}>مستشفى</option>
                                <option value="bank" {% if category == 'bank' %}selected{% endif %}>بنك</option>
                                <option value="embassy" {% if category == 'embassy' %}selected{% endif %}>سفارة</option>
                                <option value="court" {% if category == 'court' %}selected{% endif %}>محكمة</option>
                                <option value="municipality" {% if category == 'municipality' %}selected{% endif %}>بلدية</option>
                                <option value="other" {% if category == 'other' %}selected{% endif %}>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                                <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>غير نشط</option>
                                <option value="government" {% if status == 'government' %}selected{% endif %}>حكومية</option>
                                <option value="frequent" {% if status == 'frequent' %}selected{% endif %}>متكررة التراسل</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="{{ url_for('entities.list_entities') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة الجهات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>قائمة الجهات
                        <span class="badge bg-secondary ms-2">{{ entities.total }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if entities.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الجهة</th>
                                    <th>النوع</th>
                                    <th>الفئة</th>
                                    <th>الشخص المسؤول</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entity in entities.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ entity.name }}</strong>
                                            {% if entity.name_en %}
                                            <br><small class="text-muted">{{ entity.name_en }}</small>
                                            {% endif %}
                                            {% if entity.reference_number %}
                                            <br><small class="text-info">{{ entity.reference_number }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ entity.get_type_display() }}</span>
                                    </td>
                                    <td>
                                        {% if entity.category %}
                                        <span class="badge bg-info">{{ entity.get_category_display() }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if entity.contact_person %}
                                        <div>
                                            <strong>{{ entity.contact_person }}</strong>
                                            {% if entity.contact_title %}
                                            <br><small class="text-muted">{{ entity.contact_title }}</small>
                                            {% endif %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if entity.phone %}
                                        {{ entity.phone }}
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge {{ entity.get_status_badge_class() }}">
                                            {{ entity.get_status_display() }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('entities.view_entity', id=entity.id) }}" 
                                               class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.is_admin() or current_user.is_director() or current_user.is_department_head() or current_user.is_manager() %}
                                            <a href="{{ url_for('entities.edit_entity', id=entity.id) }}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="confirmDelete({{ entity.id }}, '{{ entity.name }}')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التنقل بين الصفحات -->
                    {% if entities.pages > 1 %}
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if entities.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('entities.list_entities', page=entities.prev_num, search=search, type=entity_type, category=category, status=status) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in entities.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != entities.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('entities.list_entities', page=page_num, search=search, type=entity_type, category=category, status=status) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if entities.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('entities.list_entities', page=entities.next_num, search=search, type=entity_type, category=category, status=status) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد جهات</h5>
                        <p class="text-muted">لم يتم العثور على جهات تطابق معايير البحث</p>
                        {% if current_user.is_admin() or current_user.is_director() or current_user.is_department_head() or current_user.is_manager() %}
                        <a href="{{ url_for('entities.new_entity') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>إضافة جهة جديدة
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الجهة <strong id="entityName"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(entityId, entityName) {
    document.getElementById('entityName').textContent = entityName;
    document.getElementById('deleteForm').action = '/entities/' + entityId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
