#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة بيانات جديدة مع البيانات الأساسية
"""

import os
import sys
from datetime import datetime, date
from werkzeug.security import generate_password_hash

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db
from models import (
    User, UserRole, Department, Permission, Role, PermissionType,
    user_permissions, role_permissions
)

def create_fresh_database():
    """إنشاء قاعدة بيانات جديدة مع البيانات الأساسية"""

    print("🗑️ حذف قاعدة البيانات القديمة...")

    # حذف قواعد البيانات الموجودة
    db_files = [
        'correspondence.db',
        'instance/correspondence.db'
    ]

    for db_file in db_files:
        if os.path.exists(db_file):
            os.remove(db_file)
            print(f"   ✅ تم حذف {db_file}")

    # إنشاء مجلد instance إذا لم يكن موجوداً
    if not os.path.exists('instance'):
        os.makedirs('instance')
        print("   📁 تم إنشاء مجلد instance")

    print("\n🏗️ إنشاء قاعدة البيانات الجديدة...")

    app = create_app()
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        print("   ✅ تم إنشاء جميع الجداول")
        
        # إنشاء الصلاحيات الأساسية
        print("\n🔐 إنشاء الصلاحيات الأساسية...")
        permissions_data = [
            # صلاحيات الرسائل
            ('create_message', 'إنشاء رسالة', 'إنشاء رسائل جديدة', 'messages'),
            ('view_message', 'عرض الرسائل', 'عرض وقراءة الرسائل', 'messages'),
            ('edit_message', 'تعديل الرسائل', 'تعديل محتوى الرسائل', 'messages'),
            ('delete_message', 'حذف الرسائل', 'حذف الرسائل نهائياً', 'messages'),
            ('approve_message', 'اعتماد الرسائل', 'اعتماد أو رفض الرسائل', 'messages'),
            ('archive_message', 'أرشفة الرسائل', 'نقل الرسائل إلى الأرشيف', 'messages'),
            
            # صلاحيات المستخدمين
            ('create_user', 'إنشاء مستخدم', 'إضافة مستخدمين جدد', 'users'),
            ('view_user', 'عرض المستخدمين', 'عرض قائمة المستخدمين', 'users'),
            ('edit_user', 'تعديل المستخدمين', 'تعديل بيانات المستخدمين', 'users'),
            ('delete_user', 'حذف المستخدمين', 'حذف حسابات المستخدمين', 'users'),
            ('manage_roles', 'إدارة الأدوار', 'إدارة أدوار وصلاحيات المستخدمين', 'users'),
            
            # صلاحيات الأقسام
            ('create_department', 'إنشاء قسم', 'إضافة أقسام جديدة', 'departments'),
            ('view_department', 'عرض الأقسام', 'عرض قائمة الأقسام', 'departments'),
            ('edit_department', 'تعديل الأقسام', 'تعديل بيانات الأقسام', 'departments'),
            ('delete_department', 'حذف الأقسام', 'حذف الأقسام', 'departments'),
            
            # صلاحيات النظام
            ('view_reports', 'عرض التقارير', 'عرض تقارير النظام', 'system'),
            ('manage_system', 'إدارة النظام', 'إدارة إعدادات النظام', 'system'),
            ('view_logs', 'عرض السجلات', 'عرض سجلات النظام', 'system'),
            ('backup_system', 'نسخ احتياطي', 'إنشاء نسخ احتياطية', 'system'),
        ]
        
        for name, display_name, description, category in permissions_data:
            permission = Permission(
                name=name,
                display_name=display_name,
                description=description,
                category=category
            )
            db.session.add(permission)
        
        db.session.commit()
        print(f"   ✅ تم إنشاء {len(permissions_data)} صلاحية")
        
        # إنشاء الأدوار الأساسية
        print("\n👥 إنشاء الأدوار الأساسية...")
        
        # دور المدير العام
        admin_role = Role(
            name='system_admin',
            display_name='مدير النظام',
            description='مدير النظام الرئيسي - صلاحيات كاملة',
            is_system_role=True
        )
        db.session.add(admin_role)
        
        # دور المدير
        manager_role = Role(
            name='manager',
            display_name='مدير',
            description='مدير القسم - صلاحيات إدارية',
            is_system_role=True
        )
        db.session.add(manager_role)
        
        # دور السكرتير
        secretary_role = Role(
            name='secretary',
            display_name='سكرتير',
            description='سكرتير - صلاحيات متوسطة',
            is_system_role=True
        )
        db.session.add(secretary_role)
        
        # دور الموظف
        employee_role = Role(
            name='employee',
            display_name='موظف',
            description='موظف عادي - صلاحيات أساسية',
            is_system_role=True
        )
        db.session.add(employee_role)
        
        db.session.commit()
        
        # ربط الصلاحيات بالأدوار
        print("   🔗 ربط الصلاحيات بالأدوار...")
        
        # صلاحيات المدير العام (جميع الصلاحيات)
        all_permissions = Permission.query.all()
        for permission in all_permissions:
            admin_role.add_permission(permission)
        
        # صلاحيات المدير
        manager_permissions = [
            'create_message', 'view_message', 'edit_message', 'delete_message', 
            'approve_message', 'archive_message',
            'create_user', 'view_user', 'edit_user',
            'view_department', 'edit_department',
            'view_reports'
        ]
        for perm_name in manager_permissions:
            permission = Permission.query.filter_by(name=perm_name).first()
            if permission:
                manager_role.add_permission(permission)
        
        # صلاحيات السكرتير
        secretary_permissions = [
            'create_message', 'view_message', 'edit_message', 'archive_message',
            'view_user', 'edit_user',
            'view_department'
        ]
        for perm_name in secretary_permissions:
            permission = Permission.query.filter_by(name=perm_name).first()
            if permission:
                secretary_role.add_permission(permission)
        
        # صلاحيات الموظف
        employee_permissions = [
            'create_message', 'view_message', 'edit_message',
            'view_user', 'view_department'
        ]
        for perm_name in employee_permissions:
            permission = Permission.query.filter_by(name=perm_name).first()
            if permission:
                employee_role.add_permission(permission)
        
        db.session.commit()
        print("   ✅ تم ربط الصلاحيات بالأدوار")
        
        # إنشاء الأقسام الأساسية
        print("\n🏢 إنشاء الأقسام الأساسية...")
        departments_data = [
            ('الإدارة العامة', 'الإدارة العامة للمؤسسة'),
            ('الموارد البشرية', 'قسم الموارد البشرية'),
            ('المالية والمحاسبة', 'قسم المالية والمحاسبة'),
            ('تقنية المعلومات', 'قسم تقنية المعلومات'),
            ('الشؤون القانونية', 'قسم الشؤون القانونية'),
        ]

        created_count = 0
        for name, description in departments_data:
            # التحقق من عدم وجود القسم مسبقاً
            existing_dept = Department.query.filter_by(name=name).first()
            if not existing_dept:
                department = Department(
                    name=name,
                    description=description
                )
                db.session.add(department)
                created_count += 1

        db.session.commit()
        print(f"   ✅ تم إنشاء {created_count} قسم جديد")
        
        # إنشاء المستخدم الافتراضي
        print("\n👤 التحقق من المستخدم الافتراضي...")

        existing_admin = User.query.filter_by(username='admin').first()
        if not existing_admin:
            # البحث عن قسم الإدارة العامة
            admin_dept = Department.query.filter_by(name='الإدارة العامة').first()
            dept_id = admin_dept.id if admin_dept else 1

            admin_user = User(
                name='مدير النظام',
                username='admin',
                role=UserRole.ADMIN,
                department_id=dept_id
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("   ✅ تم إنشاء المستخدم الافتراضي")
        else:
            print("   ℹ️ المستخدم الافتراضي موجود مسبقاً")

        print("      👤 اسم المستخدم: admin")
        print("      🔐 كلمة المرور: admin123")
        
        print("\n✅ تم إنشاء قاعدة البيانات بنجاح!")
        print("🚀 يمكنك الآن تشغيل النظام باستخدام: python app.py")

if __name__ == '__main__':
    create_fresh_database()
