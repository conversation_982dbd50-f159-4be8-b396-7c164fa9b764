from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, User, Role, Permission, UserRole, PermissionType
from functools import wraps

permissions_bp = Blueprint('permissions', __name__, url_prefix='/permissions')

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        if not (current_user.role == UserRole.ADMIN or current_user.has_permission('manage_system')):
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

@permissions_bp.route('/')
@login_required
@admin_required
def index():
    """الصفحة الرئيسية لإدارة الصلاحيات"""
    roles = Role.query.all()
    permissions = Permission.query.all()
    users_count = User.query.count()
    
    # إحصائيات
    stats = {
        'total_roles': len(roles),
        'total_permissions': len(permissions),
        'total_users': users_count,
        'active_users': User.query.filter_by(is_active=True).count()
    }
    
    return render_template('permissions/index.html', 
                         roles=roles, 
                         permissions=permissions, 
                         stats=stats)

@permissions_bp.route('/roles')
@login_required
@admin_required
def list_roles():
    """قائمة الأدوار"""
    roles = Role.query.order_by(Role.name).all()
    return render_template('permissions/roles.html', roles=roles)

@permissions_bp.route('/roles/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_role():
    """إنشاء دور جديد"""
    if request.method == 'POST':
        name = request.form.get('name')
        display_name = request.form.get('display_name')
        description = request.form.get('description')
        permission_ids = request.form.getlist('permissions')
        
        if not name or not display_name:
            flash('اسم الدور والاسم المعروض مطلوبان', 'error')
            return redirect(url_for('permissions.new_role'))
        
        # التحقق من عدم وجود دور بنفس الاسم
        if Role.query.filter_by(name=name).first():
            flash('يوجد دور بهذا الاسم مسبقاً', 'error')
            return redirect(url_for('permissions.new_role'))
        
        # إنشاء الدور الجديد
        role = Role(
            name=name,
            display_name=display_name,
            description=description
        )
        
        # إضافة الصلاحيات
        for permission_id in permission_ids:
            permission = Permission.query.get(permission_id)
            if permission:
                role.add_permission(permission)
        
        db.session.add(role)
        db.session.commit()
        
        flash('تم إنشاء الدور بنجاح', 'success')
        return redirect(url_for('permissions.list_roles'))
    
    permissions = Permission.query.order_by(Permission.category, Permission.display_name).all()
    return render_template('permissions/new_role.html', permissions=permissions)

@permissions_bp.route('/roles/<int:role_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_role(role_id):
    """تعديل دور"""
    role = Role.query.get_or_404(role_id)
    
    if request.method == 'POST':
        role.display_name = request.form.get('display_name')
        role.description = request.form.get('description')
        permission_ids = request.form.getlist('permissions')
        
        if not role.display_name:
            flash('الاسم المعروض مطلوب', 'error')
            return redirect(url_for('permissions.edit_role', role_id=role_id))
        
        # تحديث الصلاحيات
        role.permissions.clear()
        for permission_id in permission_ids:
            permission = Permission.query.get(permission_id)
            if permission:
                role.add_permission(permission)
        
        db.session.commit()
        flash('تم تحديث الدور بنجاح', 'success')
        return redirect(url_for('permissions.list_roles'))
    
    permissions = Permission.query.order_by(Permission.category, Permission.display_name).all()
    return render_template('permissions/edit_role.html', role=role, permissions=permissions)

@permissions_bp.route('/roles/<int:role_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_role(role_id):
    """حذف دور"""
    role = Role.query.get_or_404(role_id)
    
    if role.is_system_role:
        flash('لا يمكن حذف أدوار النظام', 'error')
        return redirect(url_for('permissions.list_roles'))
    
    if len(role.users) > 0:
        flash('لا يمكن حذف دور مرتبط بمستخدمين', 'error')
        return redirect(url_for('permissions.list_roles'))
    
    db.session.delete(role)
    db.session.commit()
    
    flash('تم حذف الدور بنجاح', 'success')
    return redirect(url_for('permissions.list_roles'))

@permissions_bp.route('/permissions')
@login_required
@admin_required
def list_permissions():
    """قائمة الصلاحيات"""
    permissions = Permission.query.order_by(Permission.category, Permission.display_name).all()
    
    # تجميع الصلاحيات حسب الفئة
    grouped_permissions = {}
    for permission in permissions:
        if permission.category not in grouped_permissions:
            grouped_permissions[permission.category] = []
        grouped_permissions[permission.category].append(permission)
    
    return render_template('permissions/permissions.html', grouped_permissions=grouped_permissions)

@permissions_bp.route('/users/<int:user_id>/permissions', methods=['GET', 'POST'])
@login_required
@admin_required
def user_permissions(user_id):
    """إدارة صلاحيات مستخدم معين"""
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        # تحديث الدور المخصص
        role_id = request.form.get('role_id')
        if role_id:
            role = Role.query.get(role_id)
            user.custom_role = role
        else:
            user.custom_role = None
        
        # تحديث الصلاحيات الإضافية
        permission_ids = request.form.getlist('additional_permissions')
        user.additional_permissions.clear()
        
        for permission_id in permission_ids:
            permission = Permission.query.get(permission_id)
            if permission:
                user.add_permission(permission)
        
        db.session.commit()
        flash('تم تحديث صلاحيات المستخدم بنجاح', 'success')
        return redirect(url_for('permissions.user_permissions', user_id=user_id))
    
    roles = Role.query.filter_by(is_active=True).all()
    permissions = Permission.query.order_by(Permission.category, Permission.display_name).all()
    user_permissions_list = user.get_all_permissions()
    
    return render_template('permissions/user_permissions.html', 
                         user=user, 
                         roles=roles, 
                         permissions=permissions,
                         user_permissions=user_permissions_list)

@permissions_bp.route('/init-permissions', methods=['POST'])
@login_required
@admin_required
def init_permissions():
    """تهيئة الصلاحيات الافتراضية"""
    try:
        # إنشاء الصلاحيات الافتراضية
        default_permissions = [
            # صلاحيات الرسائل
            ('create_message', 'إنشاء رسالة', 'إمكانية إنشاء رسائل جديدة', 'الرسائل'),
            ('view_message', 'عرض الرسائل', 'إمكانية عرض الرسائل', 'الرسائل'),
            ('edit_message', 'تعديل الرسائل', 'إمكانية تعديل الرسائل', 'الرسائل'),
            ('delete_message', 'حذف الرسائل', 'إمكانية حذف الرسائل', 'الرسائل'),
            ('approve_message', 'اعتماد الرسائل', 'إمكانية اعتماد الرسائل', 'الرسائل'),
            ('archive_message', 'أرشفة الرسائل', 'إمكانية أرشفة الرسائل', 'الرسائل'),
            
            # صلاحيات المستخدمين
            ('create_user', 'إنشاء مستخدم', 'إمكانية إنشاء مستخدمين جدد', 'المستخدمين'),
            ('view_user', 'عرض المستخدمين', 'إمكانية عرض بيانات المستخدمين', 'المستخدمين'),
            ('edit_user', 'تعديل المستخدمين', 'إمكانية تعديل بيانات المستخدمين', 'المستخدمين'),
            ('delete_user', 'حذف المستخدمين', 'إمكانية حذف المستخدمين', 'المستخدمين'),
            ('manage_roles', 'إدارة الأدوار', 'إمكانية إدارة أدوار المستخدمين', 'المستخدمين'),
            
            # صلاحيات الأقسام
            ('create_department', 'إنشاء قسم', 'إمكانية إنشاء أقسام جديدة', 'الأقسام'),
            ('view_department', 'عرض الأقسام', 'إمكانية عرض الأقسام', 'الأقسام'),
            ('edit_department', 'تعديل الأقسام', 'إمكانية تعديل الأقسام', 'الأقسام'),
            ('delete_department', 'حذف الأقسام', 'إمكانية حذف الأقسام', 'الأقسام'),
            
            # صلاحيات النظام
            ('view_reports', 'عرض التقارير', 'إمكانية عرض التقارير', 'النظام'),
            ('manage_system', 'إدارة النظام', 'إمكانية إدارة إعدادات النظام', 'النظام'),
            ('view_logs', 'عرض السجلات', 'إمكانية عرض سجلات النظام', 'النظام'),
            ('backup_system', 'نسخ احتياطي', 'إمكانية إنشاء نسخ احتياطية', 'النظام'),
        ]
        
        for name, display_name, description, category in default_permissions:
            if not Permission.query.filter_by(name=name).first():
                permission = Permission(
                    name=name,
                    display_name=display_name,
                    description=description,
                    category=category
                )
                db.session.add(permission)
        
        db.session.commit()
        flash('تم تهيئة الصلاحيات الافتراضية بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تهيئة الصلاحيات: {str(e)}', 'error')
    
    return redirect(url_for('permissions.index'))
