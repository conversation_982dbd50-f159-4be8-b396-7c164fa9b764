<!DOCTYPE html>
<html lang="{{ current_language() }}" dir="{{ text_direction() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ t('dashboard') }} - نظام المراسلات الإلكترونية{% endblock %}</title>

    <!-- Bootstrap CSS -->
    {% if current_language() == 'ar' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    {% block extra_css %}{% endblock %}

    <style>
        .language-switcher {
            position: fixed;
            top: 10px;
            {% if current_language() == 'ar' %}left: 10px;{% else %}right: 10px;{% endif %}
            z-index: 1050;
        }
        .language-switcher .dropdown-toggle {
            background: rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #fff;
            font-size: 12px;
            padding: 5px 10px;
        }
        .language-switcher .dropdown-toggle:hover {
            background: rgba(0,0,0,0.2);
        }
        .approval-section {
            border-top: 2px solid #dee2e6;
            margin-top: 2rem;
            padding-top: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 0.375rem;
        }
        .approval-badge {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }

        /* Custom Navigation Styles */
        .custom-navbar {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 3px solid rgba(255,255,255,0.1);
        }

        .navbar-brand {
            font-weight: bold !important;
            font-size: 1.3rem !important;
            color: white !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 5px;
            margin: 0 2px;
            padding: 8px 12px !important;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .navbar-nav .dropdown-menu {
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            border-radius: 8px;
            margin-top: 5px;
        }

        .navbar-nav .dropdown-item {
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .navbar-nav .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        .navbar-toggler {
            border: 2px solid white;
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* Language and User Menu Styling */
        .navbar-nav .nav-item:last-child .dropdown-menu {
            right: 0;
            left: auto;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Responsive adjustments */
        @media (max-width: 991px) {
            .navbar-nav .nav-link {
                margin: 2px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark custom-navbar">
        <div class="container-fluid">
            <a class="navbar-brand text-white fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-envelope me-2"></i>
                نظام المراسلات الإلكترونية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope me-1"></i>الرسائل
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('messages.list_messages') }}">جميع الرسائل</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.new_message') }}">رسالة جديدة</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.list_messages', type='incoming') }}">الرسائل الواردة</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.list_messages', type='outgoing') }}">الرسائل الصادرة</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.internal_messages') }}">المراسلات الداخلية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.my_internal_messages') }}">مراسلاتي الداخلية</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.list_files') }}">
                                <i class="fas fa-folder-open me-1"></i>إدارة الملفات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('archive.index') }}">
                                <i class="fas fa-archive me-1"></i>الأرشيف
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('entities.list_entities') }}">
                                <i class="fas fa-building me-1"></i>الجهات
                            </a></li>
                            {% if current_user.is_admin() or current_user.is_director() %}
                            <li><a class="dropdown-item" href="{{ url_for('settings.index') }}">
                                <i class="fas fa-cog me-1"></i>الإعدادات
                            </a></li>
                            {% endif %}
                            {% if current_user.is_manager() or current_user.is_secretary() or current_user.is_supervisor() or current_user.is_department_head() or current_user.is_director() or current_user.is_admin() %}
                            <li><a class="dropdown-item" href="{{ url_for('messages.pending_approval') }}">
                                <i class="fas fa-stamp me-2"></i>{{ t('awaiting_approval') }}
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% if current_user.can_manage_users() %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>المستخدمون
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('users.list_users') }}">جميع المستخدمين</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('users.new_user') }}">مستخدم جديد</a></li>
                            {% if current_user.role == current_user.role.__class__.ADMIN or (current_user.has_permission and current_user.has_permission('manage_system')) %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('permissions.index') }}">
                                <i class="fas fa-shield-alt me-1"></i>إدارة الصلاحيات
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-building me-1"></i>الأقسام
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('departments.list_departments') }}">جميع الأقسام</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('departments.new_department') }}">قسم جديد</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link text-white" href="{{ url_for('search') }}">
                            <i class="fas fa-search me-1"></i>البحث
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            {{ language_name() }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('change_language', language='ar') }}">
                                <i class="fas fa-flag me-2"></i>العربية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('change_language', language='en') }}">
                                <i class="fas fa-flag me-2"></i>English
                            </a></li>
                        </ul>
                    </li>

                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ current_user.name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user-circle me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('users.signature') }}">
                                <i class="fas fa-signature me-2"></i>التوقيع الإلكتروني
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content -->
    <main class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 نظام المراسلات الإلكترونية. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
