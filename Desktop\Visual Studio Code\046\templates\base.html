<!DOCTYPE html>
<html lang="{{ current_language() }}" dir="{{ text_direction() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ t('dashboard') }} - نظام المراسلات الإلكترونية{% endblock %}</title>

    <!-- Bootstrap CSS -->
    {% if current_language() == 'ar' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    {% block extra_css %}{% endblock %}

    <style>
        .language-switcher {
            position: fixed;
            top: 10px;
            {% if current_language() == 'ar' %}left: 10px;{% else %}right: 10px;{% endif %}
            z-index: 1050;
        }
        .language-switcher .dropdown-toggle {
            background: rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #fff;
            font-size: 12px;
            padding: 5px 10px;
        }
        .language-switcher .dropdown-toggle:hover {
            background: rgba(0,0,0,0.2);
        }
        .approval-section {
            border-top: 2px solid #dee2e6;
            margin-top: 2rem;
            padding-top: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 0.375rem;
        }
        .approval-badge {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <div class="dropdown">
            <button class="btn btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-globe me-1"></i>
                {{ language_name() }}
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ url_for('change_language', language='ar') }}">
                    <i class="fas fa-flag me-2"></i>العربية
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('change_language', language='en') }}">
                    <i class="fas fa-flag me-2"></i>English
                </a></li>
            </ul>
        </div>
    </div>

    <!-- Navigation -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-envelope me-2"></i>
                نظام المراسلات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope me-1"></i>الرسائل
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('messages.list_messages') }}">جميع الرسائل</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.new_message') }}">رسالة جديدة</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.list_messages', type='incoming') }}">الرسائل الواردة</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.list_messages', type='outgoing') }}">الرسائل الصادرة</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.internal_messages') }}">المراسلات الداخلية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.my_internal_messages') }}">مراسلاتي الداخلية</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.list_files') }}">
                                <i class="fas fa-folder-open me-1"></i>إدارة الملفات
                            </a></li>
                            {% if current_user.is_manager() or current_user.is_secretary() %}
                            <li><a class="dropdown-item" href="{{ url_for('messages.pending_approval') }}">
                                <i class="fas fa-stamp me-2"></i>{{ t('awaiting_approval') }}
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% if current_user.can_manage_users() %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>المستخدمون
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('users.list_users') }}">جميع المستخدمين</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('users.new_user') }}">مستخدم جديد</a></li>
                            {% if current_user.role == current_user.role.__class__.ADMIN or (current_user.has_permission and current_user.has_permission('manage_system')) %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('permissions.index') }}">
                                <i class="fas fa-shield-alt me-1"></i>إدارة الصلاحيات
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-building me-1"></i>الأقسام
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('departments.list_departments') }}">جميع الأقسام</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('departments.new_department') }}">قسم جديد</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search') }}">
                            <i class="fas fa-search me-1"></i>البحث
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ current_user.name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">تغيير كلمة المرور</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content -->
    <main class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 نظام المراسلات الإلكترونية. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
