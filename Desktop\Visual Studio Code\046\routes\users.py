from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file, current_app
from flask_login import login_required, current_user
from werkzeug.security import generate_password_hash
from werkzeug.utils import secure_filename
from models import db, User, UserRole, Department
import os
import json
from datetime import datetime

users_bp = Blueprint('users', __name__)

@users_bp.route('/')
@login_required
def list_users():
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لإدارة المستخدمين', 'error')
        return redirect(url_for('index'))
    
    users = User.query.order_by(User.name).all()
    return render_template('users/list.html', users=users)

@users_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_user():
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لإنشاء مستخدمين جدد', 'error')
        return redirect(url_for('index'))

    # Get departments for dropdown
    departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()

    if request.method == 'POST':
        name = request.form.get('name')
        username = request.form.get('username')
        password = request.form.get('password')
        role = request.form.get('role')
        department_id = request.form.get('department_id')
        
        # Validation
        if not all([name, username, password, role]):
            flash('يرجى ملء جميع الحقول', 'error')
            return render_template('users/new.html', departments=departments)
        
        # Check if username already exists
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            flash('اسم المستخدم موجود مسبقاً', 'error')
            return render_template('users/new.html', departments=departments)
        
        # Validate role
        try:
            user_role = UserRole(role)
        except ValueError:
            flash('دور المستخدم غير صحيح', 'error')
            return render_template('users/new.html', departments=departments)
        
        # Only managers can create other managers
        if user_role == UserRole.MANAGER and not current_user.is_manager():
            flash('فقط المدير يمكنه إنشاء مدراء آخرين', 'error')
            return render_template('users/new.html', departments=departments)
        
        # Create user
        user = User(
            name=name,
            username=username,
            role=user_role,
            department_id=int(department_id) if department_id else None
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        flash('تم إنشاء المستخدم بنجاح', 'success')
        return redirect(url_for('users.list_users'))
    
    return render_template('users/new.html', departments=departments)

@users_bp.route('/<int:id>')
@login_required
def view_user(id):
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لعرض تفاصيل المستخدمين', 'error')
        return redirect(url_for('index'))
    
    user = User.query.get_or_404(id)
    return render_template('users/view.html', user=user)

@users_bp.route('/signature')
@login_required
def signature():
    """صفحة إدارة التوقيع الإلكتروني"""
    return render_template('users/signature.html')

@users_bp.route('/signature/digital', methods=['POST'])
@login_required
def save_digital_signature():
    """حفظ التوقيع الرقمي"""
    try:
        data = request.get_json()
        signature_data = data.get('signature_data')

        if not signature_data:
            return jsonify({'success': False, 'message': 'لا توجد بيانات توقيع'})

        # Save digital signature
        signature_info = {
            'data': signature_data,
            'created_at': datetime.utcnow().isoformat(),
            'type': 'digital'
        }

        current_user.set_digital_signature(signature_info)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حفظ التوقيع بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@users_bp.route('/signature/upload', methods=['POST'])
@login_required
def upload_signature():
    """رفع صورة التوقيع"""
    try:
        if 'signature_file' not in request.files:
            flash('لم يتم اختيار ملف', 'error')
            return redirect(url_for('users.signature'))

        file = request.files['signature_file']
        if file.filename == '':
            flash('لم يتم اختيار ملف', 'error')
            return redirect(url_for('users.signature'))

        # Check file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            flash('نوع الملف غير مدعوم. يرجى استخدام PNG, JPG, أو GIF', 'error')
            return redirect(url_for('users.signature'))

        # Check file size (2MB max)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > 2 * 1024 * 1024:  # 2MB
            flash('حجم الملف كبير جداً. الحد الأقصى 2MB', 'error')
            return redirect(url_for('users.signature'))

        # Create signatures directory if it doesn't exist
        signatures_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'signatures')
        os.makedirs(signatures_dir, exist_ok=True)

        # Generate unique filename
        filename = secure_filename(file.filename)
        name, ext = os.path.splitext(filename)
        unique_filename = f"signature_{current_user.id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}{ext}"
        file_path = os.path.join(signatures_dir, unique_filename)

        # Remove old signature file if exists
        if current_user.signature_type == 'image' and current_user.signature_data:
            old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], current_user.signature_data)
            if os.path.exists(old_file_path):
                try:
                    os.remove(old_file_path)
                except:
                    pass

        # Save file
        file.save(file_path)

        # Update user signature
        relative_path = os.path.join('signatures', unique_filename)
        current_user.set_image_signature(relative_path, filename)
        db.session.commit()

        flash('تم رفع التوقيع بنجاح', 'success')
        return redirect(url_for('users.signature'))

    except Exception as e:
        flash(f'حدث خطأ أثناء رفع التوقيع: {str(e)}', 'error')
        return redirect(url_for('users.signature'))

@users_bp.route('/signature/remove', methods=['POST'])
@login_required
def remove_signature():
    """إزالة التوقيع الإلكتروني"""
    try:
        # Remove signature file if it's an image
        if current_user.signature_type == 'image' and current_user.signature_data:
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], current_user.signature_data)
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass

        # Remove signature from database
        current_user.remove_signature()
        db.session.commit()

        flash('تم إزالة التوقيع بنجاح', 'success')
        return redirect(url_for('users.signature'))

    except Exception as e:
        flash(f'حدث خطأ أثناء إزالة التوقيع: {str(e)}', 'error')
        return redirect(url_for('users.signature'))

@users_bp.route('/signature/image/<int:user_id>')
@login_required
def get_signature_image(user_id):
    """عرض صورة التوقيع"""
    try:
        user = User.query.get_or_404(user_id)

        if user.signature_type != 'image' or not user.signature_data:
            return '', 404

        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], user.signature_data)

        if not os.path.exists(file_path):
            return '', 404

        return send_file(file_path)

    except Exception as e:
        return '', 404

@users_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(id)
    departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()

    if request.method == 'POST':
        user.name = request.form.get('name')
        role = request.form.get('role')
        is_active = bool(request.form.get('is_active'))
        department_id = request.form.get('department_id')
        
        # Validate role
        try:
            user_role = UserRole(role)
        except ValueError:
            flash('دور المستخدم غير صحيح', 'error')
            return render_template('users/edit.html', user=user, departments=departments)
        
        # Only managers can assign manager role
        if user_role == UserRole.MANAGER and not current_user.is_manager():
            flash('فقط المدير يمكنه تعيين دور المدير', 'error')
            return render_template('users/edit.html', user=user, departments=departments)
        
        # Prevent deactivating the last manager
        if user.is_manager() and not is_active:
            active_managers = User.query.filter_by(role=UserRole.MANAGER, is_active=True).count()
            if active_managers <= 1:
                flash('لا يمكن إلغاء تفعيل آخر مدير في النظام', 'error')
                return render_template('users/edit.html', user=user, departments=departments)
        
        user.role = user_role
        user.is_active = is_active
        user.department_id = int(department_id) if department_id else None
        
        # Handle password change
        new_password = request.form.get('new_password')
        if new_password:
            if len(new_password) < 6:
                flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error')
                return render_template('users/edit.html', user=user, departments=departments)
            user.set_password(new_password)
        
        db.session.commit()
        flash('تم تحديث المستخدم بنجاح', 'success')
        return redirect(url_for('users.view_user', id=id))
    
    return render_template('users/edit.html', user=user, departments=departments)

@users_bp.route('/<int:id>/toggle_status', methods=['POST'])
@login_required
def toggle_user_status(id):
    if not current_user.is_manager():
        flash('ليس لديك صلاحية لتغيير حالة المستخدمين', 'error')
        return redirect(url_for('users.list_users'))
    
    user = User.query.get_or_404(id)
    
    # Prevent deactivating the last manager
    if user.is_manager() and user.is_active:
        active_managers = User.query.filter_by(role=UserRole.MANAGER, is_active=True).count()
        if active_managers <= 1:
            flash('لا يمكن إلغاء تفعيل آخر مدير في النظام', 'error')
            return redirect(url_for('users.list_users'))
    
    user.is_active = not user.is_active
    db.session.commit()
    
    status = 'تم تفعيل' if user.is_active else 'تم إلغاء تفعيل'
    flash(f'{status} المستخدم بنجاح', 'success')
    return redirect(url_for('users.list_users'))
