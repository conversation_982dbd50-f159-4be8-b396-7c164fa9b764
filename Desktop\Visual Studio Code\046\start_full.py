#!/usr/bin/env python3
"""
Start full Electronic Correspondence System with login
تشغيل النظام الكامل مع صفحة الدخول
"""

import os
import sys

def print_startup():
    print("=" * 60)
    print("🚀 تشغيل النظام الكامل - نظام المراسلات الإلكترونية")
    print("=" * 60)
    print("📧 جميع الميزات متاحة:")
    print("   ✅ صفحة الدخول")
    print("   ✅ إدارة الرسائل")
    print("   ✅ التوقيع الإلكتروني")
    print("   ✅ نظام الأرشيف")
    print("   ✅ إدارة الصلاحيات")
    print("=" * 60)

def main():
    print_startup()
    
    try:
        # Import the full app
        from app import create_app
        
        print("✅ تم تحميل النظام الكامل")
        
        # Create app
        app = create_app()
        
        print("✅ تم إنشاء التطبيق")
        print("🌐 صفحة الدخول: http://localhost:5000/auth/login")
        print("🏠 الصفحة الرئيسية: http://localhost:5000")
        print("🔑 بيانات الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔐 كلمة المرور: admin123")
        print("=" * 60)
        print("اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)
        
        # Start the full system
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود جميع الملفات المطلوبة")
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        print("💡 جرب تشغيل: python init_database.py أولاً")
        
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
