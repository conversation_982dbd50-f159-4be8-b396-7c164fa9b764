{% extends "base.html" %}

{% block title %}إنشاء دور جديد - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus me-2"></i>
                إنشاء دور جديد
            </h1>
            <div>
                <a href="{{ url_for('permissions.list_roles') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للأدوار
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدور
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الدور (تقني) *</label>
                        <input type="text" class="form-control" id="name" name="name" required
                               placeholder="مثال: custom_manager">
                        <div class="form-text">اسم تقني باللغة الإنجليزية بدون مسافات</div>
                    </div>

                    <div class="mb-3">
                        <label for="display_name" class="form-label">الاسم المعروض *</label>
                        <input type="text" class="form-control" id="display_name" name="display_name" required
                               placeholder="مثال: مدير مخصص">
                        <div class="form-text">الاسم الذي سيظهر للمستخدمين</div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label">وصف الدور</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف مختصر لمهام ومسؤوليات هذا الدور"></textarea>
                    </div>

                    <div class="mb-4">
                        <h6>الصلاحيات</h6>
                        <div class="form-text mb-3">اختر الصلاحيات التي تريد منحها لهذا الدور</div>
                        
                        {% if permissions %}
                        <!-- Group permissions by category -->
                        {% set categories = permissions | groupby('category') %}
                        {% for category, category_permissions in categories %}
                        <div class="card mb-3">
                            <div class="card-header">
                                <div class="form-check">
                                    <input class="form-check-input category-checkbox" type="checkbox" 
                                           id="category_{{ loop.index }}" 
                                           data-category="{{ category }}">
                                    <label class="form-check-label fw-bold" for="category_{{ loop.index }}">
                                        {{ category }}
                                    </label>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for permission in category_permissions %}
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" 
                                                   type="checkbox" 
                                                   name="permissions" 
                                                   value="{{ permission.id }}" 
                                                   id="permission_{{ permission.id }}"
                                                   data-category="{{ category }}">
                                            <label class="form-check-label" for="permission_{{ permission.id }}">
                                                <strong>{{ permission.display_name }}</strong>
                                                {% if permission.description %}
                                                <br><small class="text-muted">{{ permission.description }}</small>
                                                {% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لا توجد صلاحيات متاحة. يرجى تهيئة الصلاحيات الافتراضية أولاً.
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('permissions.list_roles') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إنشاء الدور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم أسماء واضحة ومفهومة للأدوار
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        امنح الصلاحيات الضرورية فقط
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اكتب وصف واضح لمهام الدور
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكنك تعديل الصلاحيات لاحقاً
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">
                    الأدوار المخصصة تسمح بمرونة أكبر في إدارة الصلاحيات. 
                    يمكنك إنشاء أدوار متخصصة لمهام محددة.
                </p>
                <p class="small text-muted">
                    بعد إنشاء الدور، يمكنك تعيينه للمستخدمين من صفحة إدارة المستخدمين.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle category checkboxes
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    
    categoryCheckboxes.forEach(function(categoryCheckbox) {
        categoryCheckbox.addEventListener('change', function() {
            const category = this.dataset.category;
            const permissionCheckboxes = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);
            
            permissionCheckboxes.forEach(function(permissionCheckbox) {
                permissionCheckbox.checked = categoryCheckbox.checked;
            });
        });
    });
    
    // Update category checkboxes when individual permissions change
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
    
    permissionCheckboxes.forEach(function(permissionCheckbox) {
        permissionCheckbox.addEventListener('change', function() {
            const category = this.dataset.category;
            const categoryCheckbox = document.querySelector(`.category-checkbox[data-category="${category}"]`);
            const categoryPermissions = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);
            
            let checkedCount = 0;
            categoryPermissions.forEach(function(cb) {
                if (cb.checked) checkedCount++;
            });
            
            if (checkedCount === 0) {
                categoryCheckbox.checked = false;
                categoryCheckbox.indeterminate = false;
            } else if (checkedCount === categoryPermissions.length) {
                categoryCheckbox.checked = true;
                categoryCheckbox.indeterminate = false;
            } else {
                categoryCheckbox.checked = false;
                categoryCheckbox.indeterminate = true;
            }
        });
    });
    
    // Generate technical name from display name
    const displayNameInput = document.getElementById('display_name');
    const nameInput = document.getElementById('name');
    
    displayNameInput.addEventListener('input', function() {
        if (!nameInput.value || nameInput.dataset.autoGenerated !== 'false') {
            const technicalName = this.value
                .toLowerCase()
                .replace(/[أ-ي]/g, '') // Remove Arabic characters
                .replace(/\s+/g, '_') // Replace spaces with underscores
                .replace(/[^a-z0-9_]/g, '') // Remove special characters
                .substring(0, 50); // Limit length
            
            nameInput.value = technicalName;
            nameInput.dataset.autoGenerated = 'true';
        }
    });
    
    nameInput.addEventListener('input', function() {
        this.dataset.autoGenerated = 'false';
    });
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.form-check-input:indeterminate {
    background-color: #6c757d;
    border-color: #6c757d;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

.card-header .form-check {
    margin-bottom: 0;
}

.permission-checkbox + label {
    cursor: pointer;
}

.category-checkbox + label {
    cursor: pointer;
    font-weight: bold;
}
</style>
{% endblock %}
