{% extends "base.html" %}

{% block title %}تعديل المستخدم {{ user.name }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-edit me-2"></i>
                تعديل المستخدم
            </h1>
            <div>
                <a href="{{ url_for('users.view_user', id=user.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمستخدم
                </a>
                <a href="{{ url_for('users.list_users') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-list me-2"></i>
                    قائمة المستخدمين
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تعديل بيانات المستخدم: {{ user.name }}</h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ user.name }}" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الاسم الكامل
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
                            <div class="form-text">
                                لا يمكن تغيير اسم المستخدم بعد الإنشاء
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">الدور الأساسي <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="EMPLOYEE" {% if user.role.value == 'EMPLOYEE' %}selected{% endif %}>موظف</option>
                                <option value="SECRETARY" {% if user.role.value == 'SECRETARY' %}selected{% endif %}>سكرتير</option>
                                <option value="SUPERVISOR" {% if user.role.value == 'SUPERVISOR' %}selected{% endif %}>مشرف</option>
                                {% if current_user.is_manager() or current_user.is_admin() %}
                                <option value="MANAGER" {% if user.role.value == 'MANAGER' %}selected{% endif %}>مدير</option>
                                <option value="DEPARTMENT_HEAD" {% if user.role.value == 'DEPARTMENT_HEAD' %}selected{% endif %}>رئيس قسم</option>
                                {% endif %}
                                {% if current_user.is_admin() %}
                                <option value="DIRECTOR" {% if user.role.value == 'DIRECTOR' %}selected{% endif %}>مدير عام</option>
                                <option value="ADMIN" {% if user.role.value == 'ADMIN' %}selected{% endif %}>مدير النظام</option>
                                {% endif %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار دور المستخدم
                            </div>
                            <div class="form-text">
                                الدور الأساسي يحدد الصلاحيات الافتراضية
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="custom_role_id" class="form-label">الدور المخصص</label>
                            <select class="form-select" id="custom_role_id" name="custom_role_id">
                                <option value="">لا يوجد دور مخصص</option>
                                {% for role in roles %}
                                <option value="{{ role.id }}"
                                        {% if user.custom_role and user.custom_role.id == role.id %}selected{% endif %}>
                                    {{ role.display_name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                الدور الحالي:
                                {% if user.custom_role %}
                                    <strong>{{ user.custom_role.display_name }}</strong>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="">لا يوجد قسم محدد</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}"
                                        {% if user.department_id == department.id %}selected{% endif %}>
                                    {{ department.name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                القسم الحالي:
                                {% if user.department %}
                                    <strong>{{ user.department.name }}</strong>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">الحالة</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                       {% if user.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    المستخدم نشط
                                </label>
                            </div>
                            <div class="form-text">
                                المستخدمون غير النشطين لا يمكنهم تسجيل الدخول
                            </div>
                        </div>
                    </div>


                    
                    <hr>
                    
                    <h6 class="mb-3">تغيير كلمة المرور (اختياري)</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" minlength="6">
                            <div class="form-text">
                                اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="confirm_new_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="confirm_new_password" name="confirm_new_password">
                            <div class="invalid-feedback">
                                كلمات المرور غير متطابقة
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong>
                        <ul class="mb-0 mt-2">
                            <li><strong>الموظف:</strong> يمكنه عرض وإنشاء الرسائل وتعديل رسائله فقط</li>
                            <li><strong>السكرتير:</strong> يمكنه إدارة المستخدمين والرسائل وأرشفتها</li>
                            <li><strong>المشرف:</strong> يمكنه اعتماد الرسائل وإدارة المستخدمين</li>
                            <li><strong>المدير:</strong> جميع الصلاحيات بما في ذلك حذف الرسائل والتقارير</li>
                            <li><strong>رئيس القسم:</strong> إدارة كاملة للقسم والمستخدمين والرسائل</li>
                            <li><strong>المدير العام:</strong> إدارة جميع الأقسام والمستخدمين والنظام</li>
                            <li><strong>مدير النظام:</strong> جميع صلاحيات النظام والإدارة والنسخ الاحتياطي</li>
                            <li><strong>الدور المخصص:</strong> يضيف صلاحيات إضافية للدور الأساسي</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التعديلات
                        </button>
                        <div>
                            <a href="{{ url_for('users.view_user', id=user.id) }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            {% if current_user.is_manager() %}
                            <a href="{{ url_for('permissions.user_permissions', user_id=user.id) }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-key me-2"></i>
                                إدارة الصلاحيات
                            </a>
                            {% endif %}
                            {% if current_user.is_manager() and user.id != current_user.id %}
                            <button type="button" class="btn btn-outline-danger" onclick="confirmStatusToggle()">
                                <i class="fas fa-{{ 'ban' if user.is_active else 'check' }} me-2"></i>
                                {{ 'إلغاء التفعيل' if user.is_active else 'تفعيل' }}
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </form>
                
                {% if current_user.is_manager() and user.id != current_user.id %}
                <!-- Hidden status toggle form -->
                <form id="statusForm" method="POST" action="{{ url_for('users.toggle_user_status', id=user.id) }}" style="display: none;">
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- User Info Card -->
<div class="row mt-4 justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">معلومات إضافية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <strong>تاريخ الإنشاء:</strong>
                        <p class="mb-0">{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-4">
                        <strong>عدد الرسائل:</strong>
                        <p class="mb-0">{{ user.created_messages.count() }}</p>
                    </div>
                    <div class="col-md-4">
                        <strong>آخر نشاط:</strong>
                        <p class="mb-0">غير متوفر</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmStatusToggle() {
    const isActive = {{ 'true' if user.is_active else 'false' }};
    const action = isActive ? 'إلغاء تفعيل' : 'تفعيل';
    const message = `هل أنت متأكد من ${action} هذا المستخدم؟`;
    
    if (confirm(message)) {
        document.getElementById('statusForm').submit();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    var newPasswordField = document.getElementById('new_password');
    var confirmPasswordField = document.getElementById('confirm_new_password');
    
    function validatePasswords() {
        if (newPasswordField.value && newPasswordField.value !== confirmPasswordField.value) {
            confirmPasswordField.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            confirmPasswordField.setCustomValidity('');
        }
    }
    
    newPasswordField.addEventListener('input', validatePasswords);
    confirmPasswordField.addEventListener('input', validatePasswords);
    
    // Show/hide confirm password field based on new password
    newPasswordField.addEventListener('input', function() {
        const confirmGroup = confirmPasswordField.closest('.mb-3');
        if (this.value) {
            confirmGroup.style.display = 'block';
            confirmPasswordField.required = true;
        } else {
            confirmGroup.style.display = 'none';
            confirmPasswordField.required = false;
            confirmPasswordField.value = '';
        }
    });
    
    // Initial state
    if (!newPasswordField.value) {
        confirmPasswordField.closest('.mb-3').style.display = 'none';
    }
});
</script>
{% endblock %}
