#!/usr/bin/env python3
"""
Test script to check imports
"""

try:
    print("Testing imports...")
    
    print("1. Importing Flask...")
    from flask import Flask
    print("   ✓ Flask imported successfully")
    
    print("2. Importing SQLAlchemy...")
    from flask_sqlalchemy import SQLAlchemy
    print("   ✓ SQLAlchemy imported successfully")
    
    print("3. Importing models...")
    import models
    print("   ✓ Models imported successfully")
    
    print("4. Testing app creation...")
    from app import create_app
    app = create_app()
    print("   ✓ App created successfully")
    
    print("5. Testing database...")
    with app.app_context():
        models.db.create_all()
        print("   ✓ Database tables created successfully")
    
    print("\n🎉 All tests passed!")
    
except Exception as e:
    print(f"\n❌ Error: {str(e)}")
    import traceback
    traceback.print_exc()
