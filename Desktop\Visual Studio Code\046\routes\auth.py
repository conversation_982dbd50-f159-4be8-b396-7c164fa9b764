from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_user, logout_user, login_required, current_user
from models import db, User, Message, Department
from werkzeug.security import check_password_hash
from datetime import datetime, timedelta

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = bool(request.form.get('remember'))
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user, remember=remember)
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile')
@login_required
def profile():
    # إحصائيات المستخدم - استخدام استعلامات مباشرة لتجنب مشاكل AppenderQuery
    from models import Message, MessageType, ApprovalStatus
    from datetime import datetime

    user_stats = {
        'total_messages': Message.query.filter_by(created_by=current_user.id).count(),
        'incoming_messages': Message.query.filter_by(
            created_by=current_user.id,
            message_type=MessageType.INCOMING
        ).count(),
        'outgoing_messages': Message.query.filter_by(
            created_by=current_user.id,
            message_type=MessageType.OUTGOING
        ).count(),
        'internal_messages': Message.query.filter_by(
            created_by=current_user.id,
            message_type=MessageType.INTERNAL
        ).count(),
        'this_month_messages': Message.query.filter(
            Message.created_by == current_user.id,
            Message.date_created >= datetime.now().replace(day=1)
        ).count(),
        'pending_approval': Message.query.filter_by(
            created_by=current_user.id,
            requires_approval=True,
            approval_status=ApprovalStatus.PENDING
        ).count()
    }

    # الرسائل الحديثة
    recent_messages = Message.query.filter_by(
        created_by=current_user.id
    ).order_by(Message.date_created.desc()).limit(5).all()

    # الرسائل المكلف بها (للرسائل الداخلية)
    assigned_messages = Message.query.filter_by(
        assigned_to=current_user.id,
        requires_response=True
    ).order_by(Message.date_created.desc()).limit(5).all()

    # معلومات القسم
    department_info = None
    if current_user.department:
        department_info = {
            'name': current_user.department.name,
            'description': current_user.department.description,
            'manager': current_user.department.manager.name if current_user.department.manager else 'غير محدد',
            'employees_count': len(current_user.department.employees)
        }

    # الصلاحيات
    user_permissions = current_user.get_all_permissions()

    return render_template('auth/profile.html',
                         user_stats=user_stats,
                         recent_messages=recent_messages,
                         assigned_messages=assigned_messages,
                         department_info=department_info,
                         user_permissions=user_permissions)

@auth_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        if not current_password or not new_password or not confirm_password:
            flash('يرجى ملء جميع الحقول', 'error')
            return render_template('auth/change_password.html')
        
        if not current_user.check_password(current_password):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return render_template('auth/change_password.html')
        
        if new_password != confirm_password:
            flash('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'error')
            return render_template('auth/change_password.html')
        
        if len(new_password) < 6:
            flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error')
            return render_template('auth/change_password.html')
        
        current_user.set_password(new_password)
        db.session.commit()
        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('auth.profile'))
    
    return render_template('auth/change_password.html')

@auth_bp.route('/edit_profile', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """تعديل الملف الشخصي"""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()

        # التحقق من صحة البيانات
        if not name:
            flash('يرجى إدخال الاسم', 'error')
            return render_template('auth/edit_profile.html')

        # تحديث البيانات
        current_user.name = name

        try:
            db.session.commit()
            flash('تم تحديث الملف الشخصي بنجاح', 'success')
            return redirect(url_for('auth.profile'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الملف الشخصي', 'error')
            return render_template('auth/edit_profile.html')

    return render_template('auth/edit_profile.html')
