#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تعديل الأدوار
"""

import requests
import sys

def test_role_edit():
    """اختبار تعديل الأدوار"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة قائمة الأدوار
        print("\n📋 اختبار صفحة قائمة الأدوار...")
        response = session.get(f"{base_url}/permissions/roles")
        if response.status_code == 200:
            print("✅ صفحة قائمة الأدوار تعمل")
            
            content = response.text
            if 'تعديل' in content and 'edit' in content:
                print("✅ تم العثور على أزرار التعديل في القائمة")
            else:
                print("⚠️ لم يتم العثور على أزرار التعديل")
                
            # البحث عن روابط التعديل
            if '/permissions/roles/' in content and '/edit' in content:
                print("✅ تم العثور على روابط التعديل")
            else:
                print("⚠️ لم يتم العثور على روابط التعديل")
        else:
            print(f"❌ فشل في الوصول لقائمة الأدوار: {response.status_code}")
            return False
        
        # اختبار تعديل دور موجود (نحاول تعديل الدور الأول)
        print("\n✏️ اختبار صفحة تعديل الدور...")
        
        # جرب تعديل الأدوار من 1 إلى 5
        edit_success = False
        for role_id in range(1, 6):
            response = session.get(f"{base_url}/permissions/roles/{role_id}/edit")
            if response.status_code == 200:
                print(f"✅ صفحة تعديل الدور {role_id} تعمل")
                
                content = response.text
                if 'تعديل الدور' in content:
                    print("✅ تم العثور على محتوى صفحة التعديل")
                    
                if 'display_name' in content:
                    print("✅ تم العثور على حقل الاسم المعروض")
                    
                if 'permissions' in content:
                    print("✅ تم العثور على قائمة الصلاحيات")
                    
                if 'حفظ التغييرات' in content:
                    print("✅ تم العثور على زر الحفظ")
                
                edit_success = True
                
                # اختبار تحديث الدور
                print(f"\n💾 اختبار تحديث الدور {role_id}...")
                update_data = {
                    'display_name': f'دور محدث {role_id}',
                    'description': f'وصف محدث للدور {role_id}',
                    'permissions': ['1', '2']  # صلاحيات تجريبية
                }
                
                response = session.post(f"{base_url}/permissions/roles/{role_id}/edit", data=update_data)
                if response.status_code == 200:
                    print(f"✅ تم تحديث الدور {role_id} بنجاح")
                else:
                    print(f"⚠️ مشكلة في تحديث الدور {role_id}: {response.status_code}")
                
                break
            elif response.status_code == 404:
                print(f"⚠️ الدور {role_id} غير موجود")
            else:
                print(f"❌ خطأ في الوصول لتعديل الدور {role_id}: {response.status_code}")
        
        if not edit_success:
            print("❌ لم يتم العثور على أي دور قابل للتعديل")
            return False
        
        # اختبار إنشاء دور جديد للتأكد من أن التعديل سيعمل عليه
        print("\n➕ اختبار إنشاء دور جديد...")
        new_role_data = {
            'name': 'test_edit_role',
            'display_name': 'دور اختبار التعديل',
            'description': 'دور لاختبار وظيفة التعديل',
            'permissions': ['1']
        }
        
        response = session.post(f"{base_url}/permissions/roles/new", data=new_role_data)
        if response.status_code == 200:
            print("✅ تم إنشاء دور جديد للاختبار")
            
            # الآن نحاول تعديل الدور الجديد
            print("\n✏️ اختبار تعديل الدور الجديد...")
            
            # نحتاج للعثور على معرف الدور الجديد
            response = session.get(f"{base_url}/permissions/roles")
            if response.status_code == 200:
                content = response.text
                # البحث عن الدور الجديد في المحتوى
                if 'test_edit_role' in content:
                    print("✅ تم العثور على الدور الجديد في القائمة")
                    
                    # محاولة تعديل الدور الجديد (نجرب معرفات مختلفة)
                    for test_role_id in range(1, 20):
                        response = session.get(f"{base_url}/permissions/roles/{test_role_id}/edit")
                        if response.status_code == 200 and 'test_edit_role' in response.text:
                            print(f"✅ تم العثور على صفحة تعديل الدور الجديد (معرف: {test_role_id})")
                            
                            # اختبار تحديث الدور الجديد
                            update_data = {
                                'display_name': 'دور اختبار التعديل - محدث',
                                'description': 'دور محدث لاختبار وظيفة التعديل',
                                'permissions': ['1', '2', '3']
                            }
                            
                            response = session.post(f"{base_url}/permissions/roles/{test_role_id}/edit", data=update_data)
                            if response.status_code == 200:
                                print("✅ تم تحديث الدور الجديد بنجاح")
                            else:
                                print(f"⚠️ مشكلة في تحديث الدور الجديد: {response.status_code}")
                            break
        else:
            print(f"⚠️ مشكلة في إنشاء دور جديد: {response.status_code}")
        
        print("\n🎉 تم اكتمال اختبار تعديل الأدوار!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار تعديل الأدوار")
    print("=" * 60)
    
    success = test_role_edit()
    
    if success:
        print("\n✅ اختبار تعديل الأدوار نجح!")
        print("\n📋 النتائج:")
        print("  • صفحة قائمة الأدوار تعمل")
        print("  • أزرار التعديل موجودة ومرئية")
        print("  • صفحة تعديل الدور تعمل")
        print("  • نموذج التعديل مكتمل")
        print("  • عملية التحديث تعمل")
        print("\n🌐 للوصول لإدارة الأدوار:")
        print("  http://localhost:8585/permissions/roles")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        sys.exit(0)
    else:
        print("\n❌ فشل في اختبار تعديل الأدوار!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم")
        print("  • صحة routes التعديل")
        print("  • وجود قوالب التعديل")
        sys.exit(1)
