import os
from datetime import datetime, date
from flask import Blueprint, render_template, request, redirect, url_for, flash, send_file, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models import db, Message, MessageType, User, Department, InternalMessageResponse, ApprovalStatus
try:
    from utils.pdf_generator import generate_message_pdf
    from utils.file_handler import allowed_file, save_uploaded_file
except ImportError:
    # Fallback functions if utils are not available
    def generate_message_pdf(message):
        return None
    def allowed_file(filename):
        return True
    def save_uploaded_file(file, registration_number):
        return None, None

messages_bp = Blueprint('messages', __name__)

@messages_bp.route('/')
@login_required
def list_messages():
    page = request.args.get('page', 1, type=int)
    message_type = request.args.get('type')
    year = request.args.get('year', type=int)
    
    messages = Message.query
    
    if message_type:
        messages = messages.filter_by(message_type=message_type)
    
    if year:
        messages = messages.filter_by(year=year)
    
    messages = messages.order_by(Message.date_created.desc()).paginate(
        page=page, per_page=current_app.config['MESSAGES_PER_PAGE'], error_out=False
    )
    
    return render_template('messages/list.html', messages=messages)

@messages_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_message():
    # Get departments for dropdowns
    departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()
    users = User.query.filter_by(is_active=True).order_by(User.name).all()

    if request.method == 'POST':
        message_type = request.form.get('message_type')
        registration_number = request.form.get('registration_number')
        destination = request.form.get('destination')
        subject = request.form.get('subject')
        content = request.form.get('content')
        message_date = request.form.get('message_date')
        department = request.form.get('department')
        priority = request.form.get('priority', 'normal')
        
        # Validation
        if not all([message_type, registration_number, destination, subject, message_date]):
            flash('يرجى ملء جميع الحقول المطلوبة', 'error')
            return render_template('messages/new.html')
        
        # Check if registration number already exists
        existing_message = Message.query.filter_by(registration_number=registration_number).first()
        if existing_message:
            flash('رقم التسجيل موجود مسبقاً', 'error')
            return render_template('messages/new.html')
        
        # Parse date
        try:
            message_date = datetime.strptime(message_date, '%Y-%m-%d').date()
        except ValueError:
            flash('تاريخ غير صحيح', 'error')
            return render_template('messages/new.html')
        
        # Handle file upload
        attachment_path = None
        attachment_name = None
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                attachment_path, attachment_name = save_uploaded_file(file, registration_number)

        # Create message
        message = Message(
            message_type=MessageType(message_type),
            registration_number=registration_number,
            destination=destination,
            subject=subject,
            content=content,
            message_date=message_date,
            department=department,
            priority=priority,
            attachment_path=attachment_path,
            attachment_name=attachment_name,
            created_by=current_user.id
        )

        # Handle internal message specific fields
        if message_type == 'internal':
            message.from_department = request.form.get('from_department')
            message.to_department = request.form.get('to_department')
            assigned_to_id = request.form.get('assigned_to')
            if assigned_to_id:
                message.assigned_to = int(assigned_to_id)

            due_date = request.form.get('due_date')
            if due_date:
                message.due_date = datetime.strptime(due_date, '%Y-%m-%d').date()

            message.is_urgent = bool(request.form.get('is_urgent'))
            message.requires_response = bool(request.form.get('requires_response'))

        # Handle approval requirements
        requires_approval = bool(request.form.get('requires_approval'))
        if requires_approval:
            message.requires_approval = True
            message.approval_status = ApprovalStatus.PENDING
        else:
            message.approval_status = ApprovalStatus.AUTO_APPROVED
        
        db.session.add(message)
        db.session.commit()
        
        flash('تم إنشاء الرسالة بنجاح', 'success')
        return redirect(url_for('messages.view_message', id=message.id))

    return render_template('messages/new.html', departments=departments, users=users)

@messages_bp.route('/<int:id>')
@login_required
def view_message(id):
    message = Message.query.get_or_404(id)
    return render_template('messages/view.html', message=message)

@messages_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_message(id):
    message = Message.query.get_or_404(id)
    
    # Check permissions
    if not current_user.is_manager() and message.created_by != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذه الرسالة', 'error')
        return redirect(url_for('messages.view_message', id=id))
    
    if request.method == 'POST':
        message.destination = request.form.get('destination')
        message.subject = request.form.get('subject')
        message.content = request.form.get('content')
        message.department = request.form.get('department')
        message.priority = request.form.get('priority', 'normal')
        
        # Handle new attachment
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                # Remove old attachment if exists
                if message.attachment_path:
                    old_path = os.path.join(current_app.config['UPLOAD_FOLDER'], message.attachment_path)
                    if os.path.exists(old_path):
                        os.remove(old_path)
                
                attachment_path, attachment_name = save_uploaded_file(file, message.registration_number)
                message.attachment_path = attachment_path
                message.attachment_name = attachment_name
        
        db.session.commit()
        flash('تم تحديث الرسالة بنجاح', 'success')
        return redirect(url_for('messages.view_message', id=id))
    
    return render_template('messages/edit.html', message=message)

@messages_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete_message(id):
    if not current_user.can_delete_messages():
        flash('ليس لديك صلاحية لحذف الرسائل', 'error')
        return redirect(url_for('messages.view_message', id=id))
    
    message = Message.query.get_or_404(id)
    
    # Remove attachment file if exists
    if message.attachment_path:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], message.attachment_path)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    db.session.delete(message)
    db.session.commit()
    
    flash('تم حذف الرسالة بنجاح', 'success')
    return redirect(url_for('messages.list_messages'))

@messages_bp.route('/<int:id>/pdf')
@login_required
def download_pdf(id):
    message = Message.query.get_or_404(id)
    try:
        pdf_path = generate_message_pdf(message)
        return send_file(pdf_path, as_attachment=True, download_name=f'message_{message.registration_number}.pdf')
    except Exception as e:
        flash(f'خطأ في إنشاء ملف PDF: {str(e)}', 'error')
        return redirect(url_for('messages.view_message', id=id))



@messages_bp.route('/internal')
@login_required
def internal_messages():
    """عرض المراسلات الداخلية"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status')
    department_filter = request.args.get('department')

    messages = Message.query.filter_by(message_type='internal')

    if status_filter == 'pending':
        messages = messages.filter_by(requires_response=True).filter(Message.responses == None)
    elif status_filter == 'urgent':
        messages = messages.filter_by(is_urgent=True)
    elif status_filter == 'overdue':
        messages = messages.filter(Message.due_date < date.today())

    if department_filter:
        messages = messages.filter(
            db.or_(
                Message.from_department == department_filter,
                Message.to_department == department_filter
            )
        )

    messages = messages.order_by(Message.date_created.desc()).paginate(
        page=page, per_page=current_app.config['MESSAGES_PER_PAGE'], error_out=False
    )

    # Get departments for filter
    departments = Department.query.filter_by(is_active=True).all()

    return render_template('messages/internal.html', messages=messages, departments=departments)

@messages_bp.route('/<int:id>/respond', methods=['GET', 'POST'])
@login_required
def respond_internal(id):
    """الرد على المراسلة الداخلية"""
    message = Message.query.get_or_404(id)

    if not message.is_internal():
        flash('هذه الميزة متاحة للمراسلات الداخلية فقط', 'error')
        return redirect(url_for('messages.view_message', id=id))

    if request.method == 'POST':
        response_content = request.form.get('response_content')

        if not response_content:
            flash('يرجى إدخال محتوى الرد', 'error')
            return render_template('messages/respond.html', message=message)

        # Handle file upload for response
        attachment_path = None
        attachment_name = None
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                attachment_path, attachment_name = save_uploaded_file(file, f"response_{message.registration_number}")

        # Create response
        response = InternalMessageResponse(
            original_message_id=message.id,
            response_content=response_content,
            responded_by=current_user.id,
            attachment_path=attachment_path,
            attachment_name=attachment_name
        )

        db.session.add(response)
        db.session.commit()

        flash('تم إرسال الرد بنجاح', 'success')
        return redirect(url_for('messages.view_message', id=id))

    return render_template('messages/respond.html', message=message)

@messages_bp.route('/my-internal')
@login_required
def my_internal_messages():
    """المراسلات الداخلية المكلف بها المستخدم الحالي"""
    page = request.args.get('page', 1, type=int)

    messages = Message.query.filter_by(
        message_type='internal',
        assigned_to=current_user.id
    ).order_by(Message.date_created.desc()).paginate(
        page=page, per_page=current_app.config['MESSAGES_PER_PAGE'], error_out=False
    )

    return render_template('messages/my_internal.html', messages=messages)

@messages_bp.route('/<int:id>/approve', methods=['POST'])
@login_required
def approve_message(id):
    """اعتماد الرسالة"""
    message = Message.query.get_or_404(id)

    if not message.can_be_approved_by(current_user):
        flash('ليس لديك صلاحية لاعتماد هذه الرسالة', 'error')
        return redirect(url_for('messages.view_message', id=id))

    approval_notes = request.form.get('approval_notes', '').strip()

    if message.approve(current_user, approval_notes):
        db.session.commit()
        flash('تم اعتماد الرسالة بنجاح', 'success')
    else:
        flash('حدث خطأ أثناء اعتماد الرسالة', 'error')

    return redirect(url_for('messages.view_message', id=id))

@messages_bp.route('/<int:id>/reject', methods=['POST'])
@login_required
def reject_message(id):
    """رفض الرسالة"""
    message = Message.query.get_or_404(id)

    if not message.can_be_approved_by(current_user):
        flash('ليس لديك صلاحية لرفض هذه الرسالة', 'error')
        return redirect(url_for('messages.view_message', id=id))

    approval_notes = request.form.get('approval_notes', '').strip()

    if not approval_notes:
        flash('يرجى إدخال سبب الرفض', 'error')
        return redirect(url_for('messages.view_message', id=id))

    if message.reject(current_user, approval_notes):
        db.session.commit()
        flash('تم رفض الرسالة', 'warning')
    else:
        flash('حدث خطأ أثناء رفض الرسالة', 'error')

    return redirect(url_for('messages.view_message', id=id))

@messages_bp.route('/pending_approval')
@login_required
def pending_approval():
    """الرسائل المعلقة للاعتماد"""
    if not (current_user.is_manager() or current_user.is_secretary()):
        flash('ليس لديك صلاحية لعرض الرسائل المعلقة للاعتماد', 'error')
        return redirect(url_for('index'))

    page = request.args.get('page', 1, type=int)

    messages = Message.query.filter_by(
        requires_approval=True,
        approval_status=ApprovalStatus.PENDING
    ).order_by(Message.date_created.desc()).paginate(
        page=page, per_page=current_app.config['MESSAGES_PER_PAGE'], error_out=False
    )

    return render_template('messages/pending_approval.html', messages=messages)

@messages_bp.route('/bulk_delete', methods=['POST'])
@login_required
def bulk_delete():
    """حذف رسائل متعددة"""
    message_ids = request.form.getlist('message_ids')

    if not message_ids:
        flash('لم يتم اختيار أي رسائل للحذف', 'error')
        return redirect(request.referrer or url_for('messages.list_messages'))

    try:
        # Convert to integers and validate
        message_ids = [int(id) for id in message_ids]

        # Get messages to delete
        messages_to_delete = Message.query.filter(Message.id.in_(message_ids)).all()

        # Check permissions
        for message in messages_to_delete:
            if not (current_user.is_manager() or message.created_by == current_user.id):
                flash(f'ليس لديك صلاحية لحذف الرسالة رقم {message.registration_number}', 'error')
                return redirect(request.referrer or url_for('messages.list_messages'))

        # Delete messages
        deleted_count = 0
        for message in messages_to_delete:
            # Delete associated files if any
            if message.attachment_path:
                try:
                    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], message.attachment_path)
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    print(f"Error deleting file {message.attachment_path}: {e}")

            # Delete internal message responses
            InternalMessageResponse.query.filter_by(message_id=message.id).delete()

            # Delete the message
            db.session.delete(message)
            deleted_count += 1

        db.session.commit()
        flash(f'تم حذف {deleted_count} رسالة بنجاح', 'success')

    except ValueError:
        flash('معرفات الرسائل غير صحيحة', 'error')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(request.referrer or url_for('messages.list_messages'))

@messages_bp.route('/download/<path:filename>')
@login_required
def download_attachment(filename):
    """تحميل مرفق"""
    try:
        # Security check - ensure filename is safe
        if '..' in filename or filename.startswith('/'):
            flash('اسم الملف غير صحيح', 'error')
            return redirect(url_for('messages.list_messages'))

        # Build file path
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)

        # Check if file exists
        if not os.path.exists(file_path):
            flash('الملف غير موجود', 'error')
            return redirect(url_for('messages.list_messages'))

        # Get original filename from database
        message = Message.query.filter_by(attachment_path=filename).first()
        if not message:
            flash('الملف غير موجود في قاعدة البيانات', 'error')
            return redirect(url_for('messages.list_messages'))

        # Send file
        return send_file(
            file_path,
            as_attachment=True,
            download_name=message.attachment_name
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تحميل الملف: {str(e)}', 'error')
        return redirect(url_for('messages.list_messages'))
