import os
from datetime import datetime, date
from flask import Blueprint, render_template, request, redirect, url_for, flash, send_file, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models import db, Message, MessageType, User
try:
    from utils.pdf_generator import generate_message_pdf
    from utils.file_handler import allowed_file, save_uploaded_file
except ImportError:
    # Fallback functions if utils are not available
    def generate_message_pdf(message):
        return None
    def allowed_file(filename):
        return True
    def save_uploaded_file(file, registration_number):
        return None, None

messages_bp = Blueprint('messages', __name__)

@messages_bp.route('/')
@login_required
def list_messages():
    page = request.args.get('page', 1, type=int)
    message_type = request.args.get('type')
    year = request.args.get('year', type=int)
    
    messages = Message.query
    
    if message_type:
        messages = messages.filter_by(message_type=message_type)
    
    if year:
        messages = messages.filter_by(year=year)
    
    messages = messages.order_by(Message.date_created.desc()).paginate(
        page=page, per_page=current_app.config['MESSAGES_PER_PAGE'], error_out=False
    )
    
    return render_template('messages/list.html', messages=messages)

@messages_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_message():
    if request.method == 'POST':
        message_type = request.form.get('message_type')
        registration_number = request.form.get('registration_number')
        destination = request.form.get('destination')
        subject = request.form.get('subject')
        content = request.form.get('content')
        message_date = request.form.get('message_date')
        department = request.form.get('department')
        priority = request.form.get('priority', 'normal')
        
        # Validation
        if not all([message_type, registration_number, destination, subject, message_date]):
            flash('يرجى ملء جميع الحقول المطلوبة', 'error')
            return render_template('messages/new.html')
        
        # Check if registration number already exists
        existing_message = Message.query.filter_by(registration_number=registration_number).first()
        if existing_message:
            flash('رقم التسجيل موجود مسبقاً', 'error')
            return render_template('messages/new.html')
        
        # Parse date
        try:
            message_date = datetime.strptime(message_date, '%Y-%m-%d').date()
        except ValueError:
            flash('تاريخ غير صحيح', 'error')
            return render_template('messages/new.html')
        
        # Handle file upload
        attachment_path = None
        attachment_name = None
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                attachment_path, attachment_name = save_uploaded_file(file, registration_number)
        
        # Create message
        message = Message(
            message_type=MessageType(message_type),
            registration_number=registration_number,
            destination=destination,
            subject=subject,
            content=content,
            message_date=message_date,
            department=department,
            priority=priority,
            attachment_path=attachment_path,
            attachment_name=attachment_name,
            created_by=current_user.id
        )
        
        db.session.add(message)
        db.session.commit()
        
        flash('تم إنشاء الرسالة بنجاح', 'success')
        return redirect(url_for('messages.view_message', id=message.id))
    
    return render_template('messages/new.html')

@messages_bp.route('/<int:id>')
@login_required
def view_message(id):
    message = Message.query.get_or_404(id)
    return render_template('messages/view.html', message=message)

@messages_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_message(id):
    message = Message.query.get_or_404(id)
    
    # Check permissions
    if not current_user.is_manager() and message.created_by != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذه الرسالة', 'error')
        return redirect(url_for('messages.view_message', id=id))
    
    if request.method == 'POST':
        message.destination = request.form.get('destination')
        message.subject = request.form.get('subject')
        message.content = request.form.get('content')
        message.department = request.form.get('department')
        message.priority = request.form.get('priority', 'normal')
        
        # Handle new attachment
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                # Remove old attachment if exists
                if message.attachment_path:
                    old_path = os.path.join(current_app.config['UPLOAD_FOLDER'], message.attachment_path)
                    if os.path.exists(old_path):
                        os.remove(old_path)
                
                attachment_path, attachment_name = save_uploaded_file(file, message.registration_number)
                message.attachment_path = attachment_path
                message.attachment_name = attachment_name
        
        db.session.commit()
        flash('تم تحديث الرسالة بنجاح', 'success')
        return redirect(url_for('messages.view_message', id=id))
    
    return render_template('messages/edit.html', message=message)

@messages_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete_message(id):
    if not current_user.can_delete_messages():
        flash('ليس لديك صلاحية لحذف الرسائل', 'error')
        return redirect(url_for('messages.view_message', id=id))
    
    message = Message.query.get_or_404(id)
    
    # Remove attachment file if exists
    if message.attachment_path:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], message.attachment_path)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    db.session.delete(message)
    db.session.commit()
    
    flash('تم حذف الرسالة بنجاح', 'success')
    return redirect(url_for('messages.list_messages'))

@messages_bp.route('/<int:id>/pdf')
@login_required
def download_pdf(id):
    message = Message.query.get_or_404(id)
    try:
        pdf_path = generate_message_pdf(message)
        return send_file(pdf_path, as_attachment=True, download_name=f'message_{message.registration_number}.pdf')
    except Exception as e:
        flash(f'خطأ في إنشاء ملف PDF: {str(e)}', 'error')
        return redirect(url_for('messages.view_message', id=id))

@messages_bp.route('/download/<path:filename>')
@login_required
def download_attachment(filename):
    return send_file(os.path.join(current_app.config['UPLOAD_FOLDER'], filename), as_attachment=True)
