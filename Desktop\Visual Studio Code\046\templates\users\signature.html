{% extends "base.html" %}

{% block title %}إدارة التوقيع الإلكتروني - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-signature me-2"></i>
                إدارة التوقيع الإلكتروني
            </h1>
            <div>
                <a href="{{ url_for('users.view_user', id=current_user.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للملف الشخصي
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Current Signature Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    حالة التوقيع الحالية
                </h5>
            </div>
            <div class="card-body">
                {% if false %}
                <div class="alert alert-success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>لديك توقيع إلكتروني نشط</strong>
                            <br>
                            <small class="text-muted">
                                النوع: 
                                {% if current_user.signature_type == 'digital' %}
                                    توقيع رقمي
                                {% else %}
                                    صورة توقيع
                                {% endif %}
                                | تم الإنشاء: {{ current_user.signature_created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#removeSignatureModal">
                                <i class="fas fa-trash me-1"></i>
                                إزالة التوقيع
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Display Current Signature -->
                <div class="mt-3">
                    <h6>معاينة التوقيع الحالي:</h6>
                    <div class="signature-preview p-3 border rounded bg-light">
                        {% if current_user.signature_type == 'digital' %}
                            <canvas id="currentSignatureCanvas" width="400" height="150" style="border: 1px solid #ddd;"></canvas>
                        {% else %}
                            <img src="{{ url_for('users.get_signature_image', user_id=current_user.id) }}" 
                                 alt="التوقيع" class="img-fluid" style="max-height: 150px;">
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>لا يوجد توقيع إلكتروني</strong>
                    <br>
                    <small>يمكنك إنشاء توقيع رقمي أو رفع صورة توقيع من الخيارات أدناه</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Signature Options -->
<div class="row">
    <!-- Digital Signature -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-pen-fancy me-2"></i>
                    التوقيع الرقمي
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">ارسم توقيعك باستخدام الماوس أو اللمس</p>
                
                <div class="signature-pad-container">
                    <canvas id="signaturePad" width="400" height="200" style="border: 2px solid #ddd; border-radius: 5px;"></canvas>
                </div>
                
                <div class="mt-3">
                    <div class="row">
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-secondary w-100" id="clearSignature">
                                <i class="fas fa-eraser me-1"></i>
                                مسح
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="button" class="btn btn-primary w-100" id="saveDigitalSignature">
                                <i class="fas fa-save me-1"></i>
                                حفظ التوقيع
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        يمكنك الرسم باستخدام الماوس أو اللمس على الشاشات التي تدعم ذلك
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Image Signature -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-image me-2"></i>
                    صورة التوقيع
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">ارفع صورة توقيعك (PNG, JPG, GIF)</p>
                
                <form method="POST" action="{{ url_for('users.upload_signature') }}" enctype="multipart/form-data" id="imageSignatureForm">
                    <div class="mb-3">
                        <label for="signatureFile" class="form-label">اختر صورة التوقيع</label>
                        <input type="file" class="form-control" id="signatureFile" name="signature_file" 
                               accept="image/png,image/jpeg,image/jpg,image/gif" required>
                        <div class="form-text">
                            الحد الأقصى: 2MB | الأنواع المدعومة: PNG, JPG, GIF
                        </div>
                    </div>
                    
                    <!-- Image Preview -->
                    <div id="imagePreview" class="mb-3" style="display: none;">
                        <label class="form-label">معاينة:</label>
                        <div class="border rounded p-2 bg-light">
                            <img id="previewImg" src="" alt="معاينة التوقيع" class="img-fluid" style="max-height: 150px;">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-success w-100">
                        <i class="fas fa-upload me-1"></i>
                        رفع وحفظ التوقيع
                    </button>
                </form>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        تأكد من أن الصورة واضحة وبخلفية شفافة أو بيضاء للحصول على أفضل نتيجة
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Usage Instructions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    كيفية استخدام التوقيع الإلكتروني
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-pen-fancy me-2 text-primary"></i>التوقيع الرقمي</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>سريع وسهل الاستخدام</li>
                            <li><i class="fas fa-check text-success me-2"></i>يمكن إنشاؤه مباشرة في المتصفح</li>
                            <li><i class="fas fa-check text-success me-2"></i>يحفظ كبيانات رقمية</li>
                            <li><i class="fas fa-check text-success me-2"></i>مناسب للاستخدام السريع</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-image me-2 text-success"></i>صورة التوقيع</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>أكثر واقعية ووضوحاً</li>
                            <li><i class="fas fa-check text-success me-2"></i>يمكن استخدام توقيع حقيقي ممسوح</li>
                            <li><i class="fas fa-check text-success me-2"></i>جودة عالية للطباعة</li>
                            <li><i class="fas fa-check text-success me-2"></i>مناسب للمستندات الرسمية</li>
                        </ul>
                    </div>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>نصيحة:</strong> سيظهر توقيعك تلقائياً في جميع الرسائل والمستندات التي تنشئها. يمكنك تغيير التوقيع في أي وقت.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Signature Modal -->
<div class="modal fade" id="removeSignatureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash text-danger me-2"></i>
                    إزالة التوقيع الإلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هل أنت متأكد من إزالة التوقيع الإلكتروني؟
                </div>
                <p>سيتم حذف التوقيع نهائياً ولن يظهر في الرسائل الجديدة. الرسائل السابقة ستحتفظ بالتوقيع.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('users.remove_signature') }}" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        إزالة التوقيع
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.signature-pad-container {
    text-align: center;
}

#signaturePad {
    cursor: crosshair;
    background-color: #fafafa;
}

.signature-preview {
    text-align: center;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

#imagePreview img {
    max-width: 100%;
    height: auto;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Digital Signature Pad
    const canvas = document.getElementById('signaturePad');
    const ctx = canvas.getContext('2d');
    let isDrawing = false;
    let lastX = 0;
    let lastY = 0;

    // Set up canvas
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    function startDrawing(e) {
        isDrawing = true;
        const rect = canvas.getBoundingClientRect();
        lastX = e.clientX - rect.left;
        lastY = e.clientY - rect.top;
    }

    function draw(e) {
        if (!isDrawing) return;
        
        const rect = canvas.getBoundingClientRect();
        const currentX = e.clientX - rect.left;
        const currentY = e.clientY - rect.top;
        
        ctx.beginPath();
        ctx.moveTo(lastX, lastY);
        ctx.lineTo(currentX, currentY);
        ctx.stroke();
        
        lastX = currentX;
        lastY = currentY;
    }

    function stopDrawing() {
        isDrawing = false;
    }

    // Mouse events
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);

    // Touch events for mobile
    canvas.addEventListener('touchstart', function(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousedown', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    });

    canvas.addEventListener('touchmove', function(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousemove', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    });

    canvas.addEventListener('touchend', function(e) {
        e.preventDefault();
        const mouseEvent = new MouseEvent('mouseup', {});
        canvas.dispatchEvent(mouseEvent);
    });

    // Clear signature
    document.getElementById('clearSignature').addEventListener('click', function() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    });

    // Save digital signature
    document.getElementById('saveDigitalSignature').addEventListener('click', function() {
        const imageData = canvas.toDataURL();
        
        // Check if canvas is empty
        const emptyCanvas = document.createElement('canvas');
        emptyCanvas.width = canvas.width;
        emptyCanvas.height = canvas.height;
        
        if (imageData === emptyCanvas.toDataURL()) {
            alert('يرجى رسم التوقيع أولاً');
            return;
        }

        // Send to server
        fetch('{{ url_for("users.save_digital_signature") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                signature_data: imageData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حفظ التوقيع بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    });

    // Image preview
    document.getElementById('signatureFile').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('previewImg').src = e.target.result;
                document.getElementById('imagePreview').style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            document.getElementById('imagePreview').style.display = 'none';
        }
    });

    // Display current digital signature
    {% if false %}
    const currentCanvas = document.getElementById('currentSignatureCanvas');
    if (currentCanvas) {
        const currentCtx = currentCanvas.getContext('2d');
        const img = new Image();
        img.onload = function() {
            currentCtx.drawImage(img, 0, 0);
        };
        img.src = '{{ current_user.get_signature_data().get("data", "") if current_user.get_signature_data() else "" }}';
    }
    {% endif %}
});
</script>
{% endblock %}
