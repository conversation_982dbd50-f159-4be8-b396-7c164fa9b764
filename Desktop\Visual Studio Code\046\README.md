# نظام المراسلات الإلكترونية
## Electronic Correspondence System

نظام شامل لإدارة المراسلات الواردة والصادرة في المؤسسات الحكومية والشركات.

### المميزات الرئيسية

- **إدارة الرسائل**: إنشاء وتعديل وحذف الرسائل الواردة والصادرة
- **إدارة المستخدمين**: نظام صلاحيات متدرج (مدير / سكرتير / موظف)
- **المرفقات**: رفع وإدارة ملفات مرفقة مع الرسائل
- **البحث المتقدم**: البحث بالتاريخ والجهة ورقم التسجيل
- **تصدير PDF**: طباعة وتصدير الرسائل بصيغة PDF
- **الأرشفة**: تنظيم الرسائل حسب السنة والقسم
- **واجهة عربية**: دعم كامل للغة العربية مع تخطيط RTL

### متطلبات النظام

- Python 3.8 أو أحدث
- Flask 2.3+
- SQLite (مدمج مع Python)

### التثبيت والتشغيل

#### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

#### 2. تهيئة قاعدة البيانات

```bash
python init_db.py
```

#### 3. تشغيل التطبيق

```bash
python app.py
```

سيعمل التطبيق على العنوان: http://localhost:5000

### بيانات الدخول الافتراضية

- **المدير**: 
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

- **السكرتير**:
  - اسم المستخدم: `secretary`
  - كلمة المرور: `secretary123`

- **الموظف**:
  - اسم المستخدم: `employee`
  - كلمة المرور: `employee123`

### هيكل المشروع

```
├── app.py                 # نقطة البداية الرئيسية
├── config.py             # إعدادات التطبيق
├── models.py             # نماذج قاعدة البيانات
├── init_db.py            # سكريبت تهيئة قاعدة البيانات
├── requirements.txt      # المتطلبات
├── routes/               # ملفات التوجيه
│   ├── auth.py          # توجيه المصادقة
│   ├── messages.py      # توجيه الرسائل
│   └── users.py         # توجيه المستخدمين
├── templates/            # قوالب HTML
│   ├── base.html        # القالب الأساسي
│   ├── dashboard.html   # لوحة التحكم
│   ├── auth/            # قوالب المصادقة
│   └── messages/        # قوالب الرسائل
├── static/               # الملفات الثابتة
│   ├── css/             # ملفات CSS
│   └── js/              # ملفات JavaScript
├── utils/                # أدوات مساعدة
│   ├── pdf_generator.py # مولد ملفات PDF
│   └── file_handler.py  # معالج الملفات
└── uploads/              # مجلد المرفقات
```

### الاستخدام

#### إنشاء رسالة جديدة
1. انقر على "رسالة جديدة" من القائمة الرئيسية
2. اختر نوع الرسالة (واردة/صادرة)
3. أدخل البيانات المطلوبة
4. أرفق الملفات إن وجدت
5. احفظ الرسالة

#### البحث في الرسائل
1. استخدم خانة البحث في الصفحة الرئيسية
2. أو انتقل إلى صفحة البحث المتقدم
3. حدد معايير البحث (التاريخ، النوع، الجهة)
4. اعرض النتائج

#### إدارة المستخدمين (للمدراء والسكرتارية)
1. انتقل إلى قسم "المستخدمون"
2. أضف مستخدمين جدد
3. عدّل الصلاحيات
4. فعّل أو ألغِ تفعيل المستخدمين

### الصلاحيات

- **المدير**: جميع الصلاحيات
- **السكرتير**: إدارة المستخدمين والرسائل
- **الموظف**: عرض وإنشاء الرسائل فقط

### الأمان

- كلمات المرور مشفرة باستخدام Werkzeug
- جلسات آمنة مع Flask-Login
- حماية من CSRF
- تحقق من صحة الملفات المرفوعة

### التخصيص

يمكن تخصيص النظام من خلال:
- تعديل ملف `config.py` للإعدادات
- تحديث قوالب HTML في مجلد `templates`
- تعديل الأنماط في `static/css/style.css`

### الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

### الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
