#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل النظام مع عرض الملفات
"""

if __name__ == "__main__":
    try:
        print("🚀 تشغيل نظام المراسلات الإلكترونية...")
        print("📄 مع دعم عرض الملفات المتقدم")
        print("=" * 50)
        
        from app import create_app
        app = create_app()
        
        print("✅ النظام جاهز!")
        print("🌐 الرابط: http://localhost:8585")
        print("🔐 المستخدم: admin")
        print("🔐 كلمة المرور: admin123")
        print("=" * 50)
        print("📄 أنواع الملفات المدعومة:")
        print("  • PDF - عرض مباشر")
        print("  • الصور (JPG, PNG, GIF) - معاينة")
        print("  • النصوص (TXT, HTML, JSON) - عرض المحتوى")
        print("  • الصوت والفيديو - تشغيل مباشر")
        print("  • جميع الأنواع - تحميل")
        print("=" * 50)
        print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
        print("=" * 50)
        
        app.run(host='0.0.0.0', port=8585, debug=False)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        print("\n💡 حلول مقترحة:")
        print("  • تأكد من تثبيت المتطلبات: pip install flask flask-sqlalchemy flask-login")
        print("  • تأكد من عدم حجز المنفذ 8585")
        print("  • شغل الأمر كمدير")
        input("\n⏸️ اضغط Enter للخروج...")
