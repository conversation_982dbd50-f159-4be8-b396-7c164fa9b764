{% extends "base.html" %}

{% block title %}إنشاء أرشيف جديد - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus me-2"></i>
                إنشاء أرشيف جديد
            </h1>
            <div>
                <a href="{{ url_for('archive.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للأرشيف
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الأرشيف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الأرشيف *</label>
                        <input type="text" class="form-control" id="name" name="name" required
                               placeholder="مثال: أرشيف 2024">
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الأرشيف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف مختصر للأرشيف ومحتوياته"></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="archive_type" class="form-label">نوع الأرشيف *</label>
                        <select class="form-select" id="archive_type" name="archive_type" required>
                            <option value="">اختر نوع الأرشيف</option>
                            <option value="yearly">أرشيف سنوي</option>
                            <option value="monthly">أرشيف شهري</option>
                            <option value="custom">أرشيف مخصص</option>
                        </select>
                    </div>

                    <!-- Yearly Archive Options -->
                    <div id="yearlyOptions" class="mb-4" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">إعدادات الأرشيف السنوي</h6>
                                <div class="mb-3">
                                    <label for="year" class="form-label">السنة</label>
                                    <select class="form-select" id="year" name="year">
                                        <option value="">اختر السنة</option>
                                        {% for year in range(2020, 2030) %}
                                        <option value="{{ year }}" {% if year == 2024 %}selected{% endif %}>{{ year }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Archive Options -->
                    <div id="monthlyOptions" class="mb-4" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">إعدادات الأرشيف الشهري</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="monthly_year" class="form-label">السنة</label>
                                        <select class="form-select" id="monthly_year" name="year">
                                            <option value="">اختر السنة</option>
                                            {% for year in range(2020, 2030) %}
                                            <option value="{{ year }}" {% if year == 2024 %}selected{% endif %}>{{ year }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="month" class="form-label">الشهر</label>
                                        <select class="form-select" id="month" name="month">
                                            <option value="">اختر الشهر</option>
                                            <option value="1">يناير</option>
                                            <option value="2">فبراير</option>
                                            <option value="3">مارس</option>
                                            <option value="4">أبريل</option>
                                            <option value="5">مايو</option>
                                            <option value="6">يونيو</option>
                                            <option value="7">يوليو</option>
                                            <option value="8">أغسطس</option>
                                            <option value="9">سبتمبر</option>
                                            <option value="10">أكتوبر</option>
                                            <option value="11">نوفمبر</option>
                                            <option value="12">ديسمبر</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Custom Archive Options -->
                    <div id="customOptions" class="mb-4" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">إعدادات الأرشيف المخصص</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="start_date" class="form-label">تاريخ البداية</label>
                                        <input type="date" class="form-control" id="start_date" name="start_date">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="end_date" class="form-label">تاريخ النهاية</label>
                                        <input type="date" class="form-control" id="end_date" name="end_date">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('archive.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إنشاء الأرشيف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اختر اسماً واضحاً للأرشيف
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حدد نوع الأرشيف المناسب
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكنك إضافة الرسائل لاحقاً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        الأرشيف يساعد في تنظيم الرسائل
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    أنواع الأرشيف
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>سنوي:</strong>
                    <p class="small text-muted">لأرشفة جميع رسائل سنة معينة</p>
                </div>
                <div class="mb-3">
                    <strong>شهري:</strong>
                    <p class="small text-muted">لأرشفة رسائل شهر معين من سنة محددة</p>
                </div>
                <div class="mb-3">
                    <strong>مخصص:</strong>
                    <p class="small text-muted">لأرشفة رسائل فترة زمنية محددة</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const archiveTypeSelect = document.getElementById('archive_type');
    const yearlyOptions = document.getElementById('yearlyOptions');
    const monthlyOptions = document.getElementById('monthlyOptions');
    const customOptions = document.getElementById('customOptions');
    
    archiveTypeSelect.addEventListener('change', function() {
        // Hide all options first
        yearlyOptions.style.display = 'none';
        monthlyOptions.style.display = 'none';
        customOptions.style.display = 'none';
        
        // Show relevant options
        switch(this.value) {
            case 'yearly':
                yearlyOptions.style.display = 'block';
                break;
            case 'monthly':
                monthlyOptions.style.display = 'block';
                break;
            case 'custom':
                customOptions.style.display = 'block';
                break;
        }
    });
    
    // Auto-generate archive name based on type and selection
    function updateArchiveName() {
        const type = archiveTypeSelect.value;
        const nameInput = document.getElementById('name');
        
        if (!nameInput.value || nameInput.dataset.autoGenerated === 'true') {
            let newName = '';
            
            switch(type) {
                case 'yearly':
                    const year = document.getElementById('year').value;
                    if (year) {
                        newName = `أرشيف ${year}`;
                    }
                    break;
                case 'monthly':
                    const monthlyYear = document.getElementById('monthly_year').value;
                    const month = document.getElementById('month').value;
                    if (monthlyYear && month) {
                        const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                        newName = `أرشيف ${monthNames[parseInt(month)]} ${monthlyYear}`;
                    }
                    break;
                case 'custom':
                    const startDate = document.getElementById('start_date').value;
                    const endDate = document.getElementById('end_date').value;
                    if (startDate && endDate) {
                        newName = `أرشيف ${startDate} إلى ${endDate}`;
                    }
                    break;
            }
            
            if (newName) {
                nameInput.value = newName;
                nameInput.dataset.autoGenerated = 'true';
            }
        }
    }
    
    // Add event listeners for auto-generation
    document.getElementById('year').addEventListener('change', updateArchiveName);
    document.getElementById('monthly_year').addEventListener('change', updateArchiveName);
    document.getElementById('month').addEventListener('change', updateArchiveName);
    document.getElementById('start_date').addEventListener('change', updateArchiveName);
    document.getElementById('end_date').addEventListener('change', updateArchiveName);
    
    // Disable auto-generation if user manually edits name
    document.getElementById('name').addEventListener('input', function() {
        this.dataset.autoGenerated = 'false';
    });
});
</script>
{% endblock %}
