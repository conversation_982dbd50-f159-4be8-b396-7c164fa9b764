#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قسم إدارة الجهات/الأقسام
"""

import requests
import sys

def test_departments_section():
    """اختبار قسم إدارة الجهات"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار الوصول لقائمة الجهات
        print("\n🏢 اختبار الوصول لقائمة الجهات...")
        response = session.get(f"{base_url}/departments")
        if response.status_code == 200:
            print("✅ تم الوصول لقائمة الجهات بنجاح")
            
            content = response.text
            if 'جميع الأقسام' in content or 'قائمة الأقسام' in content:
                print("✅ تم العثور على محتوى قائمة الجهات")
            else:
                print("⚠️ لم يتم العثور على محتوى قائمة الجهات")
        else:
            print(f"❌ فشل الوصول لقائمة الجهات: {response.status_code}")
            return False
        
        # اختبار صفحة إنشاء جهة جديدة
        print("\n➕ اختبار صفحة إنشاء جهة جديدة...")
        response = session.get(f"{base_url}/departments/new")
        if response.status_code == 200:
            print("✅ تم الوصول لصفحة إنشاء جهة جديدة")
            
            content = response.text
            if 'إنشاء قسم جديد' in content or 'اسم القسم' in content:
                print("✅ تم العثور على نموذج إنشاء الجهة")
            else:
                print("⚠️ لم يتم العثور على نموذج إنشاء الجهة")
        else:
            print(f"❌ فشل الوصول لصفحة إنشاء جهة: {response.status_code}")
            return False
        
        # اختبار إنشاء جهة جديدة
        print("\n📝 اختبار إنشاء جهة جديدة...")
        department_data = {
            'name': 'قسم الاختبار',
            'description': 'قسم تجريبي للاختبار',
            'is_active': 'on'
        }
        
        response = session.post(f"{base_url}/departments/new", data=department_data)
        if response.status_code == 200:
            print("✅ تم إنشاء الجهة بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في إنشاء الجهة: {response.status_code}")
        
        # اختبار API الجهات
        print("\n🔌 اختبار API الجهات...")
        response = session.get(f"{base_url}/departments/api/departments")
        if response.status_code == 200:
            print("✅ API الجهات يعمل بنجاح")
            try:
                data = response.json()
                print(f"✅ تم العثور على {len(data)} جهة في النظام")
            except:
                print("⚠️ مشكلة في تحليل بيانات JSON")
        else:
            print(f"❌ فشل في الوصول لـ API الجهات: {response.status_code}")
        
        # اختبار API المستخدمين
        print("\n👥 اختبار API المستخدمين...")
        response = session.get(f"{base_url}/departments/api/users")
        if response.status_code == 200:
            print("✅ API المستخدمين يعمل بنجاح")
            try:
                data = response.json()
                print(f"✅ تم العثور على {len(data)} مستخدم في النظام")
            except:
                print("⚠️ مشكلة في تحليل بيانات JSON")
        else:
            print(f"❌ فشل في الوصول لـ API المستخدمين: {response.status_code}")
        
        # اختبار عرض تفاصيل جهة (إذا وجدت)
        print("\n🔍 اختبار عرض تفاصيل الجهة...")
        response = session.get(f"{base_url}/departments/1")
        if response.status_code == 200:
            print("✅ تم عرض تفاصيل الجهة بنجاح")
            
            content = response.text
            if 'تفاصيل القسم' in content or 'إحصائيات القسم' in content:
                print("✅ تم العثور على محتوى تفاصيل الجهة")
            else:
                print("⚠️ لم يتم العثور على محتوى تفاصيل الجهة")
        else:
            print(f"⚠️ لا توجد جهة بالمعرف 1 أو مشكلة في الوصول: {response.status_code}")
        
        # اختبار الوصول للقائمة الجانبية
        print("\n📋 اختبار القائمة الجانبية...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            if 'الأقسام' in content and 'departments' in content:
                print("✅ تم العثور على رابط الجهات في القائمة الجانبية")
            else:
                print("⚠️ لم يتم العثور على رابط الجهات في القائمة الجانبية")
        
        print("\n🎉 تم اكتمال جميع اختبارات قسم الجهات!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار قسم إدارة الجهات/الأقسام")
    print("=" * 60)
    
    success = test_departments_section()
    
    if success:
        print("\n✅ جميع اختبارات قسم الجهات نجحت!")
        print("\n📋 الميزات المتاحة في قسم الجهات:")
        print("🏢 إدارة الجهات/الأقسام:")
        print("  • عرض قائمة جميع الجهات")
        print("  • إنشاء جهة جديدة")
        print("  • تعديل بيانات الجهة")
        print("  • عرض تفاصيل الجهة")
        print("  • تفعيل/إلغاء تفعيل الجهة")
        print("  • حذف الجهة (مع التحقق من الرسائل المرتبطة)")
        print("\n👥 إدارة الموظفين:")
        print("  • عرض موظفي كل جهة")
        print("  • تعيين مستخدمين للجهات")
        print("  • إزالة مستخدمين من الجهات")
        print("  • تعيين مدير للجهة")
        print("\n📊 الإحصائيات:")
        print("  • عدد الرسائل لكل جهة")
        print("  • الرسائل الداخلية بين الجهات")
        print("  • الرسائل المعلقة التي تحتاج رد")
        print("  • عدد الموظفين في كل جهة")
        print("\n🔌 APIs متاحة:")
        print("  • /departments/api/departments - قائمة الجهات")
        print("  • /departments/api/users - قائمة المستخدمين")
        print("\n🌐 الروابط المتاحة:")
        print("  • http://localhost:8585/departments - قائمة الجهات")
        print("  • http://localhost:8585/departments/new - إنشاء جهة جديدة")
        print("  • http://localhost:8585/departments/[id] - تفاصيل الجهة")
        print("  • http://localhost:8585/departments/[id]/edit - تعديل الجهة")
        print("  • http://localhost:8585/departments/[id]/employees - موظفي الجهة")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض اختبارات قسم الجهات!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم على المنفذ 8585")
        print("  • صحة بيانات تسجيل الدخول")
        print("  • وجود قاعدة البيانات والجداول")
        print("  • صحة ملفات القوالب")
        sys.exit(1)
