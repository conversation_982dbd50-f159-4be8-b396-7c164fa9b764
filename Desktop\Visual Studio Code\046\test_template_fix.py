#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة القوالب
"""

import requests
import sys

def test_template_fix():
    """اختبار إصلاح مشكلة القوالب"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة قائمة المستخدمين (التي كانت تحتوي على المشكلة)
        print("\n👥 اختبار صفحة قائمة المستخدمين...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            print("✅ صفحة قائمة المستخدمين تعمل بشكل صحيح")
            
            content = response.text
            if 'جميع المستخدمين' in content:
                print("✅ محتوى الصفحة يظهر بشكل صحيح")
            
            if 'extra_css' not in content or content.count('extra_css') <= 2:
                print("✅ لا توجد تعريفات مكررة لـ extra_css")
            else:
                print("⚠️ قد تكون هناك تعريفات مكررة لـ extra_css")
                
        else:
            print(f"❌ فشل في الوصول لصفحة قائمة المستخدمين: {response.status_code}")
            return False
        
        # اختبار صفحات أخرى مهمة
        pages_to_test = [
            ("/", "الصفحة الرئيسية"),
            ("/messages", "قائمة الرسائل"),
            ("/messages/pending_approval", "الرسائل المعلقة للاعتماد"),
            ("/departments", "قائمة الأقسام"),
            ("/permissions", "إدارة الصلاحيات")
        ]
        
        print("\n🧪 اختبار صفحات أخرى...")
        for url, name in pages_to_test:
            response = session.get(f"{base_url}{url}")
            if response.status_code == 200:
                print(f"✅ {name} تعمل بشكل صحيح")
            else:
                print(f"⚠️ مشكلة في {name}: {response.status_code}")
        
        # اختبار إنشاء مستخدم جديد
        print("\n👤 اختبار إنشاء مستخدم جديد...")
        response = session.get(f"{base_url}/users/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء مستخدم جديد تعمل")
        else:
            print(f"⚠️ مشكلة في صفحة إنشاء مستخدم: {response.status_code}")
        
        # اختبار تعديل مستخدم
        print("\n✏️ اختبار صفحة تعديل مستخدم...")
        response = session.get(f"{base_url}/users/1/edit")
        if response.status_code == 200:
            print("✅ صفحة تعديل المستخدم تعمل")
        else:
            print(f"⚠️ مشكلة في صفحة تعديل المستخدم: {response.status_code}")
        
        print("\n🎉 تم اكتمال اختبار إصلاح القوالب!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار إصلاح مشكلة القوالب")
    print("=" * 50)
    
    success = test_template_fix()
    
    if success:
        print("\n✅ تم إصلاح مشكلة القوالب بنجاح!")
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ دمج تعريفات extra_css المكررة في ملف واحد")
        print("  ✅ إزالة التعريفات المكررة من قوالب المستخدمين")
        print("  ✅ التأكد من عمل جميع الصفحات بشكل صحيح")
        print("\n🌐 جميع الصفحات تعمل الآن:")
        print("  👥 قائمة المستخدمين: http://localhost:8585/users")
        print("  📝 إنشاء مستخدم: http://localhost:8585/users/new")
        print("  ✏️ تعديل مستخدم: http://localhost:8585/users/1/edit")
        print("  📋 الرسائل المعلقة: http://localhost:8585/messages/pending_approval")
        print("  🏠 الصفحة الرئيسية: http://localhost:8585/")
        print("\n🎯 مشكلة TemplateAssertionError تم حلها!")
        sys.exit(0)
    else:
        print("\n❌ فشل في إصلاح مشكلة القوالب!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم")
        print("  • عدم وجود أخطاء في القوالب")
        print("  • صحة بيانات الدخول")
        sys.exit(1)
