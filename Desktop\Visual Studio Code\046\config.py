import os
from datetime import timedelta

class Config:
    # Basic Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-this-in-production'
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///correspondence.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # File upload configuration
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size
    ALLOWED_EXTENSIONS = {
        # Documents
        'txt', 'pdf', 'doc', 'docx', 'rtf', 'odt',
        # Spreadsheets
        'xls', 'xlsx', 'ods', 'csv',
        # Presentations
        'ppt', 'pptx', 'odp',
        # Images
        'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'svg', 'webp',
        # Archives
        'zip', 'rar', '7z', 'tar', 'gz',
        # Audio/Video
        'mp3', 'wav', 'mp4', 'avi', 'mov', 'wmv', 'flv',
        # Other
        'xml', 'json', 'html', 'css', 'js'
    }
    
    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # Application settings
    MESSAGES_PER_PAGE = 20
    
    # Arabic text support
    ARABIC_SUPPORT = True
    
    @staticmethod
    def init_app(app):
        # Create upload directory if it doesn't exist
        upload_path = os.path.join(app.instance_path, Config.UPLOAD_FOLDER)
        os.makedirs(upload_path, exist_ok=True)
        
        # Create static directories
        static_dirs = ['static/css', 'static/js', 'static/images']
        for directory in static_dirs:
            os.makedirs(directory, exist_ok=True)

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
