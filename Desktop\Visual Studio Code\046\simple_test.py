#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنظام
"""

import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, UserRole, Permission, Role, Department

def simple_test():
    """اختبار بسيط للنظام"""
    
    app = create_app()
    with app.app_context():
        print("🧪 اختبار بسيط للنظام...")
        print("=" * 50)
        
        # اختبار قاعدة البيانات
        print("💾 اختبار قاعدة البيانات:")
        try:
            users_count = User.query.count()
            permissions_count = Permission.query.count()
            roles_count = Role.query.count()
            departments_count = Department.query.count()
            
            print(f"   ✅ المستخدمون: {users_count}")
            print(f"   ✅ الصلاحيات: {permissions_count}")
            print(f"   ✅ الأدوار: {roles_count}")
            print(f"   ✅ الأقسام: {departments_count}")
        except Exception as e:
            print(f"   ❌ خطأ في قاعدة البيانات: {e}")
            return
        
        # اختبار المدير
        print("\n👤 اختبار المدير:")
        admin = User.query.filter_by(username='admin').first()
        if admin:
            print(f"   ✅ المدير موجود: {admin.name}")
            print(f"   ✅ الدور: {admin.role.value}")
            print(f"   ✅ نشط: {admin.is_active}")
            print(f"   ✅ يمكنه إدارة المستخدمين: {admin.can_manage_users()}")
        else:
            print("   ❌ المدير غير موجود")
            return
        
        # اختبار الصلاحيات
        print("\n🔐 اختبار الصلاحيات الأساسية:")
        basic_permissions = [
            'create_user', 'view_user', 'edit_user',
            'create_department', 'view_department',
            'manage_system'
        ]
        
        for perm in basic_permissions:
            has_perm = admin.has_permission(perm)
            status = "✅" if has_perm else "❌"
            print(f"   {status} {perm}")
        
        # اختبار الأقسام
        print("\n🏢 اختبار الأقسام:")
        departments = Department.query.all()
        for dept in departments:
            print(f"   ✅ {dept.name} - {'نشط' if dept.is_active else 'غير نشط'}")
        
        # اختبار الأدوار
        print("\n👥 اختبار الأدوار:")
        roles = Role.query.all()
        for role in roles:
            permissions_count = len(role.permissions)
            print(f"   ✅ {role.display_name} - {permissions_count} صلاحية")
        
        print("\n✅ جميع الاختبارات تمت بنجاح!")
        print("\n🚀 النظام جاهز للاستخدام:")
        print("   🌐 الرابط: http://localhost:8585")
        print("   👤 المستخدم: admin")
        print("   🔐 كلمة المرور: admin123")

if __name__ == '__main__':
    simple_test()
