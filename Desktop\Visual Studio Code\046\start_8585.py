#!/usr/bin/env python3
"""
Start server on port 8585 - Electronic Correspondence System
تشغيل الخادم على المنفذ 8585
"""

from flask import Flask, render_template_string

app = Flask(__name__)

@app.route('/')
def home():
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام المراسلات الإلكترونية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                margin: 0;
            }
            .hero {
                color: white;
                text-align: center;
                padding: 80px 20px;
            }
            .hero h1 {
                font-size: 3rem;
                margin-bottom: 20px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .hero p {
                font-size: 1.2rem;
                margin-bottom: 40px;
                opacity: 0.9;
            }
            .status-card {
                background: rgba(255,255,255,0.95);
                border-radius: 15px;
                padding: 30px;
                margin: 20px auto;
                max-width: 600px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                color: #333;
            }
            .success-badge {
                background: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                font-weight: bold;
                display: inline-block;
                margin: 10px 0;
            }
            .feature-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 40px 0;
            }
            .feature-card {
                background: white;
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                transition: transform 0.3s;
                color: #333;
            }
            .feature-card:hover {
                transform: translateY(-5px);
            }
            .feature-icon {
                font-size: 3rem;
                margin-bottom: 20px;
            }
            .btn-custom {
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                border: none;
                border-radius: 25px;
                padding: 12px 30px;
                color: white;
                text-decoration: none;
                font-weight: bold;
                transition: transform 0.2s;
                display: inline-block;
                margin: 10px;
            }
            .btn-custom:hover {
                transform: translateY(-2px);
                color: white;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="hero">
            <div class="container">
                <h1><i class="fas fa-rocket me-3"></i>مرحباً بك في نظام المراسلات الإلكترونية</h1>
                <p>نظام شامل ومتطور لإدارة المراسلات والوثائق الإلكترونية</p>
                
                <div class="status-card">
                    <div class="success-badge">
                        <i class="fas fa-check-circle me-2"></i>النظام يعمل بنجاح!
                    </div>
                    <p class="mt-3">تم تشغيل الخادم بنجاح على المنفذ <strong>8585</strong></p>
                    <p><strong>الرابط:</strong> http://localhost:8585</p>
                </div>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon text-primary">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h5>نظام الأرشيف</h5>
                        <p>أرشفة منظمة للرسائل حسب السنة والشهر</p>
                        <a href="/archive" class="btn-custom">دخول</a>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon text-success">
                            <i class="fas fa-signature"></i>
                        </div>
                        <h5>التوقيع الإلكتروني</h5>
                        <p>طريقة رقمية وبصرية لتوقيع المستندات</p>
                        <a href="/signature" class="btn-custom">دخول</a>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon text-info">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h5>إدارة الصلاحيات</h5>
                        <p>تحكم كامل في صلاحيات المستخدمين</p>
                        <a href="/permissions" class="btn-custom">دخول</a>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="/login" class="btn-custom">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                    <a href="/test" class="btn-custom">
                        <i class="fas fa-flask me-2"></i>اختبار النظام
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/login')
def login():
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تسجيل الدخول</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .login-card {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                overflow: hidden;
                max-width: 450px;
                width: 100%;
            }
            .login-header {
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                padding: 40px 30px;
                text-align: center;
            }
            .login-body {
                padding: 40px;
            }
            .form-control {
                border-radius: 10px;
                border: 2px solid #e9ecef;
                padding: 15px;
                margin-bottom: 20px;
                font-size: 16px;
            }
            .btn-login {
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
                width: 100%;
                color: white;
                font-size: 16px;
            }
            .demo-info {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin-top: 25px;
                border-left: 4px solid #28a745;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="login-card">
                        <div class="login-header">
                            <h2><i class="fas fa-lock me-3"></i>تسجيل الدخول</h2>
                            <p class="mb-0">نظام المراسلات الإلكترونية</p>
                            <small>المنفذ: 8585</small>
                        </div>
                        <div class="login-body">
                            <form>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">اسم المستخدم</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" placeholder="أدخل اسم المستخدم">
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <label class="form-label fw-bold">كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" placeholder="أدخل كلمة المرور">
                                    </div>
                                </div>
                                <button type="button" class="btn btn-login" onclick="showDemo()">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول
                                </button>
                            </form>
                            
                            <div class="demo-info">
                                <h6><i class="fas fa-info-circle text-success me-2"></i>بيانات تجريبية</h6>
                                <p class="mb-2"><strong>اسم المستخدم:</strong> admin</p>
                                <p class="mb-2"><strong>كلمة المرور:</strong> admin123</p>
                                <small class="text-muted">للنظام الكامل: python app.py</small>
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="/" class="text-decoration-none">
                                    <i class="fas fa-arrow-right me-1"></i>العودة للصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            function showDemo() {
                alert('هذه صفحة تجريبية!\\n\\nالمنفذ الحالي: 8585\\nللنظام الكامل استخدم: python app.py');
            }
        </script>
    </body>
    </html>
    ''')

@app.route('/test')
def test():
    return {
        'status': 'success',
        'message': 'الخادم يعمل على المنفذ 8585',
        'port': 8585,
        'host': 'localhost',
        'url': 'http://localhost:8585',
        'routes': {
            'home': '/',
            'login': '/login',
            'test': '/test'
        }
    }

if __name__ == '__main__':
    print("🚀 تشغيل نظام المراسلات الإلكترونية")
    print("=" * 60)
    print("🌐 الرابط الرئيسي: http://localhost:8585")
    print("🔐 صفحة الدخول: http://localhost:8585/login")
    print("🧪 اختبار النظام: http://localhost:8585/test")
    print("=" * 60)
    print("📋 بيانات الدخول التجريبية:")
    print("   👤 اسم المستخدم: admin")
    print("   🔐 كلمة المرور: admin123")
    print("=" * 60)
    print("⚡ المنفذ: 8585")
    print("🛑 لإيقاف الخادم: Ctrl+C")
    print("=" * 60)
    
    app.run(host='127.0.0.1', port=8585, debug=True)
