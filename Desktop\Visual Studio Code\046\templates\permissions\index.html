{% extends "base.html" %}

{% block title %}إدارة الصلاحيات - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-shield-alt me-2"></i>
                إدارة الصلاحيات والأدوار
            </h1>
            <div>
                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#initPermissionsModal">
                    <i class="fas fa-cog me-2"></i>
                    تهيئة الصلاحيات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_roles }}</h4>
                        <p class="card-text">إجمالي الأدوار</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users-cog fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_permissions }}</h4>
                        <p class="card-text">إجمالي الصلاحيات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-key fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_users }}</h4>
                        <p class="card-text">إجمالي المستخدمين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.active_users }}</h4>
                        <p class="card-text">المستخدمين النشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('permissions.list_roles') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users-cog fa-2x d-block mb-2"></i>
                            إدارة الأدوار
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-key fa-2x d-block mb-2"></i>
                            إدارة الصلاحيات
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('users.list_users') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-users fa-2x d-block mb-2"></i>
                            إدارة المستخدمين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Roles -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>
                    الأدوار المتاحة
                </h5>
                <a href="{{ url_for('permissions.new_role') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    دور جديد
                </a>
            </div>
            <div class="card-body">
                {% if roles %}
                <div class="list-group list-group-flush">
                    {% for role in roles[:5] %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ role.display_name }}</h6>
                            <small class="text-muted">{{ role.permissions|length }} صلاحية</small>
                        </div>
                        <div>
                            {% if role.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if roles|length > 5 %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('permissions.list_roles') }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الأدوار
                    </a>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-users-cog fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد أدوار محددة</p>
                    <a href="{{ url_for('permissions.new_role') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء دور جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    الصلاحيات المتاحة
                </h5>
            </div>
            <div class="card-body">
                {% if permissions %}
                <div class="list-group list-group-flush">
                    {% for permission in permissions[:5] %}
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ permission.display_name }}</h6>
                                <small class="text-muted">{{ permission.category }}</small>
                            </div>
                            <span class="badge bg-primary">{{ permission.category }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if permissions|length > 5 %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('permissions.list_permissions') }}" class="btn btn-sm btn-outline-success">
                        عرض جميع الصلاحيات
                    </a>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-key fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد صلاحيات محددة</p>
                    <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#initPermissionsModal">
                        <i class="fas fa-cog me-2"></i>
                        تهيئة الصلاحيات الافتراضية
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Initialize Permissions Modal -->
<div class="modal fade" id="initPermissionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تهيئة الصلاحيات الافتراضية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هذا الإجراء سيقوم بإنشاء الصلاحيات الافتراضية للنظام إذا لم تكن موجودة مسبقاً.</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكد من أنك تريد المتابعة.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('permissions.init_permissions') }}" class="d-inline">
                    <button type="submit" class="btn btn-warning">تهيئة الصلاحيات</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}
