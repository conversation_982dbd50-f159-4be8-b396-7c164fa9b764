#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوصول إلى صفحات النظام
"""

import requests
import sys

def test_pages_access():
    """اختبار الوصول إلى صفحات النظام"""
    
    base_url = "http://localhost:8585"
    session = requests.Session()
    
    print("🌐 اختبار الوصول إلى صفحات النظام...")
    print("=" * 50)
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        # الحصول على صفحة تسجيل الدخول أولاً
        login_page = session.get(f"{base_url}/auth/login")
        if login_page.status_code != 200:
            print(f"❌ فشل في الوصول إلى صفحة تسجيل الدخول: {login_page.status_code}")
            return
        
        # تسجيل الدخول
        login_response = session.post(f"{base_url}/auth/login", data=login_data)
        if login_response.status_code == 200 and "dashboard" in login_response.url:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل في تسجيل الدخول: {login_response.status_code}")
            return
        
        # قائمة الصفحات للاختبار
        pages_to_test = [
            ("/", "الصفحة الرئيسية"),
            ("/users", "إدارة المستخدمين"),
            ("/users/new", "مستخدم جديد"),
            ("/departments", "إدارة الأقسام"),
            ("/departments/new", "قسم جديد"),
            ("/permissions", "إدارة الصلاحيات"),
            ("/permissions/roles", "إدارة الأدوار"),
            ("/permissions/permissions", "قائمة الصلاحيات"),
            ("/messages", "إدارة الرسائل"),
            ("/messages/new", "رسالة جديدة"),
            ("/archive", "نظام الأرشيف"),
            ("/users/signature", "التوقيع الإلكتروني"),
        ]
        
        print("\n📋 اختبار الصفحات:")
        print("-" * 50)
        
        success_count = 0
        total_count = len(pages_to_test)
        
        for path, name in pages_to_test:
            try:
                response = session.get(f"{base_url}{path}")
                if response.status_code == 200:
                    print(f"✅ {name}: {response.status_code}")
                    success_count += 1
                elif response.status_code == 302:
                    print(f"🔄 {name}: {response.status_code} (إعادة توجيه)")
                    success_count += 1
                else:
                    print(f"❌ {name}: {response.status_code}")
            except Exception as e:
                print(f"❌ {name}: خطأ - {str(e)}")
        
        print("-" * 50)
        print(f"📊 النتائج: {success_count}/{total_count} صفحة تعمل بشكل صحيح")
        
        if success_count == total_count:
            print("🎉 جميع الصفحات تعمل بشكل صحيح!")
        else:
            print(f"⚠️ {total_count - success_count} صفحة تحتاج إلى مراجعة")
            
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم. تأكد من أن الخادم يعمل على المنفذ 8585")
    except Exception as e:
        print(f"❌ حدث خطأ: {str(e)}")

if __name__ == '__main__':
    test_pages_access()
