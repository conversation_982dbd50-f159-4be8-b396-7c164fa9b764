import os
import tempfile
from datetime import datetime
from fpdf import FPDF

class SimplePDF(FPDF):
    def __init__(self):
        super().__init__()
        self.add_page()
        self.set_font('Arial', size=12)

    def add_text(self, text, x=None, y=None, style=''):
        """Add text with basic formatting"""
        if text:
            # Convert Arabic text to a safe format for PDF
            safe_text = self.safe_text(text)

            if x is not None and y is not None:
                self.set_xy(x, y)

            if style:
                self.set_font('Arial', style, 12)
            else:
                self.set_font('Arial', '', 12)

            self.cell(0, 10, safe_text, ln=True, align='R')
        else:
            self.ln(10)

    def safe_text(self, text):
        """Convert text to safe ASCII characters for PDF"""
        if not text:
            return ""

        # Replace Arabic characters with transliteration or remove them
        # This is a simple fallback - in production you'd want proper Arabic font support
        safe_chars = []
        for char in text:
            if ord(char) < 256:  # ASCII characters
                safe_chars.append(char)
            else:
                # Replace non-ASCII with placeholder or transliteration
                safe_chars.append('?')

        return ''.join(safe_chars)

    def add_header(self, title):
        """Add document header"""
        self.set_font('Arial', 'B', 16)
        self.add_text(title)
        self.ln(10)

    def add_field(self, label, value):
        """Add a field with label and value"""
        self.set_font('Arial', 'B', 12)
        self.add_text(f"{label}:")

        self.set_font('Arial', '', 12)
        self.add_text(value or "Not specified")
        self.ln(5)

def generate_message_pdf(message):
    """Generate PDF for a message"""
    pdf = SimplePDF()

    # Header
    pdf.add_header("Message Details")

    # Message details
    pdf.add_field("Registration Number", message.registration_number)
    pdf.add_field("Message Type", "Incoming" if message.is_incoming() else "Outgoing")
    pdf.add_field("Destination", message.destination)
    pdf.add_field("Subject", message.subject)
    pdf.add_field("Date", message.message_date.strftime('%Y-%m-%d'))

    if message.department:
        pdf.add_field("Department", message.department)

    pdf.add_field("Priority", message.priority.title())
    pdf.add_field("Created By", message.creator.name)
    pdf.add_field("Created Date", message.date_created.strftime('%Y-%m-%d %H:%M'))

    # Content
    if message.content:
        pdf.ln(10)
        pdf.set_font('Arial', 'B', 12)
        pdf.add_text("Content:")
        pdf.set_font('Arial', '', 11)

        # Split content into lines to handle long text
        content_lines = message.content.split('\n')
        for line in content_lines:
            if len(line) > 80:  # Split long lines
                words = line.split(' ')
                current_line = ""
                for word in words:
                    if len(current_line + word) > 80:
                        if current_line:
                            pdf.add_text(current_line)
                            current_line = word + " "
                        else:
                            pdf.add_text(word)
                    else:
                        current_line += word + " "
                if current_line:
                    pdf.add_text(current_line)
            else:
                pdf.add_text(line)

    # Attachment info
    if message.attachment_name:
        pdf.ln(10)
        pdf.add_field("Attachment", message.attachment_name)

    # Footer
    pdf.ln(20)
    pdf.set_font('Arial', 'I', 10)
    pdf.add_text(f"Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
    pdf.output(temp_file.name)

    return temp_file.name

def generate_messages_report(messages, title="Messages Report"):
    """Generate PDF report for multiple messages"""
    pdf = SimplePDF()

    # Header
    pdf.add_header(title)
    pdf.add_text(f"Number of messages: {len(messages)}")
    pdf.add_text(f"Report date: {datetime.now().strftime('%Y-%m-%d')}")
    pdf.ln(10)

    # Messages summary
    for i, message in enumerate(messages, 1):
        pdf.set_font('Arial', 'B', 11)
        pdf.add_text(f"{i}. {message.registration_number}")

        pdf.set_font('Arial', '', 10)
        pdf.add_text(f"Subject: {message.subject}")
        pdf.add_text(f"Destination: {message.destination}")
        pdf.add_text(f"Date: {message.message_date.strftime('%Y-%m-%d')}")
        pdf.add_text(f"Type: {'Incoming' if message.is_incoming() else 'Outgoing'}")
        pdf.ln(5)

        # Add page break every 10 messages
        if i % 10 == 0 and i < len(messages):
            pdf.add_page()

    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
    pdf.output(temp_file.name)

    return temp_file.name
