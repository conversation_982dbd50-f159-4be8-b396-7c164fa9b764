#!/usr/bin/env python3
"""
Fix database with correct UserRole enum values
إصلاح قاعدة البيانات بقيم الأدوار الصحيحة
"""

import os
import sys
from datetime import datetime

# Add current directory to path to import models
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_database_with_app():
    """Fix database using Flask app context"""
    print("🔧 إصلاح قاعدة البيانات باستخدام نماذج Flask...")
    
    try:
        from app import create_app
        from models import db, User, Role, Department, UserRole
        from werkzeug.security import generate_password_hash
        
        # Create app
        app = create_app()
        
        with app.app_context():
            print("🗑️ حذف الجداول القديمة...")
            db.drop_all()
            
            print("📊 إنشاء جداول جديدة...")
            db.create_all()
            
            print("👥 إضافة الأدوار الأساسية...")
            
            # Add default roles
            admin_role = Role(name='admin', description='مدير النظام')
            manager_role = Role(name='manager', description='مدير قسم')
            secretary_role = Role(name='secretary', description='سكرتير')
            employee_role = Role(name='employee', description='موظف')
            
            db.session.add_all([admin_role, manager_role, secretary_role, employee_role])
            db.session.commit()
            
            print("🏢 إضافة الأقسام الأساسية...")
            
            # Add default departments
            admin_dept = Department(name='الإدارة العامة', description='الإدارة العامة للمؤسسة')
            hr_dept = Department(name='الموارد البشرية', description='قسم الموارد البشرية')
            it_dept = Department(name='تقنية المعلومات', description='قسم تقنية المعلومات')
            
            db.session.add_all([admin_dept, hr_dept, it_dept])
            db.session.commit()
            
            print("👤 إضافة المستخدمين الأساسيين...")
            
            # Add default users with correct enum values
            admin_user = User(
                name='المدير العام',
                username='admin',
                role=UserRole.ADMIN,  # استخدام enum مباشرة
                role_id=admin_role.id,
                department_id=admin_dept.id,
                is_active=True
            )
            admin_user.set_password('admin123')
            
            manager_user = User(
                name='مدير القسم',
                username='manager',
                role=UserRole.MANAGER,  # استخدام enum مباشرة
                role_id=manager_role.id,
                department_id=admin_dept.id,
                is_active=True
            )
            manager_user.set_password('manager123')
            
            secretary_user = User(
                name='السكرتير',
                username='secretary',
                role=UserRole.SECRETARY,  # استخدام enum مباشرة
                role_id=secretary_role.id,
                department_id=hr_dept.id,
                is_active=True
            )
            secretary_user.set_password('secretary123')
            
            employee_user = User(
                name='موظف تجريبي',
                username='user',
                role=UserRole.EMPLOYEE,  # استخدام enum مباشرة
                role_id=employee_role.id,
                department_id=it_dept.id,
                is_active=True
            )
            employee_user.set_password('user123')
            
            db.session.add_all([admin_user, manager_user, secretary_user, employee_user])
            db.session.commit()
            
            print("✅ تم إصلاح قاعدة البيانات بنجاح!")
            print("=" * 60)
            print("👥 المستخدمون المتاحون:")
            print("   🔑 admin / admin123 (مدير النظام)")
            print("   👔 manager / manager123 (مدير قسم)")
            print("   📝 secretary / secretary123 (سكرتير)")
            print("   👤 user / user123 (موظف)")
            print("=" * 60)
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 إنشاء المجلدات المطلوبة...")
    
    directories = [
        'uploads',
        'uploads/signatures',
        'uploads/attachments',
        'uploads/2024',
        'uploads/2024/01',
        'uploads/2024/02',
        'uploads/2024/03',
        'uploads/2024/04',
        'uploads/2024/05',
        'uploads/2024/06',
        'uploads/2024/07',
        'uploads/2024/08',
        'uploads/2024/09',
        'uploads/2024/10',
        'uploads/2024/11',
        'uploads/2024/12',
        'instance',
        'static/css',
        'static/js',
        'static/images',
        'templates'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ {directory}")
    
    print("✅ تم إنشاء جميع المجلدات!")

def main():
    print("🚀 إصلاح النظام الكامل للمراسلات الإلكترونية")
    print("=" * 60)
    
    try:
        # Create directories
        create_directories()
        
        # Fix database with correct enum values
        if fix_database_with_app():
            print("=" * 60)
            print("✅ تم إصلاح النظام بنجاح!")
            print("🎯 يمكنك الآن تشغيل النظام الكامل:")
            print("   python app.py")
            print("=" * 60)
            print("🌐 الروابط بعد التشغيل:")
            print("   الصفحة الرئيسية: http://localhost:8585")
            print("   صفحة الدخول: http://localhost:8585/auth/login")
            print("   إدارة الرسائل: http://localhost:8585/messages")
            print("=" * 60)
            print("🔑 بيانات الدخول:")
            print("   👤 admin / admin123 (مدير النظام)")
            print("   👔 manager / manager123 (مدير قسم)")
            print("   📝 secretary / secretary123 (سكرتير)")
            print("   👤 user / user123 (موظف)")
            print("=" * 60)
        else:
            print("❌ فشل في إصلاح قاعدة البيانات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في التحضير: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    main()
