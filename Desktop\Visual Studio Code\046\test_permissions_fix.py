#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة صفحة الصلاحيات
"""

import requests
import sys

def test_permissions_fix():
    """اختبار إصلاح مشكلة صفحة الصلاحيات"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار الصفحة الرئيسية لإدارة الصلاحيات
        print("\n🛡️ اختبار الصفحة الرئيسية لإدارة الصلاحيات...")
        response = session.get(f"{base_url}/permissions")
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية لإدارة الصلاحيات تعمل")
            
            content = response.text
            if 'إدارة الصلاحيات' in content:
                print("✅ عنوان الصفحة صحيح")
            
            if 'إجمالي الصلاحيات' in content:
                print("✅ الإحصائيات تظهر بشكل صحيح")
                
        else:
            print(f"❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
            return False
        
        # اختبار صفحة قائمة الصلاحيات (التي كانت تحتوي على المشكلة)
        print("\n📋 اختبار صفحة قائمة الصلاحيات...")
        response = session.get(f"{base_url}/permissions/permissions")
        if response.status_code == 200:
            print("✅ صفحة قائمة الصلاحيات تعمل بشكل صحيح")
            
            content = response.text
            if 'إدارة الصلاحيات' in content:
                print("✅ محتوى الصفحة يظهر بشكل صحيح")
            
            if 'إجمالي الصلاحيات' in content:
                print("✅ الإحصائيات تعمل بدون أخطاء")
            
            if 'فئات الصلاحيات' in content:
                print("✅ تجميع الصلاحيات يعمل بشكل صحيح")
                
        else:
            print(f"❌ فشل في الوصول لصفحة قائمة الصلاحيات: {response.status_code}")
            return False
        
        # اختبار صفحة قائمة الأدوار
        print("\n👥 اختبار صفحة قائمة الأدوار...")
        response = session.get(f"{base_url}/permissions/roles")
        if response.status_code == 200:
            print("✅ صفحة قائمة الأدوار تعمل")
        else:
            print(f"⚠️ مشكلة في صفحة قائمة الأدوار: {response.status_code}")
        
        # اختبار إنشاء دور جديد
        print("\n➕ اختبار صفحة إنشاء دور جديد...")
        response = session.get(f"{base_url}/permissions/roles/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء دور جديد تعمل")
        else:
            print(f"⚠️ مشكلة في صفحة إنشاء دور جديد: {response.status_code}")
        
        # اختبار تهيئة الصلاحيات الافتراضية
        print("\n🔧 اختبار تهيئة الصلاحيات الافتراضية...")
        response = session.post(f"{base_url}/permissions/init-permissions")
        if response.status_code == 200:
            print("✅ تهيئة الصلاحيات الافتراضية تعمل")
        else:
            print(f"⚠️ مشكلة في تهيئة الصلاحيات: {response.status_code}")
        
        # اختبار نهائي لصفحة الصلاحيات بعد التهيئة
        print("\n🔄 اختبار نهائي لصفحة الصلاحيات...")
        response = session.get(f"{base_url}/permissions/permissions")
        if response.status_code == 200:
            content = response.text
            
            # تحقق من وجود فئات الصلاحيات
            categories_found = []
            if 'صلاحيات الرسائل' in content:
                categories_found.append("الرسائل")
            if 'صلاحيات المستخدمين' in content:
                categories_found.append("المستخدمين")
            if 'صلاحيات الأقسام' in content:
                categories_found.append("الأقسام")
            if 'صلاحيات النظام' in content:
                categories_found.append("النظام")
            
            if categories_found:
                print(f"✅ فئات الصلاحيات الموجودة: {', '.join(categories_found)}")
            else:
                print("⚠️ لم يتم العثور على فئات صلاحيات")
            
            # تحقق من عدم وجود أخطاء في الإحصائيات
            if 'TypeError' not in content and 'Error' not in content:
                print("✅ لا توجد أخطاء في الصفحة")
            else:
                print("⚠️ قد تكون هناك أخطاء في الصفحة")
                
        else:
            print(f"❌ فشل في الاختبار النهائي: {response.status_code}")
            return False
        
        print("\n🎉 تم اكتمال اختبار إصلاح صفحة الصلاحيات!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار إصلاح مشكلة صفحة الصلاحيات")
    print("=" * 55)
    
    success = test_permissions_fix()
    
    if success:
        print("\n✅ تم إصلاح مشكلة صفحة الصلاحيات بنجاح!")
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح خطأ TypeError في حساب إجمالي الصلاحيات")
        print("  ✅ استبدال sum(attribute='__len__') بـ map('length') | sum")
        print("  ✅ التأكد من عمل جميع صفحات إدارة الصلاحيات")
        print("  ✅ تهيئة الصلاحيات الافتراضية")
        print("\n🌐 صفحات إدارة الصلاحيات تعمل الآن:")
        print("  🛡️ الصفحة الرئيسية: http://localhost:8585/permissions")
        print("  📋 قائمة الصلاحيات: http://localhost:8585/permissions/permissions")
        print("  👥 قائمة الأدوار: http://localhost:8585/permissions/roles")
        print("  ➕ إنشاء دور جديد: http://localhost:8585/permissions/roles/new")
        print("\n🎯 خطأ TypeError تم حله!")
        print("\n💡 الميزات المتاحة:")
        print("  📊 إحصائيات الصلاحيات والأدوار")
        print("  🗂️ تجميع الصلاحيات حسب الفئة")
        print("  ➕ إنشاء وتعديل الأدوار")
        print("  🔧 تهيئة الصلاحيات الافتراضية")
        print("  👤 إدارة صلاحيات المستخدمين")
        sys.exit(0)
    else:
        print("\n❌ فشل في إصلاح مشكلة صفحة الصلاحيات!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم")
        print("  • صحة قاعدة البيانات")
        print("  • عدم وجود أخطاء في القوالب")
        sys.exit(1)
