{% extends "base.html" %}

{% block title %}إدارة الأقسام - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-building me-2"></i>
                إدارة الأقسام
            </h1>
            <a href="{{ url_for('departments.new_department') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                قسم جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ stats.total }}</h3>
                <p class="mb-0">إجمالي الأقسام</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ stats.active }}</h3>
                <p class="mb-0">الأقسام النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h3>{{ stats.inactive }}</h3>
                <p class="mb-0">الأقسام غير النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ stats.with_manager }}</h3>
                <p class="mb-0">لها مدراء</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" name="search" class="form-control" placeholder="اسم القسم أو الوصف" value="{{ request.args.get('search', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if request.args.get('status') == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if request.args.get('status') == 'inactive' %}selected{% endif %}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ url_for('departments.list_departments') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-success w-100" onclick="exportDepartments()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Departments Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if departments.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم القسم</th>
                                <th>الوصف</th>
                                <th>مدير القسم</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for department in departments.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="department-icon me-2">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div>
                                            <strong>{{ department.name }}</strong>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if department.description %}
                                        {{ department.description[:50] }}{% if department.description|length > 50 %}...{% endif %}
                                    {% else %}
                                        <span class="text-muted">لا يوجد وصف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if department.manager_id %}
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-2">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            {{ department.manager.name if department.manager else 'غير محدد' }}
                                        </div>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if department.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ department.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('departments.view_department', id=department.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('departments.edit_department', id=department.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if current_user.is_manager() %}
                                        <form method="POST" action="{{ url_for('departments.toggle_department_status', id=department.id) }}" 
                                              style="display: inline;">
                                            <button type="submit" 
                                                    class="btn btn-outline-{{ 'secondary' if department.is_active else 'success' }} btn-sm"
                                                    title="{{ 'إلغاء التفعيل' if department.is_active else 'تفعيل' }}">
                                                <i class="fas fa-{{ 'ban' if department.is_active else 'check' }}"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if departments.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if departments.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('departments.list_departments', page=departments.prev_num, **request.args) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in departments.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != departments.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('departments.list_departments', page=page_num, **request.args) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if departments.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('departments.list_departments', page=departments.next_num, **request.args) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-building fa-4x mb-3"></i>
                    <h4>لا توجد أقسام</h4>
                    <p>لم يتم إنشاء أي أقسام حتى الآن</p>
                    <a href="{{ url_for('departments.new_department') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء أول قسم
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.department-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    background-color: #0d6efd;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.avatar-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function exportDepartments() {
    // This would typically generate a report
    alert('سيتم إضافة وظيفة التصدير قريباً');
}
</script>
{% endblock %}
