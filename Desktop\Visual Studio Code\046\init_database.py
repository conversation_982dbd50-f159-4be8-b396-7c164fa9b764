#!/usr/bin/env python3
"""
Script to initialize the database with all tables and default data
"""

from app import create_app
from models import db, Permission, Role, User, UserRole, Department
import os

def init_database():
    """Initialize database with all tables and default data"""
    app = create_app()
    
    with app.app_context():
        print("🚀 Initializing database...")
        
        # Create all tables
        print("📊 Creating database tables...")
        db.create_all()
        print("   ✓ All tables created successfully")
        
        # Create uploads directory
        uploads_dir = os.path.join(os.getcwd(), 'uploads')
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
            print("   ✓ Uploads directory created")
        
        # Create signatures directory
        signatures_dir = os.path.join(uploads_dir, 'signatures')
        if not os.path.exists(signatures_dir):
            os.makedirs(signatures_dir)
            print("   ✓ Signatures directory created")
        
        # Initialize permissions
        print("\n🔐 Creating default permissions...")
        default_permissions = [
            # صلاحيات الرسائل
            ('create_message', 'إنشاء رسالة', 'إمكانية إنشاء رسائل جديدة', 'الرسائل'),
            ('view_message', 'عرض الرسائل', 'إمكانية عرض الرسائل', 'الرسائل'),
            ('edit_message', 'تعديل الرسائل', 'إمكانية تعديل الرسائل', 'الرسائل'),
            ('delete_message', 'حذف الرسائل', 'إمكانية حذف الرسائل', 'الرسائل'),
            ('approve_message', 'اعتماد الرسائل', 'إمكانية اعتماد الرسائل', 'الرسائل'),
            ('archive_message', 'أرشفة الرسائل', 'إمكانية أرشفة الرسائل', 'الرسائل'),
            
            # صلاحيات المستخدمين
            ('create_user', 'إنشاء مستخدم', 'إمكانية إنشاء مستخدمين جدد', 'المستخدمين'),
            ('view_user', 'عرض المستخدمين', 'إمكانية عرض بيانات المستخدمين', 'المستخدمين'),
            ('edit_user', 'تعديل المستخدمين', 'إمكانية تعديل بيانات المستخدمين', 'المستخدمين'),
            ('delete_user', 'حذف المستخدمين', 'إمكانية حذف المستخدمين', 'المستخدمين'),
            ('manage_roles', 'إدارة الأدوار', 'إمكانية إدارة أدوار المستخدمين', 'المستخدمين'),
            
            # صلاحيات الأقسام
            ('create_department', 'إنشاء قسم', 'إمكانية إنشاء أقسام جديدة', 'الأقسام'),
            ('view_department', 'عرض الأقسام', 'إمكانية عرض الأقسام', 'الأقسام'),
            ('edit_department', 'تعديل الأقسام', 'إمكانية تعديل الأقسام', 'الأقسام'),
            ('delete_department', 'حذف الأقسام', 'إمكانية حذف الأقسام', 'الأقسام'),
            
            # صلاحيات النظام
            ('view_reports', 'عرض التقارير', 'إمكانية عرض التقارير', 'النظام'),
            ('manage_system', 'إدارة النظام', 'إمكانية إدارة إعدادات النظام', 'النظام'),
            ('view_logs', 'عرض السجلات', 'إمكانية عرض سجلات النظام', 'النظام'),
            ('backup_system', 'نسخ احتياطي', 'إمكانية إنشاء نسخ احتياطية', 'النظام'),
        ]
        
        permissions_created = 0
        for name, display_name, description, category in default_permissions:
            if not Permission.query.filter_by(name=name).first():
                permission = Permission(
                    name=name,
                    display_name=display_name,
                    description=description,
                    category=category
                )
                db.session.add(permission)
                permissions_created += 1
                print(f"   ✓ Created permission: {display_name}")
        
        # Initialize roles
        print("\n👥 Creating default roles...")
        default_roles = [
            ('admin_role', 'مدير النظام', 'دور مدير النظام مع جميع الصلاحيات', True, [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user', 'delete_user', 'manage_roles',
                'create_department', 'view_department', 'edit_department', 'delete_department',
                'view_reports', 'manage_system', 'view_logs', 'backup_system'
            ]),
            ('manager_role', 'مدير', 'دور المدير مع صلاحيات إدارية', True, [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user',
                'view_department', 'edit_department',
                'view_reports'
            ]),
            ('secretary_role', 'سكرتير', 'دور السكرتير مع صلاحيات محدودة', True, [
                'create_message', 'view_message', 'edit_message', 'archive_message',
                'view_user', 'edit_user',
                'view_department'
            ]),
            ('employee_role', 'موظف', 'دور الموظف العادي', True, [
                'create_message', 'view_message', 'edit_message',
                'view_user', 'view_department'
            ])
        ]
        
        roles_created = 0
        for name, display_name, description, is_system, permission_names in default_roles:
            if not Role.query.filter_by(name=name).first():
                role = Role(
                    name=name,
                    display_name=display_name,
                    description=description,
                    is_system_role=is_system
                )
                
                # Add permissions to role
                for permission_name in permission_names:
                    permission = Permission.query.filter_by(name=permission_name).first()
                    if permission:
                        role.add_permission(permission)
                
                db.session.add(role)
                roles_created += 1
                print(f"   ✓ Created role: {display_name} with {len(permission_names)} permissions")
        
        # Create default department
        print("\n🏢 Creating default department...")
        if not Department.query.filter_by(name='الإدارة العامة').first():
            department = Department(
                name='الإدارة العامة',
                description='القسم الرئيسي للإدارة العامة'
            )
            db.session.add(department)
            print("   ✓ Created default department: الإدارة العامة")
        
        # Create admin user if not exists
        print("\n👤 Creating admin user...")
        if not User.query.filter_by(username='admin').first():
            admin_department = Department.query.filter_by(name='الإدارة العامة').first()
            admin_role = Role.query.filter_by(name='admin_role').first()
            
            admin_user = User(
                name='مدير النظام',
                username='admin',
                role=UserRole.ADMIN,
                department_id=admin_department.id if admin_department else None,
                role_id=admin_role.id if admin_role else None
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            print("   ✓ Created admin user (username: admin, password: admin123)")
        
        # Commit all changes
        try:
            db.session.commit()
            print(f"\n✅ Database initialization completed successfully!")
            print(f"   - {permissions_created} permissions created")
            print(f"   - {roles_created} roles created")
            print(f"   - Default department and admin user created")
            print(f"\n🔑 Login credentials:")
            print(f"   Username: admin")
            print(f"   Password: admin123")
            print(f"\n🌐 Access the system at: http://localhost:5000")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error during initialization: {str(e)}")
            return False
        
        return True

if __name__ == '__main__':
    print("🎯 Electronic Correspondence System - Database Initialization")
    print("=" * 60)
    
    if init_database():
        print("\n🎉 Initialization completed successfully!")
        print("You can now start the application with: python app.py")
    else:
        print("\n💥 Initialization failed!")
        print("Please check the error messages above.")
