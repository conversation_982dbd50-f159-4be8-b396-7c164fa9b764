#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مسارات إعدادات النظام
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, SystemSettings, User
from functools import wraps

settings_bp = Blueprint('settings', __name__, url_prefix='/settings')

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات الإدارة"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        if not (current_user.is_admin() or current_user.is_director()):
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

@settings_bp.route('/')
@login_required
@admin_required
def index():
    """الصفحة الرئيسية للإعدادات"""
    # إحصائيات الإعدادات
    total_settings = SystemSettings.query.count()
    active_settings = SystemSettings.query.filter_by(is_active=True).count()
    color_settings = SystemSettings.query.filter_by(category='colors').count()
    display_settings = SystemSettings.query.filter_by(category='display').count()
    
    stats = {
        'total': total_settings,
        'active': active_settings,
        'colors': color_settings,
        'display': display_settings
    }
    
    # الإعدادات الحديثة
    recent_settings = SystemSettings.query.order_by(
        SystemSettings.updated_at.desc()
    ).limit(5).all()
    
    return render_template('settings/index.html', 
                         stats=stats, 
                         recent_settings=recent_settings)

@settings_bp.route('/colors', methods=['GET', 'POST'])
@login_required
@admin_required
def colors():
    """إعدادات الألوان"""
    if request.method == 'POST':
        # حفظ إعدادات الألوان
        color_keys = [
            'primary_color', 'secondary_color', 'success_color', 'danger_color',
            'warning_color', 'info_color', 'light_color', 'dark_color',
            'sidebar_bg', 'navbar_bg', 'card_bg', 'text_color'
        ]
        
        for key in color_keys:
            value = request.form.get(key)
            if value:
                # البحث عن الإعداد الموجود أو إنشاء جديد
                setting = SystemSettings.query.filter_by(setting_key=key).first()
                if setting:
                    setting.setting_value = value
                    setting.updated_by = current_user.id
                else:
                    setting = SystemSettings(
                        setting_key=key,
                        setting_value=value,
                        setting_type='color',
                        category='colors',
                        display_name=get_color_display_name(key),
                        description=f'لون {get_color_display_name(key)}',
                        updated_by=current_user.id
                    )
                    db.session.add(setting)
        
        db.session.commit()
        flash('تم حفظ إعدادات الألوان بنجاح', 'success')
        return redirect(url_for('settings.colors'))
    
    # الحصول على الألوان الحالية
    current_colors = SystemSettings.get_color_settings()
    
    return render_template('settings/colors.html', colors=current_colors)

@settings_bp.route('/display', methods=['GET', 'POST'])
@login_required
@admin_required
def display():
    """إعدادات العرض"""
    if request.method == 'POST':
        # حفظ إعدادات العرض
        display_keys = [
            'show_days_only', 'date_format', 'time_format', 
            'items_per_page', 'show_sidebar', 'compact_mode'
        ]
        
        for key in display_keys:
            if key == 'show_days_only':
                value = 'true' if request.form.get(key) else 'false'
            elif key == 'show_sidebar':
                value = 'true' if request.form.get(key) else 'false'
            elif key == 'compact_mode':
                value = 'true' if request.form.get(key) else 'false'
            else:
                value = request.form.get(key)
            
            if value is not None:
                setting = SystemSettings.query.filter_by(setting_key=key).first()
                if setting:
                    setting.setting_value = value
                    setting.updated_by = current_user.id
                else:
                    setting = SystemSettings(
                        setting_key=key,
                        setting_value=value,
                        setting_type=get_setting_type(key),
                        category='display',
                        display_name=get_display_name(key),
                        description=f'إعداد {get_display_name(key)}',
                        updated_by=current_user.id
                    )
                    db.session.add(setting)
        
        db.session.commit()
        flash('تم حفظ إعدادات العرض بنجاح', 'success')
        return redirect(url_for('settings.display'))
    
    # الحصول على إعدادات العرض الحالية
    current_display = SystemSettings.get_display_settings()
    
    return render_template('settings/display.html', display=current_display)

@settings_bp.route('/general', methods=['GET', 'POST'])
@login_required
@admin_required
def general():
    """الإعدادات العامة"""
    if request.method == 'POST':
        # حفظ الإعدادات العامة
        general_keys = [
            'system_name', 'system_description', 'contact_email', 
            'contact_phone', 'address', 'logo_url'
        ]
        
        for key in general_keys:
            value = request.form.get(key)
            if value is not None:
                setting = SystemSettings.query.filter_by(setting_key=key).first()
                if setting:
                    setting.setting_value = value
                    setting.updated_by = current_user.id
                else:
                    setting = SystemSettings(
                        setting_key=key,
                        setting_value=value,
                        setting_type='text',
                        category='general',
                        display_name=get_general_display_name(key),
                        description=f'إعداد {get_general_display_name(key)}',
                        updated_by=current_user.id
                    )
                    db.session.add(setting)
        
        db.session.commit()
        flash('تم حفظ الإعدادات العامة بنجاح', 'success')
        return redirect(url_for('settings.general'))
    
    # الحصول على الإعدادات العامة الحالية
    general_settings = SystemSettings.query.filter_by(category='general').all()
    current_general = {}
    for setting in general_settings:
        current_general[setting.setting_key] = setting.setting_value
    
    return render_template('settings/general.html', general=current_general)

@settings_bp.route('/reset', methods=['POST'])
@login_required
@admin_required
def reset_settings():
    """إعادة تعيين الإعدادات للقيم الافتراضية"""
    category = request.form.get('category', 'all')
    
    try:
        if category == 'all':
            SystemSettings.query.delete()
        else:
            SystemSettings.query.filter_by(category=category).delete()
        
        db.session.commit()
        flash(f'تم إعادة تعيين إعدادات {category} للقيم الافتراضية', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إعادة التعيين: {str(e)}', 'error')
    
    return redirect(url_for('settings.index'))

@settings_bp.route('/api/colors')
@login_required
def api_colors():
    """API للحصول على إعدادات الألوان"""
    colors = SystemSettings.get_color_settings()
    return jsonify(colors)

@settings_bp.route('/api/display')
@login_required
def api_display():
    """API للحصول على إعدادات العرض"""
    display = SystemSettings.get_display_settings()
    return jsonify(display)

def get_color_display_name(key):
    """الحصول على الاسم المعروض للون"""
    names = {
        'primary_color': 'اللون الأساسي',
        'secondary_color': 'اللون الثانوي',
        'success_color': 'لون النجاح',
        'danger_color': 'لون الخطر',
        'warning_color': 'لون التحذير',
        'info_color': 'لون المعلومات',
        'light_color': 'اللون الفاتح',
        'dark_color': 'اللون الداكن',
        'sidebar_bg': 'خلفية الشريط الجانبي',
        'navbar_bg': 'خلفية شريط التنقل',
        'card_bg': 'خلفية البطاقات',
        'text_color': 'لون النص'
    }
    return names.get(key, key)

def get_display_name(key):
    """الحصول على الاسم المعروض لإعداد العرض"""
    names = {
        'show_days_only': 'عرض الأيام كأرقام فقط',
        'date_format': 'تنسيق التاريخ',
        'time_format': 'تنسيق الوقت',
        'items_per_page': 'عدد العناصر في الصفحة',
        'show_sidebar': 'إظهار الشريط الجانبي',
        'compact_mode': 'الوضع المضغوط'
    }
    return names.get(key, key)

def get_general_display_name(key):
    """الحصول على الاسم المعروض للإعداد العام"""
    names = {
        'system_name': 'اسم النظام',
        'system_description': 'وصف النظام',
        'contact_email': 'البريد الإلكتروني',
        'contact_phone': 'رقم الهاتف',
        'address': 'العنوان',
        'logo_url': 'رابط الشعار'
    }
    return names.get(key, key)

def get_setting_type(key):
    """الحصول على نوع الإعداد"""
    types = {
        'show_days_only': 'boolean',
        'show_sidebar': 'boolean',
        'compact_mode': 'boolean',
        'items_per_page': 'number',
        'date_format': 'text',
        'time_format': 'text'
    }
    return types.get(key, 'text')
