{% extends "base.html" %}

{% block title %}الرسائل المعلقة للاعتماد - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-stamp me-2"></i>
                {{ t('awaiting_approval') }}
            </h1>
            <div>
                <a href="{{ url_for('messages.list_messages') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    {{ t('back') }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ messages.total }}</h3>
                <p class="mb-0">{{ t('awaiting_approval') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h3>{{ messages.items|selectattr('is_urgent', 'equalto', true)|list|length }}</h3>
                <p class="mb-0">{{ t('urgent') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ messages.items|selectattr('message_type.value', 'equalto', 'internal')|list|length }}</h3>
                <p class="mb-0">{{ t('internal') }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Messages Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    الرسائل المعلقة للاعتماد
                </h5>
            </div>
            <div class="card-body">
                {% if messages.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ t('registration_number') }}</th>
                                <th>{{ t('subject') }}</th>
                                <th>{{ t('message_type') }}</th>
                                <th>المرسل</th>
                                <th>{{ t('message_date') }}</th>
                                <th>{{ t('priority') }}</th>
                                <th>{{ t('actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in messages.items %}
                            <tr class="{% if message.is_urgent %}table-danger{% elif message.priority == 'high' %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ message.registration_number }}</strong>
                                    {% if message.is_urgent %}
                                        <i class="fas fa-exclamation-triangle text-danger ms-1" title="{{ t('urgent') }}"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject[:50] }}{% if message.subject|length > 50 %}...{% endif %}
                                    </a>
                                </td>
                                <td>
                                    {% if message.is_incoming() %}
                                        <span class="badge bg-success">{{ t('incoming') }}</span>
                                    {% elif message.is_outgoing() %}
                                        <span class="badge bg-info">{{ t('outgoing') }}</span>
                                    {% else %}
                                        <span class="badge bg-warning">{{ t('internal') }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        {{ message.creator.name }}
                                    </div>
                                </td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if message.priority == 'urgent' %}
                                        <span class="badge bg-danger">{{ t('urgent') }}</span>
                                    {% elif message.priority == 'high' %}
                                        <span class="badge bg-warning">{{ t('high') }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ t('normal') }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                           class="btn btn-outline-primary" title="{{ t('view') }}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-success" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#quickApproveModal{{ message.id }}"
                                                title="{{ t('approve') }}">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#quickRejectModal{{ message.id }}"
                                                title="{{ t('reject') }}">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if messages.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if messages.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('messages.pending_approval', page=messages.prev_num) }}">{{ t('previous') }}</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in messages.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != messages.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('messages.pending_approval', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if messages.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('messages.pending_approval', page=messages.next_num) }}">{{ t('next') }}</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-check-circle fa-4x mb-3 text-success"></i>
                    <h4>لا توجد رسائل معلقة للاعتماد</h4>
                    <p>جميع الرسائل تم اعتمادها أو لا تتطلب اعتماد</p>
                    <a href="{{ url_for('messages.list_messages') }}" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>
                        عرض جميع الرسائل
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Approval/Rejection Modals -->
{% for message in messages.items %}
<!-- Quick Approve Modal -->
<div class="modal fade" id="quickApproveModal{{ message.id }}" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title">{{ t('approve') }}</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('messages.approve_message', id=message.id) }}">
                <div class="modal-body">
                    <p class="mb-2"><strong>{{ message.registration_number }}</strong></p>
                    <p class="mb-3">{{ message.subject[:50] }}...</p>
                    <textarea class="form-control" name="approval_notes" rows="2" 
                              placeholder="ملاحظات (اختياري)"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">{{ t('cancel') }}</button>
                    <button type="submit" class="btn btn-success btn-sm">{{ t('approve') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Quick Reject Modal -->
<div class="modal fade" id="quickRejectModal{{ message.id }}" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title">{{ t('reject') }}</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('messages.reject_message', id=message.id) }}">
                <div class="modal-body">
                    <p class="mb-2"><strong>{{ message.registration_number }}</strong></p>
                    <p class="mb-3">{{ message.subject[:50] }}...</p>
                    <textarea class="form-control" name="approval_notes" rows="2" 
                              placeholder="سبب الرفض *" required></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">{{ t('cancel') }}</button>
                    <button type="submit" class="btn btn-danger btn-sm">{{ t('reject') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}
</style>
{% endblock %}
