{% extends "base.html" %}

{% block title %}عرض الرسالة {{ message.registration_number }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-envelope me-2"></i>
                تفاصيل الرسالة
            </h1>
            <div>
                <a href="{{ url_for('messages.list_messages') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
                {% if current_user.is_manager() or message.created_by == current_user.id %}
                <a href="{{ url_for('messages.edit_message', id=message.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                {% endif %}
                <a href="{{ url_for('messages.download_pdf', id=message.id) }}" class="btn btn-success">
                    <i class="fas fa-file-pdf me-2"></i>
                    تحميل PDF
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">معلومات الرسالة</h5>
                <div>
                    {% if message.is_incoming() %}
                        <span class="badge bg-success fs-6">رسالة واردة</span>
                    {% else %}
                        <span class="badge bg-info fs-6">رسالة صادرة</span>
                    {% endif %}
                    
                    {% if message.priority == 'urgent' %}
                        <span class="badge bg-danger fs-6">عاجل</span>
                    {% elif message.priority == 'high' %}
                        <span class="badge bg-warning fs-6">مهم</span>
                    {% else %}
                        <span class="badge bg-secondary fs-6">عادي</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم التسجيل:</strong>
                        <p class="mb-0">{{ message.registration_number }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الرسالة:</strong>
                        <p class="mb-0">{{ message.message_date.strftime('%Y-%m-%d') }}</p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الجهة:</strong>
                        <p class="mb-0">{{ message.destination }}</p>
                    </div>
                    {% if message.department %}
                    <div class="col-md-6">
                        <strong>القسم:</strong>
                        <p class="mb-0">{{ message.department }}</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Internal Message Specific Fields -->
                {% if message.is_internal() %}
                <div class="row mb-3">
                    {% if message.from_department %}
                    <div class="col-md-6">
                        <strong>من القسم:</strong>
                        <p class="mb-0">{{ message.from_department }}</p>
                    </div>
                    {% endif %}
                    {% if message.to_department %}
                    <div class="col-md-6">
                        <strong>إلى القسم:</strong>
                        <p class="mb-0">{{ message.to_department }}</p>
                    </div>
                    {% endif %}
                </div>

                {% if message.assigned_to or message.due_date %}
                <div class="row mb-3">
                    {% if message.assigned_to %}
                    <div class="col-md-6">
                        <strong>المكلف بالرد:</strong>
                        <p class="mb-0">{{ message.assigned_user.name if message.assigned_user else 'غير محدد' }}</p>
                    </div>
                    {% endif %}
                    {% if message.due_date %}
                    <div class="col-md-6">
                        <strong>تاريخ الاستحقاق:</strong>
                        <p class="mb-0 {% if message.due_date < moment().date() %}text-danger{% endif %}">
                            {{ message.due_date.strftime('%Y-%m-%d') }}
                            {% if message.due_date < moment().date() %}
                                <i class="fas fa-exclamation-triangle ms-1" title="متأخر"></i>
                            {% endif %}
                        </p>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                {% if message.is_urgent or message.requires_response %}
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>خصائص المراسلة:</strong>
                        <div class="mt-1">
                            {% if message.is_urgent %}
                                <span class="badge bg-danger me-1">
                                    <i class="fas fa-exclamation-triangle me-1"></i>عاجل
                                </span>
                            {% endif %}
                            {% if message.requires_response %}
                                <span class="badge bg-warning me-1">
                                    <i class="fas fa-reply me-1"></i>يتطلب رد
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
                {% endif %}
                
                <div class="mb-3">
                    <strong>الموضوع:</strong>
                    <p class="mb-0">{{ message.subject }}</p>
                </div>
                
                {% if message.content %}
                <div class="mb-3">
                    <strong>المحتوى:</strong>
                    <div class="border rounded p-3 bg-light">
                        {{ message.content|nl2br }}
                    </div>
                </div>
                {% endif %}
                
                {% if message.attachment_name %}
                <div class="mb-3">
                    <strong>المرفق:</strong>
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas {{ get_file_icon(message.attachment_name) }} me-2 text-primary"></i>
                            <span>{{ message.attachment_name }}</span>
                        </div>
                        <div class="btn-group btn-group-sm">
                            {% set extension = message.attachment_name.split('.')[-1].lower() %}
                            {% if extension in ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'txt', 'html', 'xml', 'json', 'css', 'js', 'mp3', 'wav', 'mp4', 'webm'] %}
                            <a href="{{ url_for('messages.view_attachment', filename=message.attachment_path) }}"
                               class="btn btn-outline-primary" target="_blank">
                                <i class="fas fa-eye me-1"></i>
                                عرض
                            </a>
                            {% endif %}
                            <a href="{{ url_for('messages.download_attachment', filename=message.attachment_path) }}"
                               class="btn btn-outline-success">
                                <i class="fas fa-download me-1"></i>
                                تحميل
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Approval Section -->
        {% if message.requires_approval or message.approval_status %}
        <div class="card mt-4 approval-section">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-stamp me-2"></i>
                    {{ t('approval_section') }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>{{ t('approval_status') }}:</strong>
                        <div class="mt-1">
                            {% if message.approval_status.value == 'pending' %}
                                <span class="badge bg-warning approval-badge">
                                    <i class="fas fa-clock me-1"></i>{{ t('awaiting_approval') }}
                                </span>
                            {% elif message.approval_status.value == 'approved' %}
                                <span class="badge bg-success approval-badge">
                                    <i class="fas fa-check-circle me-1"></i>{{ t('approved') }}
                                </span>
                            {% elif message.approval_status.value == 'rejected' %}
                                <span class="badge bg-danger approval-badge">
                                    <i class="fas fa-times-circle me-1"></i>{{ t('rejected') }}
                                </span>
                            {% elif message.approval_status.value == 'auto_approved' %}
                                <span class="badge bg-info approval-badge">
                                    <i class="fas fa-robot me-1"></i>{{ t('auto_approved') }}
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    {% if message.approved_by %}
                    <div class="col-md-6 mb-3">
                        <strong>{{ t('approved_by') }}:</strong>
                        <div class="mt-1">
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <div>{{ message.approver.name }}</div>
                                    <small class="text-muted">{{ message.approver.role.value }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                {% if message.approval_date %}
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>{{ t('approval_date') }}:</strong>
                        <div class="mt-1">{{ message.approval_date.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                </div>
                {% endif %}

                {% if message.approval_notes %}
                <div class="row">
                    <div class="col-12 mb-3">
                        <strong>{{ t('approval_notes') }}:</strong>
                        <div class="mt-1 p-2 bg-light rounded">{{ message.approval_notes }}</div>
                    </div>
                </div>
                {% endif %}

                <!-- Approval Actions -->
                {% if message.is_pending_approval() and message.can_be_approved_by(current_user) %}
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>{{ t('approval_required') }}:</strong> هذه الرسالة تتطلب اعتمادك
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal">
                                <i class="fas fa-check me-2"></i>{{ t('approve') }}
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                <i class="fas fa-times me-2"></i>{{ t('reject') }}
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Signatures Section -->
                {% if false %}
                <div class="mt-4 pt-3 border-top">
                    <h6 class="mb-3">
                        <i class="fas fa-signature me-2"></i>
                        التوقيعات الإلكترونية
                    </h6>

                    <div class="row">
                        <!-- Creator Signature -->
                        <div class="col-md-6 mb-3">
                            <div class="signature-container">
                                <strong>توقيع منشئ الرسالة:</strong>
                                <div class="signature-display mt-2 p-3 border rounded bg-white text-center" style="min-height: 100px;">
                                    {% if message.creator.signature_type == 'digital' %}
                                        <canvas id="creatorSignatureCanvas" width="250" height="80" style="max-width: 100%;"></canvas>
                                    {% else %}
                                        <img src="{{ url_for('users.get_signature_image', user_id=message.creator.id) }}"
                                             alt="توقيع {{ message.creator.name }}" class="img-fluid" style="max-height: 80px;">
                                    {% endif %}
                                </div>
                                <div class="text-center mt-2">
                                    <small class="text-muted">{{ message.creator.name }}</small><br>
                                    <small class="text-muted">{{ message.date_created.strftime('%Y-%m-%d') }}</small>
                                </div>
                            </div>
                        </div>

                        <!-- Approver Signature -->
                        {% if false %}
                        <div class="col-md-6 mb-3">
                            <div class="signature-container">
                                <strong>توقيع المعتمد:</strong>
                                <div class="signature-display mt-2 p-3 border rounded bg-white text-center" style="min-height: 100px;">
                                    {% if message.approver.signature_type == 'digital' %}
                                        <canvas id="approverSignatureCanvas" width="250" height="80" style="max-width: 100%;"></canvas>
                                    {% else %}
                                        <img src="{{ url_for('users.get_signature_image', user_id=message.approver.id) }}"
                                             alt="توقيع {{ message.approver.name }}" class="img-fluid" style="max-height: 80px;">
                                    {% endif %}
                                </div>
                                <div class="text-center mt-2">
                                    <small class="text-muted">{{ message.approver.name }}</small><br>
                                    <small class="text-muted">{{ message.approval_date.strftime('%Y-%m-%d') if message.approval_date else '' }}</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Internal Message Responses -->
        {% if message.is_internal() and message.responses %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    الردود على المراسلة
                </h5>
            </div>
            <div class="card-body">
                {% for response in message.responses %}
                <div class="border rounded p-3 mb-3 {% if loop.last %}mb-0{% endif %}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <strong>{{ response.responder.name }}</strong>
                            <span class="badge bg-{{ 'success' if response.status == 'approved' else 'warning' if response.status == 'pending' else 'danger' }} ms-2">
                                {% if response.status == 'approved' %}موافق عليه
                                {% elif response.status == 'pending' %}معلق
                                {% else %}مرفوض{% endif %}
                            </span>
                        </div>
                        <small class="text-muted">{{ response.response_date.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    <div class="mb-2">
                        {{ response.response_content|nl2br }}
                    </div>
                    {% if response.attachment_name %}
                    <div class="mt-2">
                        <i class="fas {{ get_file_icon(response.attachment_name) }} me-2 text-primary"></i>
                        <a href="{{ url_for('messages.download_attachment', filename=response.attachment_path) }}"
                           class="text-decoration-none">
                            {{ response.attachment_name }}
                        </a>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">معلومات إضافية</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>منشئ الرسالة:</strong>
                    <p class="mb-0">{{ message.creator.name }}</p>
                </div>
                
                <div class="mb-3">
                    <strong>تاريخ الإنشاء:</strong>
                    <p class="mb-0">{{ message.date_created.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                
                <div class="mb-3">
                    <strong>السنة:</strong>
                    <p class="mb-0">{{ message.year }}</p>
                </div>
                
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <span class="badge bg-success">نشط</span>
                </div>
            </div>
        </div>
        
        <!-- Actions Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">الإجراءات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('messages.download_pdf', id=message.id) }}"
                       class="btn btn-outline-success">
                        <i class="fas fa-file-pdf me-2"></i>
                        تحميل PDF
                    </a>

                    {% if message.is_internal() and message.requires_response and not message.responses and (current_user.id == message.assigned_to or current_user.is_manager()) %}
                    <a href="{{ url_for('messages.respond_internal', id=message.id) }}"
                       class="btn btn-primary">
                        <i class="fas fa-reply me-2"></i>
                        رد على المراسلة
                    </a>
                    {% endif %}

                    {% if current_user.is_manager() or message.created_by == current_user.id %}
                    <a href="{{ url_for('messages.edit_message', id=message.id) }}"
                       class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الرسالة
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    
                    {% if current_user.can_delete_messages() %}
                    <form method="POST" action="{{ url_for('messages.delete_message', id=message.id) }}" 
                          onsubmit="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash me-2"></i>
                            حذف الرسالة
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    {{ t('approve_message') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('messages.approve_message', id=message.id) }}">
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم اعتماد هذه الرسالة وإشعار المرسل بالموافقة
                    </div>
                    <div class="mb-3">
                        <label for="approval_notes" class="form-label">{{ t('approval_notes') }} (اختياري)</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3"
                                  placeholder="أضف ملاحظات حول الاعتماد..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('cancel') }}</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>{{ t('approve') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times-circle text-danger me-2"></i>
                    {{ t('reject_message') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('messages.reject_message', id=message.id) }}">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        سيتم رفض هذه الرسالة وإشعار المرسل بالرفض
                    </div>
                    <div class="mb-3">
                        <label for="rejection_notes" class="form-label">سبب الرفض <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_notes" name="approval_notes" rows="3"
                                  placeholder="يرجى توضيح سبب رفض الرسالة..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('cancel') }}</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>{{ t('reject') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.signature-display {
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.signature-container {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background-color: #f8f9fa;
}

@media print {
    .btn, .card-header, nav, footer {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .col-lg-4 {
        display: none !important;
    }
    .col-lg-8 {
        width: 100% !important;
    }
    .signature-display {
        background: white !important;
        border: 2px solid #000 !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Display creator digital signature
    {% if false %}
    const creatorCanvas = document.getElementById('creatorSignatureCanvas');
    if (creatorCanvas) {
        const creatorCtx = creatorCanvas.getContext('2d');
        const creatorImg = new Image();
        creatorImg.onload = function() {
            creatorCtx.clearRect(0, 0, creatorCanvas.width, creatorCanvas.height);
            creatorCtx.drawImage(creatorImg, 0, 0, creatorCanvas.width, creatorCanvas.height);
        };
        creatorImg.src = '{{ message.creator.get_signature_data().get("data", "") if message.creator.get_signature_data() else "" }}';
    }
    {% endif %}

    // Display approver digital signature
    {% if false %}
    const approverCanvas = document.getElementById('approverSignatureCanvas');
    if (approverCanvas) {
        const approverCtx = approverCanvas.getContext('2d');
        const approverImg = new Image();
        approverImg.onload = function() {
            approverCtx.clearRect(0, 0, approverCanvas.width, approverCanvas.height);
            approverCtx.drawImage(approverImg, 0, 0, approverCanvas.width, approverCanvas.height);
        };
        approverImg.src = '{{ message.approver.get_signature_data().get("data", "") if message.approver.get_signature_data() else "" }}';
    }
    {% endif %}
});
</script>
{% endblock %}
