#!/usr/bin/env python3
"""
Simple startup script for Electronic Correspondence System
"""

import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🚀 تشغيل نظام المراسلات الإلكترونية...")
    print("📍 الخادم متاح على: http://localhost:8585")
    print("🔑 الدخول: admin / admin123")
    print("=" * 50)
    
    from app import create_app
    app = create_app()
    
    # Create uploads directory if it doesn't exist
    uploads_dir = os.path.join(os.path.dirname(__file__), 'uploads')
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)
        print("✓ Created uploads directory")
    
    # Start the server
    app.run(host='127.0.0.1', port=5000, debug=True)
    
except Exception as e:
    print(f"❌ Error starting server: {str(e)}")
    print("\n💡 Try running: python init_database.py first")
    input("Press Enter to exit...")
