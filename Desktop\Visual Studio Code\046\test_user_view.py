#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار صفحة تفاصيل المستخدم
"""

import requests
import sys

def test_user_view():
    """اختبار صفحة تفاصيل المستخدم"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار قائمة المستخدمين أولاً
        print("\n👥 اختبار قائمة المستخدمين...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            print("✅ قائمة المستخدمين تعمل")
            
            # البحث عن معرفات المستخدمين في المحتوى
            content = response.text
            if 'href="/users/' in content:
                print("✅ تم العثور على روابط المستخدمين")
            else:
                print("⚠️ لم يتم العثور على روابط المستخدمين")
        else:
            print(f"❌ فشل في قائمة المستخدمين: {response.status_code}")
            return False
        
        # اختبار تفاصيل المستخدم الحالي (admin)
        print("\n🔍 اختبار تفاصيل المستخدم الحالي...")
        response = session.get(f"{base_url}/users/1")
        if response.status_code == 200:
            print("✅ صفحة تفاصيل المستخدم تعمل")
            
            content = response.text
            if 'الإحصائيات' in content:
                print("✅ تم العثور على قسم الإحصائيات")
            else:
                print("⚠️ لم يتم العثور على قسم الإحصائيات")
                
            if 'رسائل واردة' in content:
                print("✅ تم العثور على إحصائيات الرسائل الواردة")
            else:
                print("⚠️ لم يتم العثور على إحصائيات الرسائل الواردة")
                
            if 'رسائل صادرة' in content:
                print("✅ تم العثور على إحصائيات الرسائل الصادرة")
            else:
                print("⚠️ لم يتم العثور على إحصائيات الرسائل الصادرة")
                
            if 'إجمالي الرسائل' in content:
                print("✅ تم العثور على إجمالي الرسائل")
            else:
                print("⚠️ لم يتم العثور على إجمالي الرسائل")
                
        elif response.status_code == 500:
            print("❌ خطأ 500 في صفحة تفاصيل المستخدم")
            print("هذا يشير إلى مشكلة في الكود")
            return False
        else:
            print(f"❌ فشل في تفاصيل المستخدم: {response.status_code}")
            return False
        
        # اختبار المستخدمين الآخرين
        print("\n👤 اختبار مستخدمين آخرين...")
        for user_id in [2, 3, 4, 5]:
            response = session.get(f"{base_url}/users/{user_id}")
            if response.status_code == 200:
                print(f"✅ المستخدم {user_id} يعمل")
            elif response.status_code == 404:
                print(f"⚠️ المستخدم {user_id} غير موجود")
            elif response.status_code == 500:
                print(f"❌ خطأ 500 في المستخدم {user_id}")
                return False
            else:
                print(f"❌ خطأ {response.status_code} في المستخدم {user_id}")
        
        print("\n🎉 تم اكتمال اختبار صفحة تفاصيل المستخدم!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار صفحة تفاصيل المستخدم")
    print("=" * 60)
    
    success = test_user_view()
    
    if success:
        print("\n✅ اختبار صفحة تفاصيل المستخدم نجح!")
        print("\n📋 النتائج:")
        print("  • صفحة تفاصيل المستخدم تعمل بدون أخطاء")
        print("  • إحصائيات الرسائل تظهر بشكل صحيح")
        print("  • العلاقات الديناميكية تعمل بشكل صحيح")
        sys.exit(0)
    else:
        print("\n❌ فشل في اختبار صفحة تفاصيل المستخدم!")
        print("\n🔧 تحقق من:")
        print("  • سجل أخطاء الخادم")
        print("  • العلاقات في النموذج")
        print("  • القالب templates/users/view.html")
        sys.exit(1)
