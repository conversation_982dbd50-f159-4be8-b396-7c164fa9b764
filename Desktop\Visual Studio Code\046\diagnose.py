#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import traceback

def diagnose_system():
    print("🔍 تشخيص النظام")
    print("=" * 40)
    
    # 1. فحص Python
    print(f"🐍 Python: {sys.version}")
    print(f"📍 المسار: {sys.executable}")
    
    # 2. فحص المجلد
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    
    # 3. فحص الملفات الأساسية
    essential_files = ['app.py', 'models.py']
    for file in essential_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} مفقود")
    
    # 4. فحص المكتبات
    libraries = ['flask', 'flask_sqlalchemy', 'flask_login']
    for lib in libraries:
        try:
            __import__(lib)
            print(f"✅ {lib}")
        except ImportError:
            print(f"❌ {lib} مفقود")
    
    # 5. اختبار التطبيق
    print("\n🧪 اختبار التطبيق...")
    try:
        from app import create_app
        print("✅ استيراد create_app نجح")
        
        app = create_app()
        print("✅ إنشاء التطبيق نجح")
        
        # اختبار بسيط
        with app.test_client() as client:
            response = client.get('/')
            print(f"✅ اختبار الصفحة الرئيسية: {response.status_code}")
        
        return app
        
    except Exception as e:
        print(f"❌ خطأ في التطبيق: {e}")
        traceback.print_exc()
        return None

def start_server(app):
    if app is None:
        print("❌ لا يمكن تشغيل الخادم - التطبيق غير جاهز")
        return
    
    print("\n🚀 تشغيل الخادم...")
    print("🌐 الرابط: http://127.0.0.1:8585")
    print("👤 المستخدم: admin")
    print("🔐 كلمة المرور: admin123")
    print("🛑 اضغط Ctrl+C للإيقاف")
    print("=" * 40)
    
    try:
        app.run(
            host='127.0.0.1',
            port=8585,
            debug=True,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ في الخادم: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    app = diagnose_system()
    
    if app:
        print("\n✅ التشخيص مكتمل - النظام جاهز")
        start_server(app)
    else:
        print("\n❌ فشل التشخيص - النظام غير جاهز")
        print("\n🔧 خطوات الإصلاح:")
        print("1. تأكد من وجود جميع الملفات")
        print("2. ثبت المكتبات: pip install flask flask-sqlalchemy flask-login")
        print("3. تحقق من قاعدة البيانات")
        
    input("\n⏸️ اضغط Enter للخروج...")
