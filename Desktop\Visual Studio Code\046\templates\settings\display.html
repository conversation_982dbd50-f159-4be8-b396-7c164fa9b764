{% extends "base.html" %}

{% block title %}إعدادات العرض{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-desktop me-2"></i>
                    إعدادات العرض
                </h2>
                <div>
                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للإعدادات
                    </a>
                </div>
            </div>

            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <!-- إعدادات التاريخ والوقت -->
                    <div class="col-lg-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar me-2"></i>إعدادات التاريخ والوقت
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="date_format" class="form-label">تنسيق التاريخ</label>
                                    <select class="form-select" id="date_format" name="date_format">
                                        <option value="dd/mm/yyyy" {% if display.date_format == 'dd/mm/yyyy' %}selected{% endif %}>يوم/شهر/سنة (31/12/2023)</option>
                                        <option value="mm/dd/yyyy" {% if display.date_format == 'mm/dd/yyyy' %}selected{% endif %}>شهر/يوم/سنة (12/31/2023)</option>
                                        <option value="yyyy-mm-dd" {% if display.date_format == 'yyyy-mm-dd' %}selected{% endif %}>سنة-شهر-يوم (2023-12-31)</option>
                                        <option value="dd-mm-yyyy" {% if display.date_format == 'dd-mm-yyyy' %}selected{% endif %}>يوم-شهر-سنة (31-12-2023)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="time_format" class="form-label">تنسيق الوقت</label>
                                    <select class="form-select" id="time_format" name="time_format">
                                        <option value="24h" {% if display.time_format == '24h' %}selected{% endif %}>24 ساعة (14:30)</option>
                                        <option value="12h" {% if display.time_format == '12h' %}selected{% endif %}>12 ساعة (2:30 PM)</option>
                                    </select>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="show_days_only" name="show_days_only" 
                                           {% if display.show_days_only == 'true' %}checked{% endif %}>
                                    <label class="form-check-label" for="show_days_only">
                                        <i class="fas fa-hashtag me-1"></i>عرض الأيام كأرقام فقط
                                    </label>
                                    <div class="form-text">
                                        عرض "3 أيام" بدلاً من "منذ 3 أيام" في الرسائل الحديثة
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الواجهة -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-window-maximize me-2"></i>إعدادات الواجهة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="items_per_page" class="form-label">عدد العناصر في الصفحة</label>
                                    <select class="form-select" id="items_per_page" name="items_per_page">
                                        <option value="10" {% if display.items_per_page == '10' %}selected{% endif %}>10 عناصر</option>
                                        <option value="20" {% if display.items_per_page == '20' %}selected{% endif %}>20 عنصر</option>
                                        <option value="50" {% if display.items_per_page == '50' %}selected{% endif %}>50 عنصر</option>
                                        <option value="100" {% if display.items_per_page == '100' %}selected{% endif %}>100 عنصر</option>
                                    </select>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="show_sidebar" name="show_sidebar" 
                                           {% if display.show_sidebar == 'true' %}checked{% endif %}>
                                    <label class="form-check-label" for="show_sidebar">
                                        <i class="fas fa-bars me-1"></i>إظهار الشريط الجانبي
                                    </label>
                                    <div class="form-text">
                                        إظهار أو إخفاء الشريط الجانبي بشكل افتراضي
                                    </div>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="compact_mode" name="compact_mode" 
                                           {% if display.compact_mode == 'true' %}checked{% endif %}>
                                    <label class="form-check-label" for="compact_mode">
                                        <i class="fas fa-compress me-1"></i>الوضع المضغوط
                                    </label>
                                    <div class="form-text">
                                        تقليل المسافات والحشو لعرض المزيد من المحتوى
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعاينة والأمثلة -->
                    <div class="col-lg-6">
                        <!-- معاينة التاريخ والوقت -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-eye me-2"></i>معاينة التاريخ والوقت
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>أمثلة على التاريخ:</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <div id="date-preview">
                                            <div class="mb-2">
                                                <strong>التنسيق المحدد:</strong> 
                                                <span id="current-date-format">31/12/2023</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>اليوم:</strong> 
                                                <span id="today-date"></span>
                                            </div>
                                            <div>
                                                <strong>أمس:</strong> 
                                                <span id="yesterday-date"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h6>أمثلة على الوقت:</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <div id="time-preview">
                                            <div class="mb-2">
                                                <strong>الوقت الحالي:</strong> 
                                                <span id="current-time"></span>
                                            </div>
                                            <div>
                                                <strong>منتصف الليل:</strong> 
                                                <span id="midnight-time"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h6>عرض الأيام:</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <div id="days-preview">
                                            <div class="mb-2">
                                                <strong>الطريقة الحالية:</strong> 
                                                <span id="days-display"></span>
                                            </div>
                                            <div class="text-muted small">
                                                مثال: رسالة تم إنشاؤها منذ 3 أيام
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معاينة الواجهة -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-layout me-2"></i>معاينة الواجهة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>عدد العناصر في الصفحة:</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <div id="pagination-preview">
                                            <div class="mb-2">
                                                <strong>العدد المحدد:</strong> 
                                                <span id="items-count">20 عنصر</span>
                                            </div>
                                            <div class="text-muted small">
                                                سيتم عرض هذا العدد من الرسائل/المستخدمين/الجهات في كل صفحة
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h6>الشريط الجانبي:</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <div id="sidebar-preview">
                                            <div class="mb-2">
                                                <strong>الحالة:</strong> 
                                                <span id="sidebar-status">مرئي</span>
                                            </div>
                                            <div class="text-muted small">
                                                يمكن للمستخدمين إظهار/إخفاء الشريط الجانبي يدوياً
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h6>الوضع المضغوط:</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <div id="compact-preview">
                                            <div class="mb-2">
                                                <strong>الحالة:</strong> 
                                                <span id="compact-status">غير مفعل</span>
                                            </div>
                                            <div class="text-muted small">
                                                يقلل المسافات بين العناصر لعرض المزيد من المحتوى
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- حفظ الإعدادات -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-1"></i>حفظ إعدادات العرض
                                    </button>
                                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث المعاينة عند تغيير الإعدادات
    const dateFormatSelect = document.getElementById('date_format');
    const timeFormatSelect = document.getElementById('time_format');
    const showDaysOnlyCheck = document.getElementById('show_days_only');
    const itemsPerPageSelect = document.getElementById('items_per_page');
    const showSidebarCheck = document.getElementById('show_sidebar');
    const compactModeCheck = document.getElementById('compact_mode');
    
    // إضافة مستمعي الأحداث
    dateFormatSelect.addEventListener('change', updateDatePreview);
    timeFormatSelect.addEventListener('change', updateTimePreview);
    showDaysOnlyCheck.addEventListener('change', updateDaysPreview);
    itemsPerPageSelect.addEventListener('change', updatePaginationPreview);
    showSidebarCheck.addEventListener('change', updateSidebarPreview);
    compactModeCheck.addEventListener('change', updateCompactPreview);
    
    // تحديث المعاينة الأولية
    updateAllPreviews();
    
    // تحديث الوقت كل ثانية
    setInterval(updateCurrentTime, 1000);
});

function updateAllPreviews() {
    updateDatePreview();
    updateTimePreview();
    updateDaysPreview();
    updatePaginationPreview();
    updateSidebarPreview();
    updateCompactPreview();
    updateCurrentTime();
}

function updateDatePreview() {
    const format = document.getElementById('date_format').value;
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const formatDate = (date, format) => {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        
        switch(format) {
            case 'dd/mm/yyyy': return `${day}/${month}/${year}`;
            case 'mm/dd/yyyy': return `${month}/${day}/${year}`;
            case 'yyyy-mm-dd': return `${year}-${month}-${day}`;
            case 'dd-mm-yyyy': return `${day}-${month}-${year}`;
            default: return `${day}/${month}/${year}`;
        }
    };
    
    document.getElementById('current-date-format').textContent = formatDate(today, format);
    document.getElementById('today-date').textContent = formatDate(today, format);
    document.getElementById('yesterday-date').textContent = formatDate(yesterday, format);
}

function updateTimePreview() {
    const format = document.getElementById('time_format').value;
    updateCurrentTime();
    
    if (format === '12h') {
        document.getElementById('midnight-time').textContent = '12:00 AM';
    } else {
        document.getElementById('midnight-time').textContent = '00:00';
    }
}

function updateCurrentTime() {
    const format = document.getElementById('time_format').value;
    const now = new Date();
    
    let timeString;
    if (format === '12h') {
        timeString = now.toLocaleTimeString('en-US', { 
            hour12: true, 
            hour: 'numeric', 
            minute: '2-digit' 
        });
    } else {
        timeString = now.toLocaleTimeString('en-GB', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }
    
    document.getElementById('current-time').textContent = timeString;
}

function updateDaysPreview() {
    const showDaysOnly = document.getElementById('show_days_only').checked;
    const display = showDaysOnly ? '3' : 'منذ 3 أيام';
    document.getElementById('days-display').textContent = display;
}

function updatePaginationPreview() {
    const itemsCount = document.getElementById('items_per_page').value;
    document.getElementById('items-count').textContent = `${itemsCount} عنصر`;
}

function updateSidebarPreview() {
    const showSidebar = document.getElementById('show_sidebar').checked;
    const status = showSidebar ? 'مرئي' : 'مخفي';
    document.getElementById('sidebar-status').textContent = status;
}

function updateCompactPreview() {
    const compactMode = document.getElementById('compact_mode').checked;
    const status = compactMode ? 'مفعل' : 'غير مفعل';
    document.getElementById('compact-status').textContent = status;
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.form-check-label {
    font-weight: 500;
}

.bg-light {
    background-color: #f8f9fa !important;
}

#date-preview, #time-preview, #days-preview,
#pagination-preview, #sidebar-preview, #compact-preview {
    font-family: 'Courier New', monospace;
}
</style>
{% endblock %}
