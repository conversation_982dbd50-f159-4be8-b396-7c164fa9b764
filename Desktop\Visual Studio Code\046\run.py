#!/usr/bin/env python3
"""
Main runner for Electronic Correspondence System
تشغيل نظام المراسلات الإلكترونية
"""

import os
import sys
from pathlib import Path

def print_header():
    print("=" * 60)
    print("🎯 نظام المراسلات الإلكترونية")
    print("📧 Electronic Correspondence System")
    print("=" * 60)

def check_environment():
    """Check if environment is ready"""
    print("🔍 فحص البيئة...")
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    print(f"✅ Python {sys.version.split()[0]}")
    
    # Check required modules
    required_modules = ['flask', 'flask_sqlalchemy', 'flask_login']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module} - مفقود")
    
    if missing_modules:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_modules)}")
        print("💡 لتثبيتها: pip install flask flask-sqlalchemy flask-login")
        return False
    
    return True

def create_directories():
    """Create necessary directories"""
    print("\n📁 إنشاء المجلدات...")
    
    directories = [
        'uploads',
        'uploads/signatures',
        'uploads/2024',
        'instance'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")

def run_basic_server():
    """Run basic Flask server"""
    print("\n🚀 تشغيل الخادم الأساسي...")
    
    from flask import Flask, render_template_string
    
    app = Flask(__name__)
    app.secret_key = 'dev-secret-key-2024'
    
    @app.route('/')
    def home():
        return render_template_string('''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>نظام المراسلات الإلكترونية</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }
                .main-container {
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    margin: 50px auto;
                    max-width: 800px;
                    overflow: hidden;
                }
                .header {
                    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }
                .content {
                    padding: 40px;
                }
                .feature-box {
                    background: #f8f9fa;
                    border-radius: 10px;
                    padding: 20px;
                    margin: 15px 0;
                    border-left: 4px solid #007bff;
                }
                .status-item {
                    display: flex;
                    align-items: center;
                    margin: 10px 0;
                }
                .status-icon {
                    width: 20px;
                    margin-left: 10px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="main-container">
                    <div class="header">
                        <h1><i class="fas fa-envelope me-3"></i>نظام المراسلات الإلكترونية</h1>
                        <p class="mb-0">Electronic Correspondence System</p>
                    </div>
                    
                    <div class="content">
                        <div class="alert alert-success text-center">
                            <h4><i class="fas fa-check-circle me-2"></i>النظام يعمل بنجاح!</h4>
                            <p class="mb-0">تم تشغيل الخادم بنجاح على المنفذ 5000</p>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="feature-box">
                                    <h5><i class="fas fa-cog text-primary me-2"></i>حالة النظام</h5>
                                    <div class="status-item">
                                        <i class="fas fa-check text-success status-icon"></i>
                                        <span>Flask Server: Running</span>
                                    </div>
                                    <div class="status-item">
                                        <i class="fas fa-check text-success status-icon"></i>
                                        <span>Python: Active</span>
                                    </div>
                                    <div class="status-item">
                                        <i class="fas fa-check text-success status-icon"></i>
                                        <span>Port 5000: Available</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="feature-box">
                                    <h5><i class="fas fa-info-circle text-info me-2"></i>معلومات الاتصال</h5>
                                    <div class="status-item">
                                        <i class="fas fa-globe text-primary status-icon"></i>
                                        <span>http://localhost:5000</span>
                                    </div>
                                    <div class="status-item">
                                        <i class="fas fa-user text-warning status-icon"></i>
                                        <span>Username: admin</span>
                                    </div>
                                    <div class="status-item">
                                        <i class="fas fa-key text-warning status-icon"></i>
                                        <span>Password: admin123</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="feature-box">
                            <h5><i class="fas fa-star text-warning me-2"></i>الميزات المتوفرة</h5>
                            <div class="row">
                                <div class="col-md-4 text-center">
                                    <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                                    <p><strong>إدارة الرسائل</strong></p>
                                </div>
                                <div class="col-md-4 text-center">
                                    <i class="fas fa-signature fa-2x text-success mb-2"></i>
                                    <p><strong>التوقيع الإلكتروني</strong></p>
                                </div>
                                <div class="col-md-4 text-center">
                                    <i class="fas fa-archive fa-2x text-info mb-2"></i>
                                    <p><strong>نظام الأرشيف</strong></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="/full" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-rocket me-2"></i>تشغيل النظام الكامل
                            </a>
                            <a href="/test" class="btn btn-outline-secondary">
                                <i class="fas fa-vial me-2"></i>اختبار API
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        ''')
    
    @app.route('/test')
    def test():
        return {
            'status': 'success',
            'message': 'النظام يعمل بنجاح',
            'server': 'Flask',
            'port': 5000,
            'features': [
                'إدارة الرسائل',
                'التوقيع الإلكتروني', 
                'نظام الأرشيف',
                'إدارة الصلاحيات'
            ]
        }
    
    @app.route('/login')
    def login_demo():
        return render_template_string('''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تسجيل الدخول - نظام المراسلات الإلكترونية</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }
                .login-container {
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                    overflow: hidden;
                    max-width: 400px;
                    width: 100%;
                }
                .login-header {
                    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }
                .login-body {
                    padding: 40px;
                }
                .form-control {
                    border-radius: 10px;
                    border: 2px solid #e9ecef;
                    padding: 12px 15px;
                    margin-bottom: 20px;
                }
                .form-control:focus {
                    border-color: #007bff;
                    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
                }
                .btn-login {
                    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                    border: none;
                    border-radius: 10px;
                    padding: 12px;
                    font-weight: bold;
                    width: 100%;
                    color: white;
                    transition: transform 0.2s;
                }
                .btn-login:hover {
                    transform: translateY(-2px);
                    color: white;
                }
                .demo-info {
                    background: #f8f9fa;
                    border-radius: 10px;
                    padding: 15px;
                    margin-top: 20px;
                    border-left: 4px solid #28a745;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="login-container">
                            <div class="login-header">
                                <h3><i class="fas fa-envelope me-2"></i>تسجيل الدخول</h3>
                                <p class="mb-0">نظام المراسلات الإلكترونية</p>
                            </div>
                            <div class="login-body">
                                <form>
                                    <div class="mb-3">
                                        <label class="form-label">اسم المستخدم</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" value="admin" readonly>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" class="form-control" value="admin123" readonly>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-login" onclick="showFullSystemMessage()">
                                        <i class="fas fa-sign-in-alt me-2"></i>دخول
                                    </button>
                                </form>

                                <div class="demo-info">
                                    <h6><i class="fas fa-info-circle text-success me-2"></i>معلومات تجريبية</h6>
                                    <p class="mb-2"><strong>اسم المستخدم:</strong> admin</p>
                                    <p class="mb-2"><strong>كلمة المرور:</strong> admin123</p>
                                    <small class="text-muted">هذه صفحة تجريبية. للنظام الكامل استخدم: python start_full.py</small>
                                </div>

                                <div class="text-center mt-3">
                                    <a href="/" class="text-decoration-none">
                                        <i class="fas fa-arrow-right me-1"></i>العودة للصفحة الرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                function showFullSystemMessage() {
                    alert('هذه صفحة تجريبية!\\n\\nللنظام الكامل مع تسجيل الدخول الحقيقي:\\n\\n1. أوقف الخادم الحالي (Ctrl+C)\\n2. شغل: python start_full.py\\n3. اذهب إلى: http://localhost:5000/auth/login');
                }
            </script>
        </body>
        </html>
        ''')

    @app.route('/full')
    def full_system():
        return render_template_string('''
        <div style="text-align: center; padding: 50px; font-family: Arial;">
            <h2>🚀 النظام الكامل</h2>
            <p>لتشغيل النظام الكامل مع صفحة الدخول، استخدم الأمر:</p>
            <code style="background: #f4f4f4; padding: 10px; border-radius: 5px;">python start_full.py</code>
            <br><br>
            <p>أو للتشغيل المباشر:</p>
            <code style="background: #f4f4f4; padding: 10px; border-radius: 5px;">python app.py</code>
            <br><br>
            <a href="/login" style="color: #007bff;">🔐 صفحة الدخول التجريبية</a>
            <br>
            <a href="/" style="color: #007bff;">← العودة للصفحة الرئيسية</a>
        </div>
        ''')
    
    print("✅ الخادم جاهز!")
    print("🌐 الرابط: http://localhost:5000")
    print("🧪 اختبار: http://localhost:5000/test")
    print("📧 النظام الكامل: http://localhost:5000/full")
    print("\n" + "=" * 60)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 60)
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=True, use_reloader=False)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")

def main():
    """Main function"""
    print_header()
    
    if not check_environment():
        print("\n💡 لحل المشاكل:")
        print("1. تثبيت Python 3.7+")
        print("2. تشغيل: pip install flask flask-sqlalchemy flask-login")
        input("\nاضغط Enter للخروج...")
        return
    
    create_directories()
    run_basic_server()

if __name__ == '__main__':
    main()
