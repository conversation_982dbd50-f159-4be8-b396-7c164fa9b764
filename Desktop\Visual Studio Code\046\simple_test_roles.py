#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لتعديل أدوار المستخدمين
"""

import requests
import sys

def test_simple():
    """اختبار بسيط"""
    base_url = "http://localhost:8585"
    
    try:
        print("🔗 اختبار الاتصال بالخادم...")
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل صحيح")
        else:
            print(f"❌ مشكلة في الخادم: {response.status_code}")
            return False
        
        print("\n🔐 اختبار صفحة تسجيل الدخول...")
        response = requests.get(f"{base_url}/auth/login", timeout=5)
        if response.status_code == 200:
            print("✅ صفحة تسجيل الدخول تعمل")
        else:
            print(f"❌ مشكلة في صفحة تسجيل الدخول: {response.status_code}")
            return False
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار بسيط للنظام")
    print("=" * 30)
    
    success = test_simple()
    
    if success:
        print("\n✅ الاختبار نجح!")
        print("\n📋 يمكنك الآن الوصول للنظام على:")
        print("🌐 http://localhost:8585")
        print("👤 اسم المستخدم: admin")
        print("🔐 كلمة المرور: admin123")
        print("\n📝 لتعديل أدوار المستخدمين:")
        print("1. سجل الدخول")
        print("2. اذهب إلى إدارة المستخدمين")
        print("3. اختر مستخدم للتعديل")
        print("4. ستجد خيارات الدور الأساسي والدور المخصص")
        print("5. يمكنك أيضاً الذهاب لإدارة الصلاحيات لإنشاء أدوار جديدة")
        sys.exit(0)
    else:
        print("\n❌ فشل الاختبار!")
        sys.exit(1)
