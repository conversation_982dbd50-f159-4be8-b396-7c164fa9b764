#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحديثات لوحة التحكم - الحالة والأيام
"""

import requests
import sys

def test_dashboard_updates():
    """اختبار تحديثات لوحة التحكم"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار الوصول للوحة التحكم
        print("\n📊 اختبار الوصول للوحة التحكم...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ تم الوصول للوحة التحكم بنجاح")
            
            # التحقق من وجود العناصر الجديدة
            content = response.text
            
            if 'الحالة' in content:
                print("✅ تم العثور على عمود الحالة")
            else:
                print("❌ لم يتم العثور على عمود الحالة")
                
            if 'الأيام' in content:
                print("✅ تم العثور على عمود الأيام")
            else:
                print("❌ لم يتم العثور على عمود الأيام")
                
            if 'get_status_display' in content or 'get_days_since' in content:
                print("✅ تم العثور على الدوال الجديدة في القالب")
            else:
                print("⚠️ لم يتم العثور على الدوال الجديدة (قد تكون مخفية)")
                
        else:
            print(f"❌ فشل الوصول للوحة التحكم: {response.status_code}")
            return False
        
        # اختبار إنشاء رسالة جديدة لاختبار الدوال
        print("\n📝 اختبار إنشاء رسالة جديدة...")
        message_data = {
            'message_type': 'incoming',
            'registration_number': 'TEST-2025-001',
            'destination': 'جهة اختبار',
            'subject': 'رسالة اختبار للحالة والأيام',
            'content': 'محتوى اختبار',
            'message_date': '2025-01-15',
            'priority': 'high',
            'status': 'active'
        }
        
        response = session.post(f"{base_url}/messages/new", data=message_data)
        if response.status_code == 200:
            print("✅ تم إنشاء رسالة اختبار بنجاح")
        else:
            print(f"⚠️ قد تكون هناك مشكلة في إنشاء الرسالة: {response.status_code}")
        
        # اختبار الوصول للوحة التحكم مرة أخرى لرؤية الرسالة الجديدة
        print("\n🔄 اختبار لوحة التحكم مع الرسالة الجديدة...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            if 'TEST-2025-001' in content:
                print("✅ تم العثور على الرسالة الجديدة في لوحة التحكم")
            else:
                print("⚠️ لم يتم العثور على الرسالة الجديدة (قد تحتاج لتحديث)")
        
        print("\n🎉 تم اكتمال جميع الاختبارات!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار تحديثات لوحة التحكم - الحالة والأيام")
    print("=" * 60)
    
    success = test_dashboard_updates()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        print("\n📋 التحديثات المطبقة:")
        print("• إضافة عمود الحالة (نشطة، مؤرشفة، محذوفة)")
        print("• إضافة عمود الأيام (منذ الإنشاء ومن التاريخ)")
        print("• عرض الأولوية (منخفضة، عادية، عالية، عاجلة)")
        print("• عرض حالة الاعتماد (معلقة، معتمدة، مرفوضة)")
        print("• تحسين التصميم والألوان")
        print("\n🌐 يمكنك الآن مشاهدة التحديثات على:")
        print("http://localhost:8585")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض الاختبارات!")
        sys.exit(1)
