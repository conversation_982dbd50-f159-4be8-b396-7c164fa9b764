<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة اختبار HTML</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #0066cc, #004499);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 نظام المراسلات الإلكترونية</h1>
        <p>صفحة اختبار HTML لعرض الملفات</p>
    </div>
    
    <div class="content">
        <h2>مرحباً بكم في نظام عرض الملفات</h2>
        
        <div class="highlight">
            <strong>ملاحظة:</strong> هذه صفحة HTML تجريبية لاختبار عرض ملفات HTML في النظام.
        </div>
        
        <h3>الميزات المتاحة:</h3>
        <div class="feature-list">
            <div class="feature-item">
                <h4>📄 عرض المستندات</h4>
                <p>PDF, DOCX, TXT</p>
            </div>
            <div class="feature-item">
                <h4>🖼️ عرض الصور</h4>
                <p>JPG, PNG, GIF, SVG</p>
            </div>
            <div class="feature-item">
                <h4>🎵 تشغيل الصوت</h4>
                <p>MP3, WAV, OGG</p>
            </div>
            <div class="feature-item">
                <h4>🎬 تشغيل الفيديو</h4>
                <p>MP4, AVI, MOV</p>
            </div>
            <div class="feature-item">
                <h4>📊 عرض البيانات</h4>
                <p>JSON, XML, CSV</p>
            </div>
            <div class="feature-item">
                <h4>💻 عرض الكود</h4>
                <p>HTML, CSS, JS</p>
            </div>
        </div>
        
        <h3>معلومات تقنية:</h3>
        <ul>
            <li><strong>التاريخ:</strong> 2025-06-06</li>
            <li><strong>الإصدار:</strong> 1.0.0</li>
            <li><strong>اللغة:</strong> العربية</li>
            <li><strong>الترميز:</strong> UTF-8</li>
        </ul>
        
        <div class="highlight">
            <p>تم إنشاء هذا الملف لاختبار عرض ملفات HTML في نظام المراسلات الإلكترونية.</p>
        </div>
    </div>
    
    <script>
        console.log('صفحة HTML تم تحميلها بنجاح');
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM جاهز');
        });
    </script>
</body>
</html>