{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة التحكم
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-2">
    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-primary text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ stats.total_messages }}</h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">إجمالي الرسائل</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-envelope"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('messages.list_messages') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        عرض <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-success text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ stats.incoming_messages }}</h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">الرسائل الواردة</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-inbox"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('messages.list_messages', type='incoming') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        عرض <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-info text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ stats.outgoing_messages }}</h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">الرسائل الصادرة</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('messages.list_messages', type='outgoing') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        عرض <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-warning text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ stats.internal_messages }}</h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">المراسلات الداخلية</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('messages.internal_messages') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        عرض <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-primary text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold"><i class="fas fa-plus"></i></h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">رسالة جديدة</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-edit"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('messages.new_message') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        إنشاء <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Internal Messages Statistics -->
<div class="row mb-2">
    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-danger text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ stats.urgent_internal }}</h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">مراسلات عاجلة</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('messages.internal_messages', status='urgent') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        عرض <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-secondary text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ stats.pending_internal }}</h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">بانتظار الرد</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('messages.internal_messages', status='pending') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        عرض <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Departments Section (for managers only) -->
{% if current_user.can_manage_users() %}
<div class="row mb-2">
    <div class="col-12">
        <h6 class="mb-2 text-muted">
            <i class="fas fa-building me-2"></i>
            إدارة الأقسام
        </h6>
    </div>
    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-info text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ stats.total_departments }}</h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">إجمالي الأقسام</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('departments.list_departments') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        عرض <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-success text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ stats.active_departments }}</h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">الأقسام النشطة</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('departments.list_departments', status='active') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        عرض <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-2">
        <div class="card bg-warning text-white h-100 card-compact">
            <div class="card-body p-2">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold"><i class="fas fa-plus"></i></h6>
                        <small class="opacity-75" style="font-size: 0.7rem;">قسم جديد</small>
                    </div>
                    <div class="ms-2">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                </div>
                <div class="mt-1">
                    <a href="{{ url_for('departments.new_department') }}" class="text-white text-decoration-none" style="font-size: 0.65rem;">
                        إنشاء <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Messages -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الرسائل الحديثة
                </h5>
                <div>
                    <button type="button" class="btn btn-danger btn-sm me-2" id="deleteSelectedBtn" style="display: none;">
                        <i class="fas fa-trash me-1"></i>
                        حذف المحدد
                    </button>
                    <a href="{{ url_for('messages.list_messages') }}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if stats.recent_messages %}
                <div class="table-responsive">
                    <form id="bulkDeleteForm" method="POST" action="{{ url_for('messages.bulk_delete') }}">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>رقم التسجيل</th>
                                    <th>النوع</th>
                                    <th>الموضوع</th>
                                    <th>الجهة</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>الأيام</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        <tbody>
                            {% for message in stats.recent_messages %}
                            <tr>
                                <td>
                                    <input type="checkbox" name="message_ids" value="{{ message.id }}" class="form-check-input message-checkbox">
                                </td>
                                <td>
                                    <strong>{{ message.registration_number }}</strong>
                                </td>
                                <td>
                                    {% if message.is_incoming() %}
                                        <span class="badge bg-success">واردة</span>
                                    {% elif message.is_outgoing() %}
                                        <span class="badge bg-info">صادرة</span>
                                    {% else %}
                                        <span class="badge bg-warning">داخلية</span>
                                    {% endif %}
                                </td>
                                <td>{{ message.subject[:50] }}{% if message.subject|length > 50 %}...{% endif %}</td>
                                <td>{{ message.destination[:30] }}{% if message.destination|length > 30 %}...{% endif %}</td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="badge {{ message.get_status_badge_class() }}">
                                        {{ message.get_status_display() }}
                                    </span>
                                    {% if message.priority != 'normal' %}
                                    <br>
                                    <small>
                                        <span class="badge {{ message.get_priority_badge_class() }}">
                                            {{ message.get_priority_display() }}
                                        </span>
                                    </small>
                                    {% endif %}
                                    {% if message.requires_approval %}
                                    <br>
                                    <small>
                                        <span class="badge {{ message.get_approval_badge_class() }}">
                                            {{ message.get_approval_status_display() }}
                                        </span>
                                    </small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% set show_days_only = get_setting('show_days_only', 'true') == 'true' %}
                                    {% if show_days_only %}
                                    <span class="text-muted fw-bold fs-5">
                                        {{ message.get_days_since_creation() }}
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        يوم
                                    </small>
                                    {% if message.get_days_since_message_date() != message.get_days_since_creation() %}
                                    <br>
                                    <small class="text-info">
                                        {{ message.get_days_since_message_date() }} من التاريخ
                                    </small>
                                    {% endif %}
                                    {% else %}
                                    <span class="text-muted">
                                        منذ {{ message.get_days_since_creation() }} يوم
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        من الإنشاء
                                    </small>
                                    {% if message.get_days_since_message_date() != message.get_days_since_creation() %}
                                    <br>
                                    <small class="text-info">
                                        منذ {{ message.get_days_since_message_date() }} يوم من التاريخ
                                    </small>
                                    {% endif %}
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    </form>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>لا توجد رسائل حتى الآن</p>
                    <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary">
                        إنشاء أول رسالة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus me-2"></i>
                            رسالة جديدة
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('search') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-search me-2"></i>
                            البحث في الرسائل
                        </a>
                    </div>
                    {% if current_user.can_manage_users() %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('users.new_user') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            مستخدم جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card-compact {
    transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    border: none;
    border-radius: 8px;
    min-height: 70px;
}

.card-compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.card-compact .card-body {
    border-radius: 8px;
    padding: 0.5rem !important;
}

.card-compact h6 {
    font-weight: 700;
    font-size: 1.1rem;
    line-height: 1.2;
}

.card-compact small {
    font-size: 0.65rem;
    opacity: 0.85;
    line-height: 1.1;
}

.card-compact a {
    font-size: 0.6rem;
    font-weight: 500;
    opacity: 0.9;
}

.card-compact a:hover {
    text-decoration: underline !important;
    opacity: 1;
}

.card-compact i {
    font-size: 0.9rem;
}

/* Ultra compact for small screens */
@media (max-width: 768px) {
    .card-compact {
        min-height: 60px;
    }

    .card-compact h6 {
        font-size: 1rem;
    }

    .card-compact small {
        font-size: 0.6rem;
    }

    .card-compact a {
        font-size: 0.55rem;
    }

    .card-compact i {
        font-size: 0.8rem;
    }
}

/* Extra compact for mobile */
@media (max-width: 576px) {
    .card-compact {
        min-height: 55px;
    }

    .card-compact .card-body {
        padding: 0.4rem !important;
    }

    .card-compact h6 {
        font-size: 0.9rem;
    }

    .card-compact small {
        font-size: 0.55rem;
    }

    .card-compact a {
        font-size: 0.5rem;
    }

    .card-compact i {
        font-size: 0.7rem;
    }

    .row.mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .col-6.mb-2 {
        margin-bottom: 0.5rem !important;
    }
}

/* Grid adjustments */
@media (min-width: 1200px) {
    .col-lg-2 {
        flex: 0 0 auto;
        width: 16.66666667%;
    }
}

/* Text adjustments */
.opacity-75 {
    opacity: 0.75 !important;
}

.fw-bold {
    font-weight: 700 !important;
}

/* Table improvements */
.table td {
    vertical-align: middle;
}

.table .badge {
    font-size: 0.75em;
}

.table small .badge {
    font-size: 0.65em;
}

/* Status column styling */
.table td:nth-child(7) {
    min-width: 120px;
}

/* Days column styling */
.table td:nth-child(8) {
    min-width: 100px;
    text-align: center;
}

/* Responsive table adjustments */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.85rem;
    }

    .table .badge {
        font-size: 0.7em;
    }

    .table small .badge {
        font-size: 0.6em;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
    const bulkDeleteForm = document.getElementById('bulkDeleteForm');

    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            messageCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            toggleDeleteButton();
        });
    }

    // Handle individual checkboxes
    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.message-checkbox:checked').length;
            const totalCount = messageCheckboxes.length;

            // Update select all checkbox state
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === totalCount;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            }

            toggleDeleteButton();
        });
    });

    // Toggle delete button visibility
    function toggleDeleteButton() {
        const checkedCount = document.querySelectorAll('.message-checkbox:checked').length;
        if (deleteSelectedBtn) {
            deleteSelectedBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
        }
    }

    // Handle bulk delete
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', function() {
            const checkedCount = document.querySelectorAll('.message-checkbox:checked').length;
            if (checkedCount === 0) {
                alert('يرجى اختيار رسالة واحدة على الأقل للحذف');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف ${checkedCount} رسالة؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
                bulkDeleteForm.submit();
            }
        });
    }
});
</script>
{% endblock %}
