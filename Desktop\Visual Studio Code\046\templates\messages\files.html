{% extends "base.html" %}

{% block title %}إدارة الملفات - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-folder-open me-2"></i>
                إدارة الملفات المرفقة
            </h1>
            <div>
                <a href="{{ url_for('messages.list_messages') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للرسائل
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ files_info|length }}</h4>
                        <p class="card-text">إجمالي الملفات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ files_info|selectattr('file_exists')|list|length }}</h4>
                        <p class="card-text">ملفات متوفرة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ files_info|rejectattr('file_exists')|list|length }}</h4>
                        <p class="card-text">ملفات مفقودة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        {% set total_size = files_info|sum(attribute='file_size') %}
                        <h4 class="card-title">
                            {% if total_size < 1024 %}
                                {{ total_size }} B
                            {% elif total_size < 1048576 %}
                                {{ "%.1f"|format(total_size/1024) }} KB
                            {% elif total_size < 1073741824 %}
                                {{ "%.1f"|format(total_size/1048576) }} MB
                            {% else %}
                                {{ "%.1f"|format(total_size/1073741824) }} GB
                            {% endif %}
                        </h4>
                        <p class="card-text">إجمالي الحجم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Files Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الملفات
                </h5>
            </div>
            <div class="card-body">
                {% if files_info %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الملف</th>
                                <th>الرسالة</th>
                                <th>النوع</th>
                                <th>الحجم</th>
                                <th>تاريخ الرفع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file_info in files_info %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas {{ get_file_icon(file_info.file_name) }} me-2 text-primary"></i>
                                        <span>{{ file_info.file_name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=file_info.message.id) }}" 
                                       class="text-decoration-none">
                                        {{ file_info.message.registration_number }}
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ file_info.message.subject[:50] }}{% if file_info.message.subject|length > 50 %}...{% endif %}</small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ file_info.extension.upper() }}</span>
                                </td>
                                <td>
                                    {% if file_info.file_size < 1024 %}
                                        {{ file_info.file_size }} B
                                    {% elif file_info.file_size < 1048576 %}
                                        {{ "%.1f"|format(file_info.file_size/1024) }} KB
                                    {% elif file_info.file_size < 1073741824 %}
                                        {{ "%.1f"|format(file_info.file_size/1048576) }} MB
                                    {% else %}
                                        {{ "%.1f"|format(file_info.file_size/1073741824) }} GB
                                    {% endif %}
                                </td>
                                <td>
                                    {{ file_info.message.date_created.strftime('%Y-%m-%d %H:%M') }}
                                </td>
                                <td>
                                    {% if file_info.file_exists %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            متوفر
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>
                                            مفقود
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if file_info.file_exists %}
                                    <div class="btn-group btn-group-sm">
                                        <!-- عرض الملف -->
                                        <a href="{{ url_for('messages.view_attachment', filename=file_info.file_path) }}"
                                           class="btn btn-primary" title="عرض الملف">
                                            <i class="fas fa-search-plus"></i>
                                        </a>

                                        <!-- عرض في المتصفح -->
                                        {% if file_info.extension in ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'txt', 'html', 'xml', 'json', 'css', 'js', 'mp3', 'wav', 'mp4', 'webm'] %}
                                        <a href="{{ url_for('messages.view_attachment', filename=file_info.file_path) }}"
                                           class="btn btn-outline-primary" target="_blank" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% endif %}

                                        <!-- تحميل -->
                                        <a href="{{ url_for('messages.download_attachment', filename=file_info.file_path) }}"
                                           class="btn btn-outline-success" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                    {% else %}
                                        <span class="text-muted">غير متوفر</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد ملفات مرفقة</h5>
                    <p class="text-muted">لم يتم رفع أي ملفات مع الرسائل بعد</p>
                    <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء رسالة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}
