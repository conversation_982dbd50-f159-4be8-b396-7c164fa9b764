#!/usr/bin/env python3
"""
Database initialization script for the Electronic Correspondence System
"""

import os
import sys
from datetime import datetime, date

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Message, Department, UserRole, MessageType, InternalMessageResponse

def init_database():
    """Initialize the database with tables and sample data"""
    app = create_app()
    
    with app.app_context():
        print("Creating database tables...")
        db.create_all()
        
        # Create default admin user if no users exist
        if User.query.count() == 0:
            print("Creating default admin user...")
            admin = User(
                name='مدير النظام',
                username='admin',
                role=UserRole.ADMIN
            )
            admin.set_password('admin123')
            db.session.add(admin)
            
            # Create additional sample users
            secretary = User(
                name='سكرتير المكتب',
                username='secretary',
                role=UserRole.SECRETARY
            )
            secretary.set_password('secretary123')
            db.session.add(secretary)
            
            employee = User(
                name='موظف عام',
                username='employee',
                role=UserRole.EMPLOYEE
            )
            employee.set_password('employee123')
            db.session.add(employee)
            
            db.session.commit()
            print("Sample users created:")
            print("  Admin: username='admin', password='admin123'")
            print("  Secretary: username='secretary', password='secretary123'")
            print("  Employee: username='employee', password='employee123'")
        
        # Create sample departments
        if Department.query.count() == 0:
            print("Creating sample departments...")
            departments = [
                Department(name='الإدارة العامة', description='الإدارة العامة للمؤسسة'),
                Department(name='الموارد البشرية', description='قسم الموارد البشرية'),
                Department(name='المالية والمحاسبة', description='قسم المالية والمحاسبة'),
                Department(name='تقنية المعلومات', description='قسم تقنية المعلومات'),
                Department(name='الشؤون القانونية', description='قسم الشؤون القانونية'),
            ]
            
            for dept in departments:
                db.session.add(dept)
            
            db.session.commit()
            print(f"Created {len(departments)} sample departments")

            # Assign users to departments and set department managers
            admin_user = User.query.filter_by(username='admin').first()
            secretary_user = User.query.filter_by(username='secretary').first()
            employee_user = User.query.filter_by(username='employee').first()

            # Get departments
            admin_dept = Department.query.filter_by(name='الإدارة العامة').first()
            hr_dept = Department.query.filter_by(name='الموارد البشرية').first()
            it_dept = Department.query.filter_by(name='تقنية المعلومات').first()

            # Assign users to departments
            if admin_user and admin_dept:
                admin_user.department_id = admin_dept.id
                admin_dept.manager_id = admin_user.id  # Make admin the manager of general administration

            if secretary_user and admin_dept:
                secretary_user.department_id = admin_dept.id

            if employee_user and hr_dept:
                employee_user.department_id = hr_dept.id

            # Create additional users for other departments
            it_manager = User(
                name='مدير تقنية المعلومات',
                username='it_manager',
                role=UserRole.SECRETARY,
                department_id=it_dept.id if it_dept else None
            )
            it_manager.set_password('it123')
            db.session.add(it_manager)

            if it_dept:
                it_dept.manager_id = it_manager.id

            hr_employee = User(
                name='موظف الموارد البشرية',
                username='hr_employee',
                role=UserRole.EMPLOYEE,
                department_id=hr_dept.id if hr_dept else None
            )
            hr_employee.set_password('hr123')
            db.session.add(hr_employee)

            db.session.commit()
            print("Users assigned to departments and department managers set")
        
        # Create sample messages
        if Message.query.count() == 0:
            print("Creating sample messages...")
            admin_user = User.query.filter_by(username='admin').first()
            
            sample_messages = [
                Message(
                    message_type=MessageType.INCOMING,
                    registration_number='IN-20240101-001',
                    destination='وزارة التعليم',
                    subject='طلب تحديث البيانات الأكاديمية',
                    content='نرجو منكم تحديث البيانات الأكاديمية للطلاب المستجدين للعام الدراسي الجديد.',
                    message_date=date(2024, 1, 15),
                    department='الإدارة العامة',
                    priority='high',
                    created_by=admin_user.id
                ),
                Message(
                    message_type=MessageType.OUTGOING,
                    registration_number='OUT-20240102-001',
                    destination='مجلس الوزراء',
                    subject='تقرير الأداء السنوي',
                    content='نتشرف بإرسال تقرير الأداء السنوي للمؤسسة للعام المنصرم.',
                    message_date=date(2024, 1, 20),
                    department='الإدارة العامة',
                    priority='urgent',
                    created_by=admin_user.id
                ),
                Message(
                    message_type=MessageType.INCOMING,
                    registration_number='IN-20240103-001',
                    destination='وزارة المالية',
                    subject='الموافقة على الميزانية المقترحة',
                    content='تم الموافقة على الميزانية المقترحة للعام المالي القادم.',
                    message_date=date(2024, 1, 25),
                    department='المالية والمحاسبة',
                    priority='normal',
                    created_by=admin_user.id
                ),
                Message(
                    message_type=MessageType.OUTGOING,
                    registration_number='OUT-20240104-001',
                    destination='الهيئة العامة للإحصاء',
                    subject='البيانات الإحصائية الربعية',
                    content='نرسل إليكم البيانات الإحصائية للربع الأول من العام الحالي.',
                    message_date=date(2024, 2, 1),
                    department='الإدارة العامة',
                    priority='normal',
                    created_by=admin_user.id
                ),
                Message(
                    message_type=MessageType.INCOMING,
                    registration_number='IN-20240105-001',
                    destination='وزارة الصحة',
                    subject='تطبيق البروتوكولات الصحية',
                    content='يرجى تطبيق البروتوكولات الصحية الجديدة في جميع المرافق.',
                    message_date=date(2024, 2, 5),
                    department='الإدارة العامة',
                    priority='high',
                    created_by=admin_user.id
                ),
                # Internal Messages
                Message(
                    message_type=MessageType.INTERNAL,
                    registration_number='INT-20240201-001',
                    destination='قسم تقنية المعلومات',
                    subject='طلب تحديث أنظمة الحاسوب',
                    content='نرجو تحديث أنظمة الحاسوب في جميع الأقسام وتثبيت التحديثات الأمنية اللازمة.',
                    message_date=date(2024, 2, 1),
                    department='تقنية المعلومات',
                    priority='high',
                    from_department='الإدارة العامة',
                    to_department='تقنية المعلومات',
                    assigned_to=admin_user.id,
                    due_date=date(2024, 2, 15),
                    is_urgent=True,
                    requires_response=True,
                    created_by=admin_user.id
                ),
                Message(
                    message_type=MessageType.INTERNAL,
                    registration_number='INT-20240202-001',
                    destination='قسم الموارد البشرية',
                    subject='تقرير الحضور والانصراف الشهري',
                    content='يرجى إعداد تقرير الحضور والانصراف لجميع الموظفين للشهر الماضي.',
                    message_date=date(2024, 2, 2),
                    department='الموارد البشرية',
                    priority='normal',
                    from_department='الإدارة العامة',
                    to_department='الموارد البشرية',
                    due_date=date(2024, 2, 10),
                    requires_response=True,
                    created_by=admin_user.id
                )
            ]
            
            for message in sample_messages:
                db.session.add(message)
            
            db.session.commit()
            print(f"Created {len(sample_messages)} sample messages")
        
        # Create uploads directory
        uploads_dir = 'uploads'
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
            print(f"Created uploads directory: {uploads_dir}")
        
        # Create static directories
        static_dirs = ['static/css', 'static/js', 'static/images']
        for directory in static_dirs:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"Created directory: {directory}")
        
        print("\nDatabase initialization completed successfully!")
        print("\nYou can now run the application with: python app.py")
        print("\nDefault login credentials:")
        print("  Admin: admin / admin123")
        print("  Secretary: secretary / secretary123")
        print("  Employee: employee / employee123")

if __name__ == '__main__':
    init_database()
