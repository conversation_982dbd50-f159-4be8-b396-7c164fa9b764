#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات لإضافة الحقول الجديدة
"""

import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, UserRole, Permission, Role, Department

def update_database():
    """تحديث قاعدة البيانات"""
    
    app = create_app()
    with app.app_context():
        print("🔄 تحديث قاعدة البيانات...")
        print("=" * 50)
        
        try:
            # إنشاء الجداول الجديدة أو تحديث الموجودة
            db.create_all()
            print("✅ تم تحديث جداول قاعدة البيانات")
            
            # التحقق من وجود الحقول الجديدة في جدول المستخدمين
            print("\n🔍 التحقق من الحقول الجديدة...")
            
            # محاولة الوصول للحقول الجديدة
            admin = User.query.filter_by(username='admin').first()
            if admin:
                # التحقق من وجود الحقول الجديدة
                try:
                    approval_manager_id = admin.approval_manager_id
                    can_approve_others = admin.can_approve_others
                    print("✅ الحقول الجديدة موجودة في قاعدة البيانات")
                except AttributeError as e:
                    print(f"❌ الحقول الجديدة غير موجودة: {e}")
                    print("💡 يرجى حذف قاعدة البيانات وإعادة إنشائها")
                    return
                
                # تحديث المدير ليكون قادراً على اعتماد رسائل الآخرين
                if not admin.can_approve_others:
                    admin.can_approve_others = True
                    db.session.commit()
                    print("✅ تم تحديث صلاحيات المدير")
            
            # إنشاء مستخدمين تجريبيين إضافيين إذا لم يكونوا موجودين
            print("\n👥 إنشاء مستخدمين تجريبيين...")
            
            # البحث عن الأقسام
            departments = Department.query.all()
            if not departments:
                print("❌ لا توجد أقسام في النظام")
                return
            
            default_dept = departments[0]
            
            # إنشاء مدير قسم
            manager = User.query.filter_by(username='manager').first()
            if not manager:
                manager = User(
                    name='مدير القسم',
                    username='manager',
                    role=UserRole.MANAGER,
                    department_id=default_dept.id,
                    can_approve_others=True
                )
                manager.set_password('manager123')
                db.session.add(manager)
                print("✅ تم إنشاء مدير القسم")
            
            # إنشاء سكرتير
            secretary = User.query.filter_by(username='secretary').first()
            if not secretary:
                secretary = User(
                    name='سكرتير المكتب',
                    username='secretary',
                    role=UserRole.SECRETARY,
                    department_id=default_dept.id,
                    approval_manager_id=manager.id if manager else None
                )
                secretary.set_password('secretary123')
                db.session.add(secretary)
                print("✅ تم إنشاء السكرتير")
            
            # إنشاء موظف
            employee = User.query.filter_by(username='employee').first()
            if not employee:
                employee = User(
                    name='موظف عام',
                    username='employee',
                    role=UserRole.EMPLOYEE,
                    department_id=default_dept.id,
                    approval_manager_id=manager.id if manager else None
                )
                employee.set_password('employee123')
                db.session.add(employee)
                print("✅ تم إنشاء الموظف")
            
            db.session.commit()
            
            print("\n📊 إحصائيات النظام:")
            total_users = User.query.count()
            total_departments = Department.query.count()
            total_permissions = Permission.query.count()
            total_roles = Role.query.count()
            
            print(f"   👥 إجمالي المستخدمين: {total_users}")
            print(f"   🏢 إجمالي الأقسام: {total_departments}")
            print(f"   🔐 إجمالي الصلاحيات: {total_permissions}")
            print(f"   👑 إجمالي الأدوار: {total_roles}")
            
            print("\n🔑 بيانات الدخول:")
            users = User.query.all()
            for user in users:
                role_name = {
                    UserRole.ADMIN: 'مدير النظام',
                    UserRole.MANAGER: 'مدير',
                    UserRole.SECRETARY: 'سكرتير',
                    UserRole.EMPLOYEE: 'موظف'
                }.get(user.role, 'غير محدد')
                
                print(f"   👤 {user.name} ({role_name})")
                print(f"      🔐 اسم المستخدم: {user.username}")
                print(f"      🏢 القسم: {user.department.name if user.department else 'غير محدد'}")
                print(f"      ✅ يمكنه الاعتماد: {'نعم' if user.can_approve_others else 'لا'}")
                if user.approval_manager:
                    print(f"      👨‍💼 مسؤول الاعتماد: {user.approval_manager.name}")
                print()
            
            print("✅ تم تحديث قاعدة البيانات بنجاح!")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
            db.session.rollback()

if __name__ == '__main__':
    update_database()
