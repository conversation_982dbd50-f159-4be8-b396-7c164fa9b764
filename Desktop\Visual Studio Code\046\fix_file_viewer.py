#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح نظام عرض الملفات
"""

import os
import sys
import subprocess

def install_required_packages():
    """تثبيت الحزم المطلوبة لعرض الملفات"""
    print("📦 تثبيت الحزم المطلوبة...")
    
    packages = [
        'Pillow>=10.0.0',
        'python-docx>=0.8.11',
        'PyPDF2>=3.0.1'
    ]
    
    for package in packages:
        try:
            print(f"  📥 تثبيت {package}...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ❌ فشل في تثبيت {package}")
            return False
    
    return True

def create_utils_directory():
    """إنشاء مجلد utils إذا لم يكن موجوداً"""
    print("📁 إنشاء مجلد utils...")
    
    utils_dir = "utils"
    if not os.path.exists(utils_dir):
        os.makedirs(utils_dir)
        print(f"  ✅ تم إنشاء مجلد {utils_dir}")
    else:
        print(f"  ✅ مجلد {utils_dir} موجود")
    
    # إنشاء ملف __init__.py
    init_file = os.path.join(utils_dir, "__init__.py")
    if not os.path.exists(init_file):
        with open(init_file, 'w') as f:
            f.write("# Utils package\n")
        print(f"  ✅ تم إنشاء {init_file}")
    
    return True

def fix_template_errors():
    """إصلاح أخطاء القوالب"""
    print("🔧 إصلاح أخطاء القوالب...")
    
    # إصلاح قالب عرض الرسائل - إزالة مسار preview_attachment المفقود
    view_template_path = "templates/messages/view.html"
    if os.path.exists(view_template_path):
        try:
            with open(view_template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إزالة أو تعليق زر المعاينة المتقدمة مؤقتاً
            if 'preview_attachment' in content:
                content = content.replace(
                    'url_for(\'messages.preview_attachment\'',
                    'url_for(\'messages.view_attachment\''
                )
                content = content.replace(
                    'معاينة متقدمة',
                    'عرض الملف'
                )
                
                with open(view_template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  ✅ تم إصلاح {view_template_path}")
            else:
                print(f"  ✅ {view_template_path} لا يحتاج إصلاح")
                
        except Exception as e:
            print(f"  ❌ خطأ في إصلاح {view_template_path}: {str(e)}")
            return False
    
    # إصلاح قالب قائمة الملفات
    files_template_path = "templates/messages/files.html"
    if os.path.exists(files_template_path):
        try:
            with open(files_template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'preview_attachment' in content:
                content = content.replace(
                    'url_for(\'messages.preview_attachment\'',
                    'url_for(\'messages.view_attachment\''
                )
                content = content.replace(
                    'معاينة متقدمة',
                    'عرض الملف'
                )
                
                with open(files_template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  ✅ تم إصلاح {files_template_path}")
            else:
                print(f"  ✅ {files_template_path} لا يحتاج إصلاح")
                
        except Exception as e:
            print(f"  ❌ خطأ في إصلاح {files_template_path}: {str(e)}")
            return False
    
    return True

def create_simple_file_viewer():
    """إنشاء عارض ملفات مبسط"""
    print("📄 إنشاء عارض ملفات مبسط...")
    
    simple_viewer_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
عارض ملفات مبسط
"""

import os
import base64
import mimetypes

def get_file_info(file_path, filename):
    """الحصول على معلومات الملف"""
    if not os.path.exists(file_path):
        return None
    
    file_size = os.path.getsize(file_path)
    extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    return {
        'name': filename,
        'size': file_size,
        'extension': extension,
        'mime_type': mimetypes.guess_type(filename)[0] or 'application/octet-stream'
    }

def can_view_in_browser(filename):
    """التحقق من إمكانية عرض الملف في المتصفح"""
    if not filename or '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    viewable_extensions = [
        'pdf', 'txt', 'html', 'xml', 'json', 'css', 'js',
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp',
        'mp3', 'wav', 'mp4', 'webm'
    ]
    
    return extension in viewable_extensions

def get_file_icon_class(filename):
    """الحصول على فئة أيقونة الملف"""
    if not filename or '.' not in filename:
        return 'fa-file'
    
    extension = filename.rsplit('.', 1)[1].lower()
    
    icon_map = {
        'pdf': 'fa-file-pdf',
        'doc': 'fa-file-word', 'docx': 'fa-file-word',
        'xls': 'fa-file-excel', 'xlsx': 'fa-file-excel',
        'ppt': 'fa-file-powerpoint', 'pptx': 'fa-file-powerpoint',
        'txt': 'fa-file-alt', 'rtf': 'fa-file-alt',
        'jpg': 'fa-file-image', 'jpeg': 'fa-file-image', 'png': 'fa-file-image',
        'gif': 'fa-file-image', 'bmp': 'fa-file-image', 'svg': 'fa-file-image',
        'mp3': 'fa-file-audio', 'wav': 'fa-file-audio',
        'mp4': 'fa-file-video', 'avi': 'fa-file-video',
        'zip': 'fa-file-archive', 'rar': 'fa-file-archive',
        'html': 'fa-file-code', 'css': 'fa-file-code', 'js': 'fa-file-code'
    }
    
    return icon_map.get(extension, 'fa-file')

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"
'''
    
    utils_dir = "utils"
    simple_viewer_path = os.path.join(utils_dir, "simple_file_viewer.py")
    
    try:
        with open(simple_viewer_path, 'w', encoding='utf-8') as f:
            f.write(simple_viewer_content)
        print(f"  ✅ تم إنشاء {simple_viewer_path}")
        return True
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء {simple_viewer_path}: {str(e)}")
        return False

def test_file_viewer():
    """اختبار عارض الملفات"""
    print("🧪 اختبار عارض الملفات...")
    
    try:
        from utils.simple_file_viewer import get_file_info, can_view_in_browser, get_file_icon_class
        
        # اختبار الدوال
        test_files = [
            "test.pdf",
            "document.docx", 
            "image.jpg",
            "data.json",
            "unknown.xyz"
        ]
        
        for filename in test_files:
            can_view = can_view_in_browser(filename)
            icon = get_file_icon_class(filename)
            print(f"  📄 {filename}: {'✅ قابل للعرض' if can_view else '❌ غير قابل للعرض'} - {icon}")
        
        print("  ✅ اختبار عارض الملفات نجح")
        return True
        
    except ImportError as e:
        print(f"  ❌ خطأ في الاستيراد: {str(e)}")
        return False
    except Exception as e:
        print(f"  ❌ خطأ في الاختبار: {str(e)}")
        return False

def update_requirements():
    """تحديث ملف المتطلبات"""
    print("📝 تحديث ملف المتطلبات...")
    
    requirements_path = "requirements.txt"
    
    try:
        # قراءة المتطلبات الحالية
        existing_requirements = []
        if os.path.exists(requirements_path):
            with open(requirements_path, 'r') as f:
                existing_requirements = [line.strip() for line in f.readlines()]
        
        # المتطلبات الجديدة لعرض الملفات
        new_requirements = [
            "Pillow>=10.0.0",
            "python-docx>=0.8.11", 
            "PyPDF2>=3.0.1"
        ]
        
        # إضافة المتطلبات الجديدة إذا لم تكن موجودة
        updated = False
        for req in new_requirements:
            package_name = req.split('>=')[0].split('==')[0]
            if not any(package_name in existing for existing in existing_requirements):
                existing_requirements.append(req)
                updated = True
                print(f"  ✅ تم إضافة {req}")
        
        if updated:
            with open(requirements_path, 'w') as f:
                f.write('\n'.join(existing_requirements) + '\n')
            print(f"  ✅ تم تحديث {requirements_path}")
        else:
            print(f"  ✅ {requirements_path} محدث بالفعل")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في تحديث {requirements_path}: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح نظام عرض الملفات")
    print("=" * 50)
    
    steps = [
        ("تثبيت الحزم المطلوبة", install_required_packages),
        ("إنشاء مجلد utils", create_utils_directory),
        ("إصلاح أخطاء القوالب", fix_template_errors),
        ("إنشاء عارض ملفات مبسط", create_simple_file_viewer),
        ("اختبار عارض الملفات", test_file_viewer),
        ("تحديث ملف المتطلبات", update_requirements)
    ]
    
    success_count = 0
    
    for step_name, step_function in steps:
        print(f"\n{len(steps) - success_count}️⃣ {step_name}...")
        if step_function():
            success_count += 1
        else:
            print(f"❌ فشل في {step_name}")
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"✅ نجح: {success_count}/{len(steps)} خطوات")
    
    if success_count == len(steps):
        print("\n🎉 تم إصلاح نظام عرض الملفات بنجاح!")
        print("\n🌟 الميزات المتاحة الآن:")
        print("  📄 عرض ملفات PDF في المتصفح")
        print("  🖼️ عرض الصور مع معاينة")
        print("  📝 عرض الملفات النصية")
        print("  🎵 تشغيل ملفات الصوت")
        print("  🎬 تشغيل ملفات الفيديو")
        print("  📊 أيقونات مناسبة لكل نوع ملف")
        print("  💾 تحميل جميع أنواع الملفات")
        
        print("\n🔗 للاختبار:")
        print("  1. شغل النظام: python debug_start.py")
        print("  2. اذهب إلى: http://localhost:8585")
        print("  3. أنشئ رسالة جديدة مع مرفق")
        print("  4. اضغط على 'عرض الملف' لمعاينة المرفق")
        
        return True
    else:
        print(f"\n❌ فشل في {len(steps) - success_count} خطوات")
        print("\n🔧 تحقق من:")
        print("  • اتصال الإنترنت لتثبيت الحزم")
        print("  • صلاحيات الكتابة في المجلد")
        print("  • صحة ملفات القوالب")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
