import os
from flask import Flask, render_template, redirect, url_for, flash, request, session
from flask_login import LoginManager, login_required, current_user
from config import config
from models import db, User, Message, Department, UserRole, ApprovalStatus
from translations import translation_manager, get_translation, get_current_language, get_text_direction
from werkzeug.security import generate_password_hash

def create_app(config_name=None):
    app = Flask(__name__)
    
    # Load configuration
    config_name = config_name or os.environ.get('FLASK_CONFIG', 'default')
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # Register blueprints
    from routes.auth import auth_bp
    from routes.messages import messages_bp
    from routes.users import users_bp
    from routes.departments import departments_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(messages_bp, url_prefix='/messages')
    app.register_blueprint(users_bp, url_prefix='/users')
    app.register_blueprint(departments_bp, url_prefix='/departments')
    
    # Main routes
    @app.route('/')
    @login_required
    def index():
        # Dashboard statistics
        total_messages = Message.query.count()
        incoming_messages = Message.query.filter_by(message_type='incoming').count()
        outgoing_messages = Message.query.filter_by(message_type='outgoing').count()
        internal_messages = Message.query.filter_by(message_type='internal').count()

        # Recent messages
        recent_messages = Message.query.order_by(Message.date_created.desc()).limit(5).all()

        # Internal messages statistics
        pending_internal = Message.query.filter_by(
            message_type='internal',
            requires_response=True
        ).filter(Message.responses == None).count()

        urgent_internal = Message.query.filter_by(
            message_type='internal',
            is_urgent=True,
            status='active'
        ).count()

        # Department statistics
        from models import Department
        total_departments = Department.query.count()
        active_departments = Department.query.filter_by(is_active=True).count()

        stats = {
            'total_messages': total_messages,
            'incoming_messages': incoming_messages,
            'outgoing_messages': outgoing_messages,
            'internal_messages': internal_messages,
            'pending_internal': pending_internal,
            'urgent_internal': urgent_internal,
            'total_departments': total_departments,
            'active_departments': active_departments,
            'recent_messages': recent_messages
        }
        
        return render_template('dashboard.html', stats=stats)
    
    @app.route('/search')
    @login_required
    def search():
        query = request.args.get('q', '')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        message_type = request.args.get('type')
        department = request.args.get('department')
        
        messages = Message.query
        
        if query:
            messages = messages.filter(
                db.or_(
                    Message.subject.contains(query),
                    Message.destination.contains(query),
                    Message.registration_number.contains(query)
                )
            )
        
        if date_from:
            messages = messages.filter(Message.message_date >= date_from)
        
        if date_to:
            messages = messages.filter(Message.message_date <= date_to)
        
        if message_type:
            messages = messages.filter(Message.message_type == message_type)
        
        if department:
            messages = messages.filter(Message.department == department)
        
        messages = messages.order_by(Message.date_created.desc()).all()
        
        return render_template('search_results.html', messages=messages, query=query)

    @app.route('/change_language/<language>')
    def change_language(language):
        """Change application language"""
        if translation_manager.set_language(language):
            flash(get_translation('language_changed', language), 'success')
        else:
            flash('Invalid language selected', 'error')

        # Redirect back to the referring page or dashboard
        return redirect(request.referrer or url_for('index'))
    
    # Template filters
    @app.template_filter('nl2br')
    def nl2br_filter(text):
        if text:
            return text.replace('\n', '<br>')
        return text

    # Template functions
    @app.template_global()
    def get_file_icon(filename):
        if not filename:
            return 'fa-file'

        extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

        icon_map = {
            'pdf': 'fa-file-pdf',
            'doc': 'fa-file-word',
            'docx': 'fa-file-word',
            'xls': 'fa-file-excel',
            'xlsx': 'fa-file-excel',
            'ppt': 'fa-file-powerpoint',
            'pptx': 'fa-file-powerpoint',
            'jpg': 'fa-file-image',
            'jpeg': 'fa-file-image',
            'png': 'fa-file-image',
            'gif': 'fa-file-image',
            'txt': 'fa-file-text',
            'zip': 'fa-file-archive',
            'rar': 'fa-file-archive',
            '7z': 'fa-file-archive',
        }

        return icon_map.get(extension, 'fa-file')

    @app.template_global()
    def moment():
        """Simple moment-like object for date operations"""
        from datetime import datetime, date
        class MomentLike:
            def date(self):
                return date.today()
            def now(self):
                return datetime.now()
        return MomentLike()

    # Translation functions for templates
    @app.template_global()
    def t(key, language=None):
        """Translation function for templates"""
        return get_translation(key, language)

    @app.template_global()
    def current_language():
        """Get current language"""
        return get_current_language()

    @app.template_global()
    def text_direction():
        """Get text direction"""
        return get_text_direction()

    @app.template_global()
    def language_name():
        """Get current language name"""
        return translation_manager.get_language_name()

    @app.template_global()
    def get_file_icon(filename):
        """Get appropriate icon for file type"""
        if not filename:
            return 'fa-file'

        extension = filename.lower().split('.')[-1] if '.' in filename else ''

        icon_map = {
            # Documents
            'pdf': 'fa-file-pdf',
            'doc': 'fa-file-word',
            'docx': 'fa-file-word',
            'rtf': 'fa-file-word',
            'odt': 'fa-file-word',
            # Spreadsheets
            'xls': 'fa-file-excel',
            'xlsx': 'fa-file-excel',
            'ods': 'fa-file-excel',
            'csv': 'fa-file-csv',
            # Presentations
            'ppt': 'fa-file-powerpoint',
            'pptx': 'fa-file-powerpoint',
            'odp': 'fa-file-powerpoint',
            # Images
            'jpg': 'fa-file-image',
            'jpeg': 'fa-file-image',
            'png': 'fa-file-image',
            'gif': 'fa-file-image',
            'bmp': 'fa-file-image',
            'tiff': 'fa-file-image',
            'svg': 'fa-file-image',
            'webp': 'fa-file-image',
            # Text files
            'txt': 'fa-file-alt',
            'xml': 'fa-file-code',
            'json': 'fa-file-code',
            'html': 'fa-file-code',
            'css': 'fa-file-code',
            'js': 'fa-file-code',
            # Archives
            'zip': 'fa-file-archive',
            'rar': 'fa-file-archive',
            '7z': 'fa-file-archive',
            'tar': 'fa-file-archive',
            'gz': 'fa-file-archive',
            # Audio
            'mp3': 'fa-file-audio',
            'wav': 'fa-file-audio',
            # Video
            'mp4': 'fa-file-video',
            'avi': 'fa-file-video',
            'mov': 'fa-file-video',
            'wmv': 'fa-file-video',
            'flv': 'fa-file-video'
        }

        return icon_map.get(extension, 'fa-file')

    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    # Create database tables and default admin user
    with app.app_context():
        db.create_all()

        # Create default admin user if no users exist
        try:
            if User.query.count() == 0:
                admin = User(
                    name='مدير النظام',
                    username='admin',
                    role=UserRole.MANAGER
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("Default admin user created: username='admin', password='admin123'")
        except Exception as e:
            print(f"Database initialization error: {e}")
            # Create tables if they don't exist
            db.create_all()
            print("Created database tables")
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
