{% extends "base.html" %}

{% block title %}{{ entity.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-building me-2"></i>
                    {{ entity.name }}
                </h2>
                <div>
                    {% if current_user.is_admin() or current_user.is_director() or current_user.is_department_head() or current_user.is_manager() %}
                    <a href="{{ url_for('entities.edit_entity', id=entity.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </a>
                    {% endif %}
                    <a href="{{ url_for('entities.list_entities') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- المعلومات الأساسية -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اسم الجهة</label>
                                    <p class="fw-bold">{{ entity.name }}</p>
                                </div>
                                {% if entity.name_en %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الاسم بالإنجليزية</label>
                                    <p>{{ entity.name_en }}</p>
                                </div>
                                {% endif %}
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">نوع الجهة</label>
                                    <p><span class="badge bg-primary">{{ entity.get_type_display() }}</span></p>
                                </div>
                                {% if entity.category %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">فئة الجهة</label>
                                    <p><span class="badge bg-info">{{ entity.get_category_display() }}</span></p>
                                </div>
                                {% endif %}
                            </div>
                            {% if entity.reference_number %}
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الرقم المرجعي</label>
                                    <p class="text-info fw-bold">{{ entity.reference_number }}</p>
                                </div>
                            </div>
                            {% endif %}
                            {% if entity.address %}
                            <div class="mb-3">
                                <label class="form-label text-muted">العنوان</label>
                                <p>{{ entity.address }}</p>
                            </div>
                            {% endif %}
                            {% if entity.notes %}
                            <div class="mb-3">
                                <label class="form-label text-muted">ملاحظات</label>
                                <p>{{ entity.notes }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-phone me-2"></i>معلومات الاتصال
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% if entity.phone %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الهاتف</label>
                                    <p>
                                        <i class="fas fa-phone me-1 text-primary"></i>
                                        <a href="tel:{{ entity.phone }}" class="text-decoration-none">{{ entity.phone }}</a>
                                    </p>
                                </div>
                                {% endif %}
                                {% if entity.fax %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الفاكس</label>
                                    <p>
                                        <i class="fas fa-fax me-1 text-secondary"></i>
                                        {{ entity.fax }}
                                    </p>
                                </div>
                                {% endif %}
                            </div>
                            <div class="row">
                                {% if entity.email %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">البريد الإلكتروني</label>
                                    <p>
                                        <i class="fas fa-envelope me-1 text-success"></i>
                                        <a href="mailto:{{ entity.email }}" class="text-decoration-none">{{ entity.email }}</a>
                                    </p>
                                </div>
                                {% endif %}
                                {% if entity.website %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الموقع الإلكتروني</label>
                                    <p>
                                        <i class="fas fa-globe me-1 text-info"></i>
                                        <a href="{{ entity.website }}" target="_blank" class="text-decoration-none">{{ entity.website }}</a>
                                    </p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الشخص المسؤول -->
                    {% if entity.contact_person %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>الشخص المسؤول
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الاسم</label>
                                    <p class="fw-bold">{{ entity.contact_person }}</p>
                                </div>
                                {% if entity.contact_title %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">المنصب</label>
                                    <p>{{ entity.contact_title }}</p>
                                </div>
                                {% endif %}
                            </div>
                            <div class="row">
                                {% if entity.contact_phone %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الهاتف</label>
                                    <p>
                                        <i class="fas fa-phone me-1 text-primary"></i>
                                        <a href="tel:{{ entity.contact_phone }}" class="text-decoration-none">{{ entity.contact_phone }}</a>
                                    </p>
                                </div>
                                {% endif %}
                                {% if entity.contact_email %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">البريد الإلكتروني</label>
                                    <p>
                                        <i class="fas fa-envelope me-1 text-success"></i>
                                        <a href="mailto:{{ entity.contact_email }}" class="text-decoration-none">{{ entity.contact_email }}</a>
                                    </p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- الشريط الجانبي -->
                <div class="col-lg-4">
                    <!-- الحالة والإعدادات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>الحالة والإعدادات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">الحالة</label>
                                <p>
                                    <span class="badge {{ entity.get_status_badge_class() }}">
                                        {{ entity.get_status_display() }}
                                    </span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">الخصائص</label>
                                <div>
                                    {% if entity.is_government %}
                                    <span class="badge bg-primary me-1 mb-1">
                                        <i class="fas fa-university me-1"></i>جهة حكومية
                                    </span>
                                    {% endif %}
                                    {% if entity.is_frequent %}
                                    <span class="badge bg-success me-1 mb-1">
                                        <i class="fas fa-star me-1"></i>متكررة التراسل
                                    </span>
                                    {% endif %}
                                    {% if not entity.is_government and not entity.is_frequent %}
                                    <span class="text-muted">لا توجد خصائص خاصة</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التتبع -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>معلومات التتبع
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">أنشئت بواسطة</label>
                                <p>{{ entity.creator.name }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">تاريخ الإنشاء</label>
                                <p>{{ entity.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">آخر تحديث</label>
                                <p>{{ entity.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- إجراءات سريعة -->
                    {% if current_user.is_admin() or current_user.is_director() or current_user.is_department_head() or current_user.is_manager() %}
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tools me-2"></i>إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('entities.edit_entity', id=entity.id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit me-1"></i>تعديل الجهة
                                </a>
                                <button type="button" class="btn btn-danger" 
                                        onclick="confirmDelete({{ entity.id }}, '{{ entity.name }}')">
                                    <i class="fas fa-trash me-1"></i>حذف الجهة
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الجهة <strong id="entityName"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(entityId, entityName) {
    document.getElementById('entityName').textContent = entityName;
    document.getElementById('deleteForm').action = '/entities/' + entityId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.form-label {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

a {
    color: #0d6efd;
}

a:hover {
    color: #0a58ca;
}
</style>
{% endblock %}
