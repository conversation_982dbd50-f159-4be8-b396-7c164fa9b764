#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الأدوار الجديدة في تعديل المستخدم
"""

import requests
import sys

def test_new_roles():
    """اختبار الأدوار الجديدة"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة إنشاء مستخدم جديد
        print("\n➕ اختبار الأدوار في صفحة إنشاء مستخدم...")
        response = session.get(f"{base_url}/users/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء مستخدم تعمل")
            
            content = response.text
            roles_found = []
            
            if 'value="EMPLOYEE"' in content:
                roles_found.append("موظف")
            if 'value="SECRETARY"' in content:
                roles_found.append("سكرتير")
            if 'value="SUPERVISOR"' in content:
                roles_found.append("مشرف")
            if 'value="MANAGER"' in content:
                roles_found.append("مدير")
            if 'value="DEPARTMENT_HEAD"' in content:
                roles_found.append("رئيس قسم")
            if 'value="DIRECTOR"' in content:
                roles_found.append("مدير عام")
            if 'value="ADMIN"' in content:
                roles_found.append("مدير النظام")
            
            print(f"✅ الأدوار المتاحة في إنشاء مستخدم: {', '.join(roles_found)}")
        else:
            print(f"❌ فشل في الوصول لصفحة إنشاء مستخدم: {response.status_code}")
            return False
        
        # اختبار إنشاء مستخدمين بأدوار مختلفة
        print("\n📝 اختبار إنشاء مستخدمين بأدوار جديدة...")
        
        new_roles_to_test = [
            ('test_supervisor', 'مستخدم مشرف', 'SUPERVISOR'),
            ('test_dept_head', 'مستخدم رئيس قسم', 'DEPARTMENT_HEAD'),
            ('test_director', 'مستخدم مدير عام', 'DIRECTOR')
        ]
        
        created_users = []
        for username, name, role in new_roles_to_test:
            user_data = {
                'username': username,
                'name': name,
                'password': 'password123',
                'role': role,
                'is_active': 'on',
                'department_id': '1'
            }
            
            response = session.post(f"{base_url}/users/new", data=user_data)
            if response.status_code == 200:
                print(f"✅ تم إنشاء مستخدم بدور {role} بنجاح")
                created_users.append((username, role))
            else:
                print(f"⚠️ مشكلة في إنشاء مستخدم بدور {role}: {response.status_code}")
        
        # اختبار صفحة تعديل المستخدم
        print("\n✏️ اختبار الأدوار في صفحة تعديل المستخدم...")
        
        # جرب تعديل المستخدم الأول
        response = session.get(f"{base_url}/users/1/edit")
        if response.status_code == 200:
            print("✅ صفحة تعديل المستخدم تعمل")
            
            content = response.text
            edit_roles_found = []
            
            if 'value="EMPLOYEE"' in content:
                edit_roles_found.append("موظف")
            if 'value="SECRETARY"' in content:
                edit_roles_found.append("سكرتير")
            if 'value="SUPERVISOR"' in content:
                edit_roles_found.append("مشرف")
            if 'value="MANAGER"' in content:
                edit_roles_found.append("مدير")
            if 'value="DEPARTMENT_HEAD"' in content:
                edit_roles_found.append("رئيس قسم")
            if 'value="DIRECTOR"' in content:
                edit_roles_found.append("مدير عام")
            if 'value="ADMIN"' in content:
                edit_roles_found.append("مدير النظام")
            
            print(f"✅ الأدوار المتاحة في تعديل المستخدم: {', '.join(edit_roles_found)}")
            
            # التحقق من وجود الوصف المحدث للأدوار
            role_descriptions = []
            if 'المشرف:' in content:
                role_descriptions.append("وصف المشرف")
            if 'رئيس القسم:' in content:
                role_descriptions.append("وصف رئيس القسم")
            if 'المدير العام:' in content:
                role_descriptions.append("وصف المدير العام")
            
            if role_descriptions:
                print(f"✅ أوصاف الأدوار الجديدة موجودة: {', '.join(role_descriptions)}")
            else:
                print("⚠️ أوصاف الأدوار الجديدة غير موجودة")
        else:
            print(f"❌ فشل في الوصول لصفحة تعديل المستخدم: {response.status_code}")
            return False
        
        # اختبار تعديل دور مستخدم موجود
        print("\n🔄 اختبار تعديل دور مستخدم...")
        
        # جرب تغيير دور المستخدم الثاني إلى مشرف
        update_data = {
            'name': 'مستخدم محدث',
            'role': 'SUPERVISOR',
            'is_active': 'on',
            'department_id': '1'
        }
        
        response = session.post(f"{base_url}/users/2/edit", data=update_data)
        if response.status_code == 200:
            print("✅ تم تحديث دور المستخدم إلى مشرف بنجاح")
        else:
            print(f"⚠️ مشكلة في تحديث دور المستخدم: {response.status_code}")
        
        # اختبار قائمة المستخدمين
        print("\n👥 اختبار عرض الأدوار الجديدة في قائمة المستخدمين...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            content = response.text
            
            displayed_roles = []
            if 'مشرف' in content:
                displayed_roles.append("مشرف")
            if 'رئيس قسم' in content:
                displayed_roles.append("رئيس قسم")
            if 'مدير عام' in content:
                displayed_roles.append("مدير عام")
            
            if displayed_roles:
                print(f"✅ الأدوار الجديدة معروضة في القائمة: {', '.join(displayed_roles)}")
            else:
                print("⚠️ الأدوار الجديدة غير معروضة في القائمة")
        else:
            print(f"❌ فشل في الوصول لقائمة المستخدمين: {response.status_code}")
        
        print("\n🎉 تم اكتمال اختبار الأدوار الجديدة!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار الأدوار الجديدة في تعديل المستخدم")
    print("=" * 60)
    
    success = test_new_roles()
    
    if success:
        print("\n✅ جميع الأدوار الجديدة تعمل بنجاح!")
        print("\n📋 الأدوار المتاحة الآن:")
        print("  👤 موظف - الدور الأساسي للموظفين العاديين")
        print("  📝 سكرتير - إدارة المستخدمين والرسائل وأرشفتها")
        print("  👁️ مشرف - اعتماد الرسائل وإدارة المستخدمين")
        print("  👔 مدير - جميع الصلاحيات بما في ذلك حذف الرسائل والتقارير")
        print("  🏢 رئيس قسم - إدارة كاملة للقسم والمستخدمين والرسائل")
        print("  🎯 مدير عام - إدارة جميع الأقسام والمستخدمين والنظام")
        print("  ⚙️ مدير النظام - جميع صلاحيات النظام والإدارة والنسخ الاحتياطي")
        print("\n🔐 الصلاحيات حسب الدور:")
        print("  📊 التقارير: مدير، رئيس قسم، مدير عام، مدير النظام")
        print("  🗑️ حذف الرسائل: مدير، رئيس قسم، مدير عام، مدير النظام")
        print("  ✅ اعتماد الرسائل: مشرف، مدير، رئيس قسم، مدير عام، مدير النظام")
        print("  👥 إدارة المستخدمين: سكرتير، مشرف، مدير، رئيس قسم، مدير عام، مدير النظام")
        print("\n🌐 للوصول لإدارة المستخدمين:")
        print("  http://localhost:8585/users")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n🎯 تم إضافة المزيد من الأدوار الأساسية بنجاح!")
        sys.exit(0)
    else:
        print("\n❌ فشل في اختبار الأدوار الجديدة!")
        sys.exit(1)
