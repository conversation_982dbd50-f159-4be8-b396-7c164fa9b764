#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص وحل مشاكل تشغيل النظام
"""

import os
import sys
import sqlite3
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 التحقق من إصدار Python...")
    version = sys.version_info
    print(f"  📋 الإصدار الحالي: Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("  ✅ إصدار Python مناسب")
        return True
    else:
        print("  ❌ إصدار Python قديم، يُنصح بـ Python 3.8 أو أحدث")
        return False

def check_required_packages():
    """التحقق من الحزم المطلوبة"""
    print("\n📦 التحقق من الحزم المطلوبة...")
    
    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'werkzeug',
        'python-docx',
        'fpdf',
        'PyPDF2',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - مفقود")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📥 تثبيت الحزم المفقودة...")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"  ✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"  ❌ فشل في تثبيت {package}")
                return False
    
    return True

def check_database():
    """التحقق من قاعدة البيانات"""
    print("\n🗄️ التحقق من قاعدة البيانات...")
    
    db_path = "correspondence.db"
    
    if not os.path.exists(db_path):
        print("  ⚠️ قاعدة البيانات غير موجودة، سيتم إنشاؤها...")
        return create_database()
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من الجداول الأساسية
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['users', 'messages', 'departments', 'entities', 'system_settings']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"  ⚠️ جداول مفقودة: {', '.join(missing_tables)}")
            conn.close()
            return create_database()
        
        print("  ✅ قاعدة البيانات سليمة")
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في قاعدة البيانات: {str(e)}")
        return create_database()

def create_database():
    """إنشاء قاعدة البيانات"""
    print("  🔧 إنشاء قاعدة البيانات...")
    
    try:
        from app import create_app
        from models import db
        
        app = create_app()
        with app.app_context():
            db.create_all()
            print("  ✅ تم إنشاء قاعدة البيانات")
            
            # إنشاء مستخدم admin إذا لم يكن موجوداً
            from models import User, UserRole
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    name='مدير النظام',
                    email='<EMAIL>',
                    role=UserRole.ADMIN,
                    is_active=True
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("  ✅ تم إنشاء مستخدم admin")
            
            return True
            
    except Exception as e:
        print(f"  ❌ فشل في إنشاء قاعدة البيانات: {str(e)}")
        return False

def check_file_structure():
    """التحقق من هيكل الملفات"""
    print("\n📁 التحقق من هيكل الملفات...")
    
    required_files = [
        'app.py',
        'models.py',
        'routes/__init__.py',
        'routes/auth.py',
        'routes/messages.py',
        'routes/users.py',
        'routes/departments.py',
        'routes/entities.py',
        'routes/settings.py',
        'templates/base.html',
        'templates/dashboard.html',
        'static/css/style.css',
        'static/js/main.js'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            print(f"  ❌ {file_path} - مفقود")
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {len(missing_files)}")
        return False
    
    return True

def check_static_files():
    """التحقق من الملفات الثابتة"""
    print("\n🎨 التحقق من الملفات الثابتة...")
    
    static_dirs = ['static', 'static/css', 'static/js', 'static/images', 'uploads']
    
    for dir_path in static_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            print(f"  ✅ تم إنشاء مجلد: {dir_path}")
        else:
            print(f"  ✅ {dir_path}")
    
    return True

def check_permissions():
    """التحقق من الصلاحيات"""
    print("\n🔐 التحقق من صلاحيات الملفات...")
    
    try:
        # التحقق من إمكانية الكتابة في المجلد الحالي
        test_file = "test_write.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("  ✅ صلاحيات الكتابة متاحة")
        
        # التحقق من مجلد uploads
        uploads_dir = "uploads"
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
        
        test_upload = os.path.join(uploads_dir, "test.tmp")
        with open(test_upload, 'w') as f:
            f.write("test")
        os.remove(test_upload)
        print("  ✅ صلاحيات uploads متاحة")
        
        return True
        
    except Exception as e:
        print(f"  ❌ مشكلة في الصلاحيات: {str(e)}")
        return False

def test_app_startup():
    """اختبار تشغيل التطبيق"""
    print("\n🚀 اختبار تشغيل التطبيق...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            # اختبار الصفحة الرئيسية
            response = client.get('/')
            if response.status_code in [200, 302]:  # 302 للتوجيه لصفحة تسجيل الدخول
                print("  ✅ الصفحة الرئيسية تعمل")
            else:
                print(f"  ❌ مشكلة في الصفحة الرئيسية: {response.status_code}")
                return False
            
            # اختبار صفحة تسجيل الدخول
            response = client.get('/auth/login')
            if response.status_code == 200:
                print("  ✅ صفحة تسجيل الدخول تعمل")
            else:
                print(f"  ❌ مشكلة في صفحة تسجيل الدخول: {response.status_code}")
                return False
        
        print("  ✅ التطبيق يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في تشغيل التطبيق: {str(e)}")
        return False

def fix_common_issues():
    """إصلاح المشاكل الشائعة"""
    print("\n🔧 إصلاح المشاكل الشائعة...")
    
    try:
        # إصلاح مشكلة المنفذ المحجوز
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8585))
        sock.close()
        
        if result == 0:
            print("  ⚠️ المنفذ 8585 محجوز، سيتم استخدام منفذ بديل")
            return 8586
        else:
            print("  ✅ المنفذ 8585 متاح")
            return 8585
            
    except Exception as e:
        print(f"  ⚠️ لا يمكن التحقق من المنفذ: {str(e)}")
        return 8585

def create_startup_script():
    """إنشاء ملف تشغيل محسن"""
    print("\n📝 إنشاء ملف تشغيل محسن...")
    
    startup_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل النظام المحسن
"""

import os
import sys
from app import create_app

def main():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل نظام المراسلات الإلكترونية")
    print("=" * 60)
    
    try:
        # إنشاء التطبيق
        app = create_app()
        
        # معلومات التشغيل
        print("✅ تم تحميل النظام بنجاح!")
        print("🌐 النظام متاح على:")
        print("   🔗 http://localhost:8585")
        print("   🔗 http://127.0.0.1:8585")
        print("=" * 60)
        print("🔐 معلومات تسجيل الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔐 كلمة المرور: admin123")
        print("=" * 60)
        print("⚡ المنفذ: 8585")
        print("🛑 لإيقاف الخادم: Ctrl+C")
        print("=" * 60)
        
        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=8585,
            debug=False,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        print("🔧 تشغيل أداة التشخيص...")
        os.system("python fix_system_issues.py")

if __name__ == "__main__":
    main()
'''
    
    with open('run_system.py', 'w', encoding='utf-8') as f:
        f.write(startup_script)
    
    print("  ✅ تم إنشاء ملف run_system.py")
    return True

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 تشخيص وإصلاح مشاكل النظام")
    print("=" * 50)
    
    issues_found = []
    
    # التحقق من Python
    if not check_python_version():
        issues_found.append("إصدار Python")
    
    # التحقق من الحزم
    if not check_required_packages():
        issues_found.append("الحزم المطلوبة")
    
    # التحقق من هيكل الملفات
    if not check_file_structure():
        issues_found.append("هيكل الملفات")
    
    # التحقق من الملفات الثابتة
    if not check_static_files():
        issues_found.append("الملفات الثابتة")
    
    # التحقق من قاعدة البيانات
    if not check_database():
        issues_found.append("قاعدة البيانات")
    
    # التحقق من الصلاحيات
    if not check_permissions():
        issues_found.append("صلاحيات الملفات")
    
    # اختبار التطبيق
    if not test_app_startup():
        issues_found.append("تشغيل التطبيق")
    
    # إصلاح المشاكل الشائعة
    port = fix_common_issues()
    
    # إنشاء ملف التشغيل
    create_startup_script()
    
    print("\n" + "=" * 50)
    
    if issues_found:
        print("⚠️ تم العثور على مشاكل:")
        for issue in issues_found:
            print(f"  ❌ {issue}")
        print("\n🔧 تم محاولة إصلاح المشاكل تلقائياً")
        print("💡 إذا استمرت المشاكل، تحقق من:")
        print("  • تثبيت Python 3.8 أو أحدث")
        print("  • تثبيت جميع الحزم المطلوبة")
        print("  • صلاحيات الكتابة في المجلد")
        print("  • عدم حجز المنفذ 8585")
    else:
        print("✅ النظام جاهز للتشغيل!")
        print("\n🚀 لتشغيل النظام:")
        print("  python run_system.py")
        print("\n🌐 أو:")
        print("  python app.py")
        print(f"\n🔗 ثم اذهب إلى: http://localhost:{port}")

if __name__ == "__main__":
    main()
