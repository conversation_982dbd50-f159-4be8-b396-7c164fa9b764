{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-user me-2"></i>
            الملف الشخصي
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">معلوماتي الشخصية</h5>
                <div>
                    {% if current_user.role.value == 'manager' %}
                        <span class="badge bg-danger fs-6">مدير</span>
                    {% elif current_user.role.value == 'secretary' %}
                        <span class="badge bg-warning fs-6">سكرتير</span>
                    {% else %}
                        <span class="badge bg-info fs-6">موظف</span>
                    {% endif %}
                    <span class="badge bg-success fs-6">نشط</span>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الاسم الكامل:</strong>
                        <p class="mb-0">{{ current_user.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>اسم المستخدم:</strong>
                        <p class="mb-0">{{ current_user.username }}</p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الدور:</strong>
                        <p class="mb-0">
                            {% if current_user.role.value == 'manager' %}
                                مدير النظام
                            {% elif current_user.role.value == 'secretary' %}
                                سكرتير
                            {% else %}
                                موظف
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الانضمام:</strong>
                        <p class="mb-0">{{ current_user.created_at.strftime('%Y-%m-%d') }}</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>الصلاحيات:</strong>
                    <div class="mt-2">
                        {% if current_user.role.value == 'manager' %}
                        <span class="badge bg-success me-1">جميع الصلاحيات</span>
                        <span class="badge bg-primary me-1">إدارة المستخدمين</span>
                        <span class="badge bg-primary me-1">حذف الرسائل</span>
                        <span class="badge bg-primary me-1">إدارة النظام</span>
                        {% elif current_user.role.value == 'secretary' %}
                        <span class="badge bg-warning me-1">إدارة المستخدمين</span>
                        <span class="badge bg-info me-1">إدارة الرسائل</span>
                        <span class="badge bg-info me-1">عرض التقارير</span>
                        {% else %}
                        <span class="badge bg-secondary me-1">عرض الرسائل</span>
                        <span class="badge bg-secondary me-1">إنشاء الرسائل</span>
                        <span class="badge bg-secondary me-1">تعديل رسائلي فقط</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- My Recent Messages -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">رسائلي الحديثة</h5>
            </div>
            <div class="card-body">
                {% set recent_messages = current_user.created_messages.order_by(current_user.created_messages.c.date_created.desc()).limit(5) %}
                {% if recent_messages %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>النوع</th>
                                <th>الموضوع</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in recent_messages %}
                            <tr>
                                <td>{{ message.registration_number }}</td>
                                <td>
                                    {% if message.is_incoming() %}
                                        <span class="badge bg-success">واردة</span>
                                    {% else %}
                                        <span class="badge bg-info">صادرة</span>
                                    {% endif %}
                                </td>
                                <td>{{ message.subject[:30] }}{% if message.subject|length > 30 %}...{% endif %}</td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('messages.list_messages') }}?created_by={{ current_user.id }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع رسائلي
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p class="mb-0">لم تقم بإنشاء أي رسائل حتى الآن</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الإجراءات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-warning">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </a>
                    
                    <a href="{{ url_for('messages.new_message') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        رسالة جديدة
                    </a>
                    
                    <a href="{{ url_for('messages.list_messages') }}" class="btn btn-outline-info">
                        <i class="fas fa-envelope me-2"></i>
                        جميع الرسائل
                    </a>
                    
                    {% if current_user.can_manage_users() %}
                    <a href="{{ url_for('users.list_users') }}" class="btn btn-outline-success">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">إحصائياتي</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ current_user.created_messages.filter_by(message_type='incoming').count() }}</h4>
                        <small class="text-muted">رسائل واردة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ current_user.created_messages.filter_by(message_type='outgoing').count() }}</h4>
                        <small class="text-muted">رسائل صادرة</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-12">
                        <h4 class="text-success">{{ current_user.created_messages.count() }}</h4>
                        <small class="text-muted">إجمالي الرسائل</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Links -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">روابط سريعة</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('index') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-home me-2"></i>الرئيسية
                    </a>
                    <a href="{{ url_for('search') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-search me-2"></i>البحث
                    </a>
                    <a href="{{ url_for('messages.list_messages', type='incoming') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-inbox me-2"></i>الرسائل الواردة
                    </a>
                    <a href="{{ url_for('messages.list_messages', type='outgoing') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-paper-plane me-2"></i>الرسائل الصادرة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
