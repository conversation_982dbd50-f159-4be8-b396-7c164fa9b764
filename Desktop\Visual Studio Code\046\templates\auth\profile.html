{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user me-2"></i>
                    الملف الشخصي
                </h2>
                <div>
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-1"></i>تعديل الملف الشخصي
                    </a>
                </div>
            </div>
        </div>
    </div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">معلوماتي الشخصية</h5>
                <div>
                    {% if current_user.role.value == 'ADMIN' %}
                        <span class="badge bg-danger fs-6">مدير النظام</span>
                    {% elif current_user.role.value == 'MANAGER' %}
                        <span class="badge bg-warning fs-6">مدير</span>
                    {% elif current_user.role.value == 'SECRETARY' %}
                        <span class="badge bg-info fs-6">سكرتير</span>
                    {% else %}
                        <span class="badge bg-secondary fs-6">موظف</span>
                    {% endif %}
                    {% if current_user.is_active %}
                        <span class="badge bg-success fs-6">نشط</span>
                    {% else %}
                        <span class="badge bg-danger fs-6">غير نشط</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الاسم الكامل:</strong>
                        <p class="mb-0">{{ current_user.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>اسم المستخدم:</strong>
                        <p class="mb-0">{{ current_user.username }}</p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الدور:</strong>
                        <p class="mb-0">
                            {% if current_user.role.value == 'ADMIN' %}
                                مدير النظام
                            {% elif current_user.role.value == 'MANAGER' %}
                                مدير
                            {% elif current_user.role.value == 'SECRETARY' %}
                                سكرتير
                            {% else %}
                                موظف
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الانضمام:</strong>
                        <p class="mb-0">{{ current_user.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>

                {% if department_info %}
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>القسم:</strong>
                        <p class="mb-0">{{ department_info.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>مدير القسم:</strong>
                        <p class="mb-0">{{ department_info.manager }}</p>
                    </div>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <strong>الصلاحيات:</strong>
                    <div class="mt-2">
                        {% if user_permissions %}
                            {% for permission in user_permissions[:8] %}
                                <span class="badge bg-primary me-1 mb-1">{{ permission }}</span>
                            {% endfor %}
                            {% if user_permissions|length > 8 %}
                                <span class="badge bg-secondary me-1">+{{ user_permissions|length - 8 }} أخرى</span>
                            {% endif %}
                        {% else %}
                            <span class="badge bg-secondary">لا توجد صلاحيات محددة</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات مفصلة -->
        <div class="card mt-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>إحصائياتي التفصيلية
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1">{{ user_stats.total_messages }}</h4>
                            <small class="text-muted">إجمالي الرسائل</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1">{{ user_stats.incoming_messages }}</h4>
                            <small class="text-muted">رسائل واردة</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info mb-1">{{ user_stats.outgoing_messages }}</h4>
                            <small class="text-muted">رسائل صادرة</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-warning mb-1">{{ user_stats.this_month_messages }}</h4>
                            <small class="text-muted">هذا الشهر</small>
                        </div>
                    </div>
                </div>

                {% if user_stats.internal_messages > 0 %}
                <div class="row text-center mt-3">
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h4 class="text-secondary mb-1">{{ user_stats.internal_messages }}</h4>
                            <small class="text-muted">رسائل داخلية</small>
                        </div>
                    </div>
                    {% if user_stats.pending_approval > 0 %}
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h4 class="text-danger mb-1">{{ user_stats.pending_approval }}</h4>
                            <small class="text-muted">في انتظار الاعتماد</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- My Recent Messages -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>رسائلي الحديثة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_messages %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>النوع</th>
                                <th>الموضوع</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in recent_messages %}
                            <tr>
                                <td>{{ message.registration_number }}</td>
                                <td>
                                    {% if message.is_incoming() %}
                                        <span class="badge bg-success">واردة</span>
                                    {% else %}
                                        <span class="badge bg-info">صادرة</span>
                                    {% endif %}
                                </td>
                                <td>{{ message.subject[:30] }}{% if message.subject|length > 30 %}...{% endif %}</td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('messages.list_messages') }}?created_by={{ current_user.id }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع رسائلي
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p class="mb-0">لم تقم بإنشاء أي رسائل حتى الآن</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- الرسائل المكلف بها -->
        {% if assigned_messages %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>الرسائل المكلف بها
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>الموضوع</th>
                                <th>من</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in assigned_messages %}
                            <tr>
                                <td>{{ message.registration_number }}</td>
                                <td>{{ message.subject[:30] }}{% if message.subject|length > 30 %}...{% endif %}</td>
                                <td>{{ message.creator.name }}</td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if message.requires_response %}
                                        <span class="badge bg-warning">يتطلب رد</span>
                                    {% endif %}
                                    {% if message.is_urgent %}
                                        <span class="badge bg-danger">عاجل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الإجراءات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الملف الشخصي
                    </a>

                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-warning">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </a>

                    <a href="{{ url_for('users.signature') }}" class="btn btn-outline-info">
                        <i class="fas fa-signature me-2"></i>
                        التوقيع الإلكتروني
                    </a>

                    <hr>

                    <a href="{{ url_for('messages.new_message') }}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>
                        رسالة جديدة
                    </a>

                    <a href="{{ url_for('messages.list_messages') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-envelope me-2"></i>
                        جميع الرسائل
                    </a>

                    {% if current_user.can_manage_users() %}
                    <hr>
                    <a href="{{ url_for('users.list_users') }}" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </a>

                    <a href="{{ url_for('departments.list_departments') }}" class="btn btn-outline-info">
                        <i class="fas fa-building me-2"></i>
                        إدارة الأقسام
                    </a>
                    {% endif %}

                    {% if current_user.role.value == 'ADMIN' %}
                    <a href="{{ url_for('permissions.index') }}" class="btn btn-outline-danger">
                        <i class="fas fa-shield-alt me-2"></i>
                        إدارة الصلاحيات
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>إحصائياتي
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h5 class="text-success mb-1">{{ user_stats.incoming_messages }}</h5>
                            <small class="text-muted">واردة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h5 class="text-info mb-1">{{ user_stats.outgoing_messages }}</h5>
                            <small class="text-muted">صادرة</small>
                        </div>
                    </div>
                </div>

                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h5 class="text-warning mb-1">{{ user_stats.internal_messages }}</h5>
                            <small class="text-muted">داخلية</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h5 class="text-primary mb-1">{{ user_stats.this_month_messages }}</h5>
                            <small class="text-muted">هذا الشهر</small>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <div class="border rounded p-3 bg-light">
                        <h4 class="text-dark mb-1">{{ user_stats.total_messages }}</h4>
                        <strong class="text-muted">إجمالي الرسائل</strong>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Links -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">روابط سريعة</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('index') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-home me-2"></i>الرئيسية
                    </a>
                    <a href="{{ url_for('search') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-search me-2"></i>البحث
                    </a>
                    <a href="{{ url_for('messages.list_messages', type='incoming') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-inbox me-2"></i>الرسائل الواردة
                    </a>
                    <a href="{{ url_for('messages.list_messages', type='outgoing') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-paper-plane me-2"></i>الرسائل الصادرة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.border {
    border-color: #e9ecef !important;
    transition: all 0.2s;
}

.border:hover {
    border-color: #007bff !important;
    background-color: #f8f9fa !important;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.btn {
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
}

.table-responsive {
    border-radius: 0.375rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.list-group-item {
    border: none;
    padding: 0.75rem 1rem;
    transition: all 0.2s;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-info { color: #0dcaf0 !important; }
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }
</style>
</div>
{% endblock %}
