#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام عرض الملفات المتقدم
"""

import os
import sys
import requests
import subprocess
import time
from pathlib import Path

def create_test_files():
    """إنشاء ملفات اختبار متنوعة"""
    print("📁 إنشاء ملفات اختبار...")
    
    test_files_dir = "test_files"
    os.makedirs(test_files_dir, exist_ok=True)
    
    # ملف نصي
    with open(f"{test_files_dir}/test_document.txt", "w", encoding="utf-8") as f:
        f.write("""هذا ملف نصي تجريبي لاختبار عرض الملفات

المحتوى:
- نص عربي
- English text
- أرقام: 123456
- رموز: !@#$%^&*()

تاريخ الإنشاء: 2025-06-06
النظام: نظام المراسلات الإلكترونية

هذا النص يحتوي على محتوى متنوع لاختبار عرض الملفات النصية في النظام.
يجب أن يظهر النص بشكل صحيح مع دعم اللغة العربية والإنجليزية.

الميزات المختبرة:
✓ عرض النص العربي
✓ عرض النص الإنجليزي
✓ عرض الأرقام والرموز
✓ تنسيق النص
✓ ترميز UTF-8
""")
    
    # ملف JSON
    import json
    test_data = {
        "name": "نظام المراسلات الإلكترونية",
        "version": "1.0.0",
        "features": [
            "إدارة الرسائل",
            "رفع الملفات",
            "عرض الملفات",
            "نظام الموافقات"
        ],
        "settings": {
            "language": "ar",
            "theme": "default",
            "max_file_size": "100MB"
        },
        "users": [
            {"id": 1, "name": "مدير النظام", "role": "admin"},
            {"id": 2, "name": "موظف", "role": "employee"}
        ]
    }
    
    with open(f"{test_files_dir}/test_data.json", "w", encoding="utf-8") as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    # ملف CSV
    with open(f"{test_files_dir}/test_data.csv", "w", encoding="utf-8") as f:
        f.write("""الاسم,القسم,الوظيفة,الراتب
أحمد محمد,تقنية المعلومات,مطور,8000
فاطمة علي,الموارد البشرية,محاسبة,6000
محمد سالم,الإدارة,مدير,12000
نورا أحمد,التسويق,مسوقة,5500
""")
    
    # ملف HTML
    with open(f"{test_files_dir}/test_page.html", "w", encoding="utf-8") as f:
        f.write("""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>صفحة اختبار</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #0066cc; }
        .highlight { background-color: yellow; }
    </style>
</head>
<body>
    <h1>صفحة اختبار HTML</h1>
    <p>هذه صفحة HTML تجريبية لاختبار عرض الملفات.</p>
    <ul>
        <li>عنصر أول</li>
        <li>عنصر ثاني</li>
        <li class="highlight">عنصر مميز</li>
    </ul>
    <p>تاريخ الإنشاء: <strong>2025-06-06</strong></p>
</body>
</html>""")
    
    # ملف XML
    with open(f"{test_files_dir}/test_config.xml", "w", encoding="utf-8") as f:
        f.write("""<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system>
        <name>نظام المراسلات الإلكترونية</name>
        <version>1.0.0</version>
        <language>ar</language>
    </system>
    <database>
        <type>SQLite</type>
        <file>correspondence.db</file>
    </database>
    <features>
        <feature name="file_upload" enabled="true" />
        <feature name="file_preview" enabled="true" />
        <feature name="approval_system" enabled="true" />
    </features>
</configuration>""")
    
    print(f"✅ تم إنشاء ملفات الاختبار في مجلد {test_files_dir}")
    return test_files_dir

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 
            'Pillow', 'python-docx', 'PyPDF2'
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ تم تثبيت المتطلبات")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def test_document_viewer():
    """اختبار عارض المستندات"""
    print("\n🧪 اختبار عارض المستندات...")
    
    try:
        from utils.document_viewer import document_viewer
        
        # اختبار الملفات المختلفة
        test_files = [
            ("test_document.txt", "ملف نصي"),
            ("test_data.json", "ملف JSON"),
            ("test_data.csv", "ملف CSV"),
            ("test_page.html", "ملف HTML"),
            ("test_config.xml", "ملف XML")
        ]
        
        for filename, description in test_files:
            file_path = f"test_files/{filename}"
            if os.path.exists(file_path):
                can_view = document_viewer.can_view(filename)
                print(f"  📄 {description}: {'✅ مدعوم' if can_view else '❌ غير مدعوم'}")
                
                if can_view:
                    try:
                        content = document_viewer.view_file(file_path, filename)
                        if content and len(content) > 0:
                            print(f"    ✅ تم عرض المحتوى ({len(content)} حرف)")
                        else:
                            print(f"    ⚠️ محتوى فارغ")
                    except Exception as e:
                        print(f"    ❌ خطأ في العرض: {str(e)}")
            else:
                print(f"  ❌ {description}: ملف غير موجود")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_file_viewer_routes():
    """اختبار مسارات عرض الملفات"""
    print("\n🌐 اختبار مسارات عرض الملفات...")
    
    # تشغيل الخادم في الخلفية
    print("🚀 تشغيل الخادم...")
    try:
        server_process = subprocess.Popen([
            'python', 'debug_start.py'
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # انتظار تشغيل الخادم
        time.sleep(5)
        
        base_url = "http://localhost:8585"
        
        # إنشاء جلسة
        session = requests.Session()
        
        # تسجيل الدخول
        print("🔐 تسجيل الدخول...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        # اختبار صفحة الملفات
        print("📁 اختبار صفحة الملفات...")
        response = session.get(f"{base_url}/messages/files")
        if response.status_code == 200:
            print("✅ صفحة الملفات تعمل")
            
            content = response.text
            if 'قائمة الملفات' in content:
                print("✅ محتوى الصفحة صحيح")
            if 'معاينة متقدمة' in content:
                print("✅ أزرار المعاينة المتقدمة موجودة")
        else:
            print(f"❌ فشل في الوصول لصفحة الملفات: {response.status_code}")
        
        # اختبار مسار المعاينة (إذا كان هناك ملفات)
        print("👁️ اختبار مسار المعاينة...")
        # هذا سيفشل إذا لم تكن هناك ملفات مرفوعة، وهذا طبيعي
        
        print("✅ تم اكتمال اختبار المسارات")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False
    
    finally:
        # إيقاف الخادم
        try:
            server_process.terminate()
            server_process.wait(timeout=5)
        except:
            try:
                server_process.kill()
            except:
                pass

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار نظام عرض الملفات المتقدم")
    print("=" * 50)
    
    # إنشاء ملفات الاختبار
    test_files_dir = create_test_files()
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return False
    
    # اختبار عارض المستندات
    viewer_test = test_document_viewer()
    
    # اختبار المسارات
    routes_test = test_file_viewer_routes()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    
    if viewer_test:
        print("✅ عارض المستندات يعمل بشكل صحيح")
    else:
        print("❌ مشكلة في عارض المستندات")
    
    if routes_test:
        print("✅ مسارات عرض الملفات تعمل")
    else:
        print("❌ مشكلة في مسارات عرض الملفات")
    
    if viewer_test and routes_test:
        print("\n🎉 نظام عرض الملفات يعمل بشكل مثالي!")
        print("\n🌟 الميزات المتاحة:")
        print("  📄 عرض ملفات DOCX مع التنسيق")
        print("  📋 عرض ملفات PDF مع استخراج النص")
        print("  🖼️ عرض الصور مع معلومات تفصيلية")
        print("  📝 عرض الملفات النصية")
        print("  📊 عرض ملفات CSV كجداول")
        print("  🔧 عرض ملفات JSON و XML")
        print("  🌐 عرض ملفات HTML")
        print("  📦 عرض محتويات ملفات ZIP")
        print("  🎵 تشغيل ملفات الصوت")
        print("  🎬 تشغيل ملفات الفيديو")
        
        print("\n🔗 الروابط:")
        print("  📁 قائمة الملفات: http://localhost:8585/messages/files")
        print("  📧 الرسائل: http://localhost:8585/messages")
        
        return True
    else:
        print("\n❌ هناك مشاكل في نظام عرض الملفات")
        print("\n🔧 تحقق من:")
        print("  • تثبيت المتطلبات: pip install Pillow python-docx PyPDF2")
        print("  • وجود ملف utils/document_viewer.py")
        print("  • صحة مسارات عرض الملفات")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
