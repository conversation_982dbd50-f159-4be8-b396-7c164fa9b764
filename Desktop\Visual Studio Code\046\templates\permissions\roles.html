{% extends "base.html" %}

{% block title %}إدارة الأدوار - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users-cog me-2"></i>
                إدارة الأدوار
            </h1>
            <div>
                <a href="{{ url_for('permissions.index') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة
                </a>
                <a href="{{ url_for('permissions.new_role') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    دور جديد
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الأدوار
                </h5>
            </div>
            <div class="card-body">
                {% if roles %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الدور</th>
                                <th>الوصف</th>
                                <th>عدد الصلاحيات</th>
                                <th>عدد المستخدمين</th>
                                <th>نوع الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles %}
                            <tr>
                                <td>
                                    <strong>{{ role.display_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ role.name }}</small>
                                </td>
                                <td>
                                    {% if role.description %}
                                        {{ role.description[:100] }}{% if role.description|length > 100 %}...{% endif %}
                                    {% else %}
                                        <span class="text-muted">لا يوجد وصف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ role.permissions|length }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ role.users|length }}</span>
                                </td>
                                <td>
                                    {% if role.is_system_role %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-cog me-1"></i>
                                            دور نظام
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-user me-1"></i>
                                            دور مخصص
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if role.is_active %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            نشط
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>
                                            غير نشط
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ role.created_at.strftime('%Y-%m-%d') }}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#viewRoleModal{{ role.id }}"
                                                title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('permissions.edit_role', role_id=role.id) }}" 
                                           class="btn btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if not role.is_system_role and role.users|length == 0 %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#deleteRoleModal{{ role.id }}"
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users-cog fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أدوار محددة</h5>
                    <p class="text-muted">ابدأ بإنشاء دور جديد لتنظيم صلاحيات المستخدمين</p>
                    <a href="{{ url_for('permissions.new_role') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء دور جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- View Role Modals -->
{% for role in roles %}
<div class="modal fade" id="viewRoleModal{{ role.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الدور: {{ role.display_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>اسم الدور:</strong> {{ role.display_name }}
                    </div>
                    <div class="col-md-6">
                        <strong>الاسم التقني:</strong> {{ role.name }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>الوصف:</strong>
                        <p>{{ role.description or 'لا يوجد وصف' }}</p>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>نوع الدور:</strong>
                        {% if role.is_system_role %}
                            <span class="badge bg-warning">دور نظام</span>
                        {% else %}
                            <span class="badge bg-secondary">دور مخصص</span>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <strong>الحالة:</strong>
                        {% if role.is_active %}
                            <span class="badge bg-success">نشط</span>
                        {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <strong>عدد المستخدمين:</strong>
                        <span class="badge bg-info">{{ role.users|length }}</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <strong>الصلاحيات ({{ role.permissions|length }}):</strong>
                        {% if role.permissions %}
                        <div class="mt-2">
                            {% for permission in role.permissions %}
                            <span class="badge bg-primary me-1 mb-1">{{ permission.display_name }}</span>
                            {% endfor %}
                        </div>
                        {% else %}
                        <p class="text-muted">لا توجد صلاحيات مرتبطة بهذا الدور</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="{{ url_for('permissions.edit_role', role_id=role.id) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Role Modals -->
{% if not role.is_system_role and role.users|length == 0 %}
<div class="modal fade" id="deleteRoleModal{{ role.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حذف الدور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الدور <strong>{{ role.display_name }}</strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('permissions.delete_role', role_id=role.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}

{% block extra_css %}
<style>
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
