{% extends "base.html" %}

{% block title %}تعديل {{ department.name }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-edit me-2"></i>
                تعديل القسم: {{ department.name }}
            </h1>
            <div>
                <a href="{{ url_for('departments.view_department', id=department.id) }}" class="btn btn-info">
                    <i class="fas fa-eye me-2"></i>
                    عرض القسم
                </a>
                <a href="{{ url_for('departments.list_departments') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    تعديل بيانات القسم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ department.name }}" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم القسم
                            </div>
                            <div class="form-text">
                                يجب أن يكون اسم القسم فريداً
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="manager_id" class="form-label">مدير القسم</label>
                            <select class="form-select" id="manager_id" name="manager_id">
                                <option value="">اختر مدير القسم (اختياري)</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" 
                                        {% if department.manager_id == user.id %}selected{% endif %}>
                                    {{ user.name }} ({{ user.role.value }})
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                مدير القسم الحالي: 
                                {% if department.manager_id %}
                                    <strong>{{ department.manager.name }}</strong>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف القسم</label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="وصف مختصر عن القسم ومهامه (اختياري)">{{ department.description or '' }}</textarea>
                        <div class="form-text">
                            وصف مختصر يساعد في فهم دور القسم ومسؤولياته
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {% if department.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                <i class="fas fa-toggle-{{ 'on' if department.is_active else 'off' }} 
                                   text-{{ 'success' if department.is_active else 'secondary' }} me-1"></i>
                                القسم {{ 'نشط' if department.is_active else 'غير نشط' }}
                            </label>
                        </div>
                        <div class="form-text">
                            الأقسام النشطة فقط تظهر في قوائم الاختيار
                        </div>
                    </div>
                    
                    <!-- Change History -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>معلومات القسم:</strong>
                        <ul class="mb-0 mt-2">
                            <li>تاريخ الإنشاء: {{ department.created_at.strftime('%Y-%m-%d %H:%M') }}</li>
                            <li>الحالة الحالية: 
                                {% if department.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </li>
                            {% if department.manager_id %}
                            <li>المدير الحالي: {{ department.manager.name }}</li>
                            {% endif %}
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                        <div>
                            <a href="{{ url_for('departments.view_department', id=department.id) }}" class="btn btn-info me-2">
                                <i class="fas fa-eye me-2"></i>
                                عرض القسم
                            </a>
                            <a href="{{ url_for('departments.list_departments') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Department Statistics -->
<div class="row mt-4 justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات القسم
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="stat-card">
                            <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary">{{ department.messages_count if department.messages_count else 0 }}</h4>
                            <p class="mb-0">إجمالي الرسائل</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card">
                            <i class="fas fa-building fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning">{{ department.internal_messages_count if department.internal_messages_count else 0 }}</h4>
                            <p class="mb-0">المراسلات الداخلية</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card">
                            <i class="fas fa-users fa-2x text-info mb-2"></i>
                            <h4 class="text-info">{{ department.employees_count if department.employees_count else 0 }}</h4>
                            <p class="mb-0">الموظفين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Warning for Changes -->
<div class="row mt-4 justify-content-center">
    <div class="col-lg-8">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيهات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-warning">
                            <i class="fas fa-info-circle me-1"></i>
                            تأثير التغييرات
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-muted me-2"></i>
                                تغيير اسم القسم سيؤثر على الرسائل المستقبلية
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-muted me-2"></i>
                                إلغاء تفعيل القسم سيخفيه من القوائم
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-muted me-2"></i>
                                تغيير المدير سيؤثر على الصلاحيات
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            احتياطات
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                الرسائل الموجودة لن تتأثر
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                يمكن التراجع عن التغييرات
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                سيتم حفظ سجل التغييرات
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stat-card {
    padding: 20px;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Toggle switch styling
    const toggleSwitch = document.getElementById('is_active');
    const toggleLabel = toggleSwitch.nextElementSibling;
    
    toggleSwitch.addEventListener('change', function() {
        const icon = toggleLabel.querySelector('i');
        if (this.checked) {
            icon.className = 'fas fa-toggle-on text-success me-1';
            toggleLabel.innerHTML = '<i class="fas fa-toggle-on text-success me-1"></i>القسم نشط';
        } else {
            icon.className = 'fas fa-toggle-off text-secondary me-1';
            toggleLabel.innerHTML = '<i class="fas fa-toggle-off text-secondary me-1"></i>القسم غير نشط';
        }
    });
    
    // Warn about unsaved changes
    let formChanged = false;
    const formInputs = form.querySelectorAll('input, select, textarea');
    
    formInputs.forEach(input => {
        input.addEventListener('change', function() {
            formChanged = true;
        });
    });
    
    window.addEventListener('beforeunload', function(e) {
        if (formChanged) {
            e.preventDefault();
            e.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
        }
    });
    
    // Reset form changed flag on submit
    form.addEventListener('submit', function() {
        formChanged = false;
    });
});
</script>
{% endblock %}
