#!/usr/bin/env python3
"""
Simple Electronic Correspondence System - Port 8585
نظام المراسلات الإلكترونية البسيط - المنفذ 8585
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'correspondence-system-2024'

# Database setup
def init_db():
    """Initialize simple database"""
    os.makedirs('instance', exist_ok=True)
    conn = sqlite3.connect('instance/simple_correspondence.db')
    cursor = conn.cursor()
    
    # Simple users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'employee',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
    ''')
    
    # Simple messages table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            sender_id INTEGER,
            recipient TEXT,
            message_type TEXT DEFAULT 'internal',
            status TEXT DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sender_id) REFERENCES users (id)
        )
    ''')
    
    # Insert default users
    users = [
        ('المدير العام', 'admin', generate_password_hash('admin123'), 'admin'),
        ('مدير القسم', 'manager', generate_password_hash('manager123'), 'manager'),
        ('السكرتير', 'secretary', generate_password_hash('secretary123'), 'secretary'),
        ('موظف تجريبي', 'user', generate_password_hash('user123'), 'employee')
    ]
    
    cursor.executemany(
        'INSERT OR IGNORE INTO users (name, username, password_hash, role) VALUES (?, ?, ?, ?)',
        users
    )
    
    conn.commit()
    conn.close()

# Authentication functions
def get_user(username):
    """Get user by username"""
    conn = sqlite3.connect('instance/simple_correspondence.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
    user = cursor.fetchone()
    conn.close()
    return user

def verify_password(stored_password, provided_password):
    """Verify password"""
    return check_password_hash(stored_password, provided_password)

# Routes
@app.route('/')
def home():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return dashboard()

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = get_user(username)
        if user and verify_password(user[3], password):  # user[3] is password_hash
            session['user_id'] = user[0]
            session['username'] = user[2]
            session['name'] = user[1]
            session['role'] = user[4]
            flash(f'مرحباً {user[1]}! تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - نظام المراسلات الإلكترونية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .login-container {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                overflow: hidden;
                max-width: 500px;
                width: 100%;
            }
            .login-header {
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                padding: 40px 30px;
                text-align: center;
            }
            .login-body {
                padding: 40px;
            }
            .form-control {
                border-radius: 10px;
                border: 2px solid #e9ecef;
                padding: 15px;
                margin-bottom: 20px;
                font-size: 16px;
            }
            .btn-login {
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
                width: 100%;
                color: white;
                font-size: 16px;
            }
            .demo-info {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin-top: 25px;
                border-left: 4px solid #28a745;
            }
            .port-badge {
                background: #17a2b8;
                color: white;
                padding: 5px 15px;
                border-radius: 15px;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="login-container">
                        <div class="login-header">
                            <h2><i class="fas fa-envelope me-3"></i>تسجيل الدخول</h2>
                            <p class="mb-2">نظام المراسلات الإلكترونية</p>
                            <span class="port-badge">المنفذ: 8585</span>
                        </div>
                        <div class="login-body">
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">اسم المستخدم</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" name="username" placeholder="أدخل اسم المستخدم" required>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <label class="form-label fw-bold">كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" name="password" placeholder="أدخل كلمة المرور" required>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول
                                </button>
                            </form>
                            
                            <div class="demo-info">
                                <h6><i class="fas fa-info-circle text-success me-2"></i>بيانات تجريبية</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <p class="mb-1"><strong>مدير:</strong></p>
                                        <small>admin / admin123</small>
                                    </div>
                                    <div class="col-6">
                                        <p class="mb-1"><strong>موظف:</strong></p>
                                        <small>user / user123</small>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <p class="mb-1"><strong>مدير قسم:</strong></p>
                                        <small>manager / manager123</small>
                                    </div>
                                    <div class="col-6">
                                        <p class="mb-1"><strong>سكرتير:</strong></p>
                                        <small>secretary / secretary123</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''')

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    username = session.get('username', 'مستخدم')
    name = session.get('name', 'مستخدم')
    role = session.get('role', 'employee')
    
    # Get role display name
    role_names = {
        'admin': 'مدير النظام',
        'manager': 'مدير قسم',
        'secretary': 'سكرتير',
        'employee': 'موظف'
    }
    role_display = role_names.get(role, 'موظف')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم - نظام المراسلات الإلكترونية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: #f8f9fa;
            }
            .navbar { 
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .hero { 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                color: white; 
                padding: 60px 0; 
            }
            .feature-card { 
                transition: transform 0.3s;
                border: none;
                border-radius: 15px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
            .feature-card:hover { 
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            }
            .stats-card {
                background: white;
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
            .role-badge {
                background: #28a745;
                color: white;
                padding: 5px 15px;
                border-radius: 15px;
                font-size: 14px;
            }
            .port-info {
                background: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 10px;
                margin: 10px 0;
            }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-envelope me-2"></i>
                    نظام المراسلات الإلكترونية
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>{{ name }}
                        <span class="role-badge ms-2">{{ role_display }}</span>
                    </span>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="hero text-center">
            <div class="container">
                <h1 class="display-5 mb-4">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    لوحة التحكم
                </h1>
                <p class="lead">مرحباً {{ name }}، أهلاً بك في نظام المراسلات الإلكترونية</p>
                
                <div class="port-info d-inline-block">
                    <i class="fas fa-server me-2"></i>المنفذ: 8585 | النظام يعمل بنجاح
                </div>
                
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="row justify-content-center mt-4">
                            <div class="col-md-6">
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                {% endwith %}
            </div>
        </section>

        <!-- Statistics -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                            <h3>25</h3>
                            <p class="text-muted">إجمالي الرسائل</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-inbox fa-3x text-success mb-3"></i>
                            <h3>8</h3>
                            <p class="text-muted">رسائل واردة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-paper-plane fa-3x text-warning mb-3"></i>
                            <h3>12</h3>
                            <p class="text-muted">رسائل صادرة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-archive fa-3x text-info mb-3"></i>
                            <h3>5</h3>
                            <p class="text-muted">رسائل مؤرشفة</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="py-5 bg-light">
            <div class="container">
                <h2 class="text-center mb-5">الميزات المتاحة</h2>
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                                <h5>إدارة الرسائل</h5>
                                <p>إنشاء وتعديل الرسائل الواردة والصادرة والداخلية</p>
                                <a href="/messages" class="btn btn-primary">دخول</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-signature fa-3x text-success mb-3"></i>
                                <h5>التوقيع الإلكتروني</h5>
                                <p>إنشاء وإدارة التوقيعات الرقمية والصور</p>
                                <a href="/signature" class="btn btn-success">دخول</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-archive fa-3x text-info mb-3"></i>
                                <h5>نظام الأرشيف</h5>
                                <p>أرشفة منظمة للرسائل حسب السنة والشهر</p>
                                <a href="/archive" class="btn btn-info">دخول</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if role == 'admin' %}
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-users fa-3x text-danger mb-3"></i>
                                <h5>إدارة المستخدمين</h5>
                                <p>إضافة وتعديل وحذف المستخدمين</p>
                                <a href="/admin/users" class="btn btn-danger">دخول</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-cog fa-3x text-secondary mb-3"></i>
                                <h5>إعدادات النظام</h5>
                                <p>إدارة إعدادات النظام والصلاحيات</p>
                                <a href="/admin/settings" class="btn btn-secondary">دخول</a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </section>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''', name=name, role_display=role_display, role=role)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/test')
def test():
    return jsonify({
        'status': 'success',
        'message': 'النظام البسيط يعمل بنجاح على المنفذ 8585',
        'port': 8585,
        'features': [
            'تسجيل الدخول',
            'لوحة التحكم',
            'إدارة المستخدمين',
            'نظام الأدوار'
        ],
        'users': [
            {'username': 'admin', 'role': 'admin'},
            {'username': 'manager', 'role': 'manager'},
            {'username': 'secretary', 'role': 'secretary'},
            {'username': 'user', 'role': 'employee'}
        ]
    })

# Placeholder routes for features
@app.route('/messages')
def messages():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return "<h1>قريباً - إدارة الرسائل</h1><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/signature')
def signature():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return "<h1>قريباً - التوقيع الإلكتروني</h1><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/archive')
def archive():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return "<h1>قريباً - نظام الأرشيف</h1><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/admin/users')
def admin_users():
    if 'user_id' not in session or session.get('role') != 'admin':
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    return "<h1>قريباً - إدارة المستخدمين</h1><a href='/dashboard'>العودة للوحة التحكم</a>"

if __name__ == '__main__':
    print("🚀 تشغيل النظام البسيط للمراسلات الإلكترونية")
    print("=" * 60)
    print("🌐 الرابط الرئيسي: http://localhost:8585")
    print("🔐 صفحة الدخول: http://localhost:8585/login")
    print("🧪 اختبار النظام: http://localhost:8585/test")
    print("=" * 60)
    print("🔑 بيانات الدخول:")
    print("   👤 admin / admin123 (مدير النظام)")
    print("   👔 manager / manager123 (مدير قسم)")
    print("   📝 secretary / secretary123 (سكرتير)")
    print("   👤 user / user123 (موظف)")
    print("=" * 60)
    print("⚡ المنفذ: 8585")
    print("🛑 لإيقاف الخادم: Ctrl+C")
    print("=" * 60)
    
    # Initialize database
    init_db()
    
    # Run app
    app.run(host='127.0.0.1', port=8585, debug=True)
