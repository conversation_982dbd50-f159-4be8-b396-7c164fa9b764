{% extends "base.html" %}

{% block title %}رسالة جديدة - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus me-2"></i>
                رسالة جديدة
            </h1>
            <a href="{{ url_for('messages.list_messages') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="message_type" class="form-label">نوع الرسالة <span class="text-danger">*</span></label>
                            <select class="form-select" id="message_type" name="message_type" required>
                                <option value="">اختر نوع الرسالة</option>
                                <option value="incoming">واردة</option>
                                <option value="outgoing">صادرة</option>
                                <option value="internal">داخلية</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار نوع الرسالة
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="registration_number" class="form-label">رقم التسجيل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="registration_number" name="registration_number" required>
                            <div class="invalid-feedback">
                                يرجى إدخال رقم التسجيل
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="destination" class="form-label">الجهة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="destination" name="destination" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم الجهة
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="message_date" class="form-label">تاريخ الرسالة <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="message_date" name="message_date" required>
                            <div class="invalid-feedback">
                                يرجى إدخال تاريخ الرسالة
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject" class="form-label">الموضوع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="subject" name="subject" required>
                        <div class="invalid-feedback">
                            يرجى إدخال موضوع الرسالة
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">المحتوى</label>
                        <textarea class="form-control" id="content" name="content" rows="5" placeholder="محتوى الرسالة (اختياري)"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">القسم</label>
                            <input type="text" class="form-control" id="department" name="department" placeholder="اسم القسم (اختياري)">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="normal">عادي</option>
                                <option value="high">مهم</option>
                                <option value="urgent">عاجل</option>
                            </select>
                        </div>
                    </div>

                    <!-- Internal Message Fields -->
                    <div id="internal_fields" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>المراسلة الداخلية:</strong> هذه المراسلة ستكون بين الأقسام داخل المؤسسة
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="from_department" class="form-label">من القسم</label>
                                <select class="form-select" id="from_department" name="from_department">
                                    <option value="">اختر القسم المرسل</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.name }}">{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="to_department" class="form-label">إلى القسم</label>
                                <select class="form-select" id="to_department" name="to_department">
                                    <option value="">اختر القسم المستقبل</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.name }}">{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="assigned_to" class="form-label">مكلف بالرد</label>
                                <select class="form-select" id="assigned_to" name="assigned_to">
                                    <option value="">اختر المستخدم (اختياري)</option>
                                    {% for user in users %}
                                    <option value="{{ user.id }}">{{ user.name }} ({{ user.role.value }})</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="due_date" name="due_date">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_urgent" name="is_urgent">
                                    <label class="form-check-label" for="is_urgent">
                                        <i class="fas fa-exclamation-triangle text-danger me-1"></i>
                                        مراسلة عاجلة
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="requires_response" name="requires_response">
                                    <label class="form-check-label" for="requires_response">
                                        <i class="fas fa-reply text-primary me-1"></i>
                                        يتطلب رد
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Approval Section -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="requires_approval" name="requires_approval">
                                    <label class="form-check-label" for="requires_approval">
                                        <i class="fas fa-stamp text-warning me-1"></i>
                                        يتطلب اعتماد من المدير
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الرسائل التي تتطلب اعتماد لن تكون مرئية حتى يتم اعتمادها من قبل المدير أو السكرتير
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">المرفق</label>
                        <div class="file-upload-area" id="fileUploadArea">
                            <input type="file" name="attachment" id="attachmentInput"
                                   accept=".pdf,.doc,.docx,.rtf,.odt,.xls,.xlsx,.ods,.csv,.ppt,.pptx,.odp,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.svg,.webp,.txt,.zip,.rar,.7z,.tar,.gz,.mp3,.wav,.mp4,.avi,.mov,.wmv,.flv,.xml,.json,.html,.css,.js"
                                   style="display: none;">
                            <div id="uploadPrompt">
                                <i class="fas fa-cloud-upload-alt fa-2x mb-2 text-muted"></i>
                                <p class="mb-0">اسحب الملف هنا أو انقر للاختيار</p>
                                <small class="text-muted">الملفات المدعومة: PDF, Word, Excel, PowerPoint, صور, فيديو, صوت، أرشيف، وأكثر (حد أقصى 100 ميجابايت)</small>
                            </div>
                            <div id="filePreview" style="display: none;">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <i id="fileIcon" class="fas fa-file me-2 text-primary"></i>
                                        <div>
                                            <div id="fileName" class="fw-bold"></div>
                                            <small id="fileSize" class="text-muted"></small>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الرسالة
                        </button>
                        <a href="{{ url_for('messages.list_messages') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    var today = new Date().toISOString().split('T')[0];
    document.getElementById('message_date').value = today;

    // Auto-generate registration number based on type and date
    var messageTypeSelect = document.getElementById('message_type');
    var registrationInput = document.getElementById('registration_number');
    var internalFields = document.getElementById('internal_fields');
    var destinationField = document.getElementById('destination');
    var destinationLabel = destinationField.previousElementSibling;

    messageTypeSelect.addEventListener('change', function() {
        // Generate registration number
        if (this.value && !registrationInput.value) {
            var prefix = this.value === 'incoming' ? 'IN' :
                        this.value === 'outgoing' ? 'OUT' : 'INT';
            var date = new Date();
            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');
            var time = String(date.getHours()).padStart(2, '0') + String(date.getMinutes()).padStart(2, '0');

            registrationInput.value = `${prefix}-${year}${month}${day}-${time}`;
        }

        // Show/hide internal fields
        if (this.value === 'internal') {
            internalFields.style.display = 'block';
            destinationLabel.textContent = 'الجهة المستهدفة';
            destinationField.placeholder = 'الجهة أو القسم المستهدف';
            loadUsers(); // Load users for assignment
        } else {
            internalFields.style.display = 'none';
            destinationLabel.textContent = 'الجهة';
            destinationField.placeholder = '';
        }
    });

    // Load users for assignment dropdown (users are already loaded from server)
    function loadUsers() {
        // Users are already populated from the server-side template
        // No need to fetch via AJAX since we have the data
    }

    // Set default due date to 7 days from now for internal messages
    document.getElementById('requires_response').addEventListener('change', function() {
        if (this.checked) {
            var dueDate = new Date();
            dueDate.setDate(dueDate.getDate() + 7);
            document.getElementById('due_date').value = dueDate.toISOString().split('T')[0];
        }
    });

    // File upload functionality
    var fileUploadArea = document.getElementById('fileUploadArea');
    var attachmentInput = document.getElementById('attachmentInput');
    var uploadPrompt = document.getElementById('uploadPrompt');
    var filePreview = document.getElementById('filePreview');
    var fileName = document.getElementById('fileName');
    var fileSize = document.getElementById('fileSize');
    var fileIcon = document.getElementById('fileIcon');
    var removeFileBtn = document.getElementById('removeFile');

    // File icon mapping
    function getFileIcon(filename) {
        if (!filename) return 'fa-file';
        var extension = filename.split('.').pop().toLowerCase();
        var iconMap = {
            // Documents
            'pdf': 'fa-file-pdf',
            'doc': 'fa-file-word',
            'docx': 'fa-file-word',
            'rtf': 'fa-file-word',
            'odt': 'fa-file-word',
            // Spreadsheets
            'xls': 'fa-file-excel',
            'xlsx': 'fa-file-excel',
            'ods': 'fa-file-excel',
            'csv': 'fa-file-csv',
            // Presentations
            'ppt': 'fa-file-powerpoint',
            'pptx': 'fa-file-powerpoint',
            'odp': 'fa-file-powerpoint',
            // Images
            'jpg': 'fa-file-image',
            'jpeg': 'fa-file-image',
            'png': 'fa-file-image',
            'gif': 'fa-file-image',
            'bmp': 'fa-file-image',
            'tiff': 'fa-file-image',
            'svg': 'fa-file-image',
            'webp': 'fa-file-image',
            // Text files
            'txt': 'fa-file-alt',
            'xml': 'fa-file-code',
            'json': 'fa-file-code',
            'html': 'fa-file-code',
            'css': 'fa-file-code',
            'js': 'fa-file-code',
            // Archives
            'zip': 'fa-file-archive',
            'rar': 'fa-file-archive',
            '7z': 'fa-file-archive',
            'tar': 'fa-file-archive',
            'gz': 'fa-file-archive',
            // Audio
            'mp3': 'fa-file-audio',
            'wav': 'fa-file-audio',
            // Video
            'mp4': 'fa-file-video',
            'avi': 'fa-file-video',
            'mov': 'fa-file-video',
            'wmv': 'fa-file-video',
            'flv': 'fa-file-video'
        };
        return iconMap[extension] || 'fa-file';
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Handle file selection
    function handleFileSelect(file) {
        if (file) {
            // Check file size (100MB = 100 * 1024 * 1024 bytes)
            var maxSize = 100 * 1024 * 1024;
            if (file.size > maxSize) {
                alert('حجم الملف كبير جداً. الحد الأقصى المسموح هو 100 ميجابايت.');
                attachmentInput.value = '';
                return;
            }

            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileIcon.className = 'fas ' + getFileIcon(file.name) + ' me-2 text-primary';

            uploadPrompt.style.display = 'none';
            filePreview.style.display = 'block';
        }
    }

    // Click to select file
    fileUploadArea.addEventListener('click', function() {
        attachmentInput.click();
    });

    // File input change
    attachmentInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            handleFileSelect(this.files[0]);
        }
    });

    // Remove file
    removeFileBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        attachmentInput.value = '';
        uploadPrompt.style.display = 'block';
        filePreview.style.display = 'none';
    });

    // Drag and drop functionality
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        var files = e.dataTransfer.files;
        if (files.length > 0) {
            attachmentInput.files = files;
            handleFileSelect(files[0]);
        }
    });
});
</script>
{% endblock %}
