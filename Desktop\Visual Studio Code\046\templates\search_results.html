{% extends "base.html" %}

{% block title %}نتائج البحث - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-search me-2"></i>
            نتائج البحث
        </h1>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" name="q" class="form-control" placeholder="رقم التسجيل، الموضوع، أو الجهة" value="{{ request.args.get('q', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ request.args.get('date_from', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ request.args.get('date_to', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">النوع</label>
                        <select name="type" class="form-select">
                            <option value="">جميع الأنواع</option>
                            <option value="incoming" {% if request.args.get('type') == 'incoming' %}selected{% endif %}>واردة</option>
                            <option value="outgoing" {% if request.args.get('type') == 'outgoing' %}selected{% endif %}>صادرة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    النتائج
                    {% if query %}
                    <span class="badge bg-primary">{{ messages|length }} نتيجة للبحث عن "{{ query }}"</span>
                    {% endif %}
                </h5>
                {% if messages %}
                <div>
                    <button class="btn btn-sm btn-outline-success" onclick="exportResults()">
                        <i class="fas fa-download me-1"></i>تصدير
                    </button>
                </div>
                {% endif %}
            </div>
            <div class="card-body">
                {% if messages %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>النوع</th>
                                <th>الموضوع</th>
                                <th>الجهة</th>
                                <th>التاريخ</th>
                                <th>المرفقات</th>
                                <th>الأولوية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in messages %}
                            <tr>
                                <td>
                                    <strong>{{ message.registration_number }}</strong>
                                </td>
                                <td>
                                    {% if message.is_incoming() %}
                                        <span class="badge bg-success">واردة</span>
                                    {% else %}
                                        <span class="badge bg-info">صادرة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject[:50] }}{% if message.subject|length > 50 %}...{% endif %}
                                    </a>
                                </td>
                                <td>{{ message.destination[:30] }}{% if message.destination|length > 30 %}...{% endif %}</td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if message.attachment_name %}
                                        <i class="fas fa-paperclip text-success" title="{{ message.attachment_name }}"></i>
                                    {% else %}
                                        <i class="fas fa-minus text-muted"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if message.priority == 'urgent' %}
                                        <span class="badge bg-danger">عاجل</span>
                                    {% elif message.priority == 'high' %}
                                        <span class="badge bg-warning">مهم</span>
                                    {% else %}
                                        <span class="badge bg-secondary">عادي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if current_user.is_manager() or message.created_by == current_user.id %}
                                        <a href="{{ url_for('messages.edit_message', id=message.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('messages.download_pdf', id=message.id) }}" 
                                           class="btn btn-outline-success" title="تحميل PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-search fa-4x mb-3"></i>
                    <h4>لا توجد نتائج</h4>
                    {% if query %}
                    <p>لم يتم العثور على أي رسائل تطابق البحث عن "{{ query }}"</p>
                    {% else %}
                    <p>يرجى إدخال كلمات البحث أو تحديد معايير البحث</p>
                    {% endif %}
                    <a href="{{ url_for('messages.list_messages') }}" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>
                        عرض جميع الرسائل
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportResults() {
    // This would typically generate a report
    // For now, we'll just show an alert
    alert('سيتم إضافة وظيفة التصدير قريباً');
}
</script>
{% endblock %}
