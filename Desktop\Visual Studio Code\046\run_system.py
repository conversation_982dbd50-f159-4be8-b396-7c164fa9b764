#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل النظام المحسن
"""

import os
import sys
from app import create_app

def main():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل نظام المراسلات الإلكترونية")
    print("=" * 60)
    
    try:
        # إنشاء التطبيق
        app = create_app()
        
        # معلومات التشغيل
        print("✅ تم تحميل النظام بنجاح!")
        print("🌐 النظام متاح على:")
        print("   🔗 http://localhost:8585")
        print("   🔗 http://127.0.0.1:8585")
        print("=" * 60)
        print("🔐 معلومات تسجيل الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔐 كلمة المرور: admin123")
        print("=" * 60)
        print("⚡ المنفذ: 8585")
        print("🛑 لإيقاف الخادم: Ctrl+C")
        print("=" * 60)
        
        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=8585,
            debug=False,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        print("🔧 تشغيل أداة التشخيص...")
        os.system("python fix_system_issues.py")

if __name__ == "__main__":
    main()
