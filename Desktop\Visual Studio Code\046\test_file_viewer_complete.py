#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لنظام عرض الملفات المحدث
"""

import os
import sys
import requests
import subprocess
import time
import json
from pathlib import Path

def create_test_files():
    """إنشاء ملفات اختبار متنوعة"""
    print("📁 إنشاء ملفات اختبار...")
    
    test_files_dir = "test_files"
    os.makedirs(test_files_dir, exist_ok=True)
    
    # ملف نصي عربي
    with open(f"{test_files_dir}/arabic_document.txt", "w", encoding="utf-8") as f:
        f.write("""📄 وثيقة اختبار باللغة العربية

هذا ملف نصي لاختبار عرض المحتوى العربي في نظام المراسلات الإلكترونية.

المحتوى:
• نص عربي مع تشكيل: مَرْحَباً بِكُم فِي النِّظَام
• أرقام عربية: ١٢٣٤٥٦٧٨٩٠
• أرقام إنجليزية: 1234567890
• تاريخ: ٢٠٢٥/٠٦/٠٦
• رموز خاصة: !@#$%^&*()

الميزات المختبرة:
✓ عرض النص العربي بشكل صحيح
✓ دعم الترميز UTF-8
✓ عرض الرموز والأرقام
✓ التنسيق والتخطيط

تم إنشاء هذا الملف لاختبار نظام عرض الملفات في نظام المراسلات الإلكترونية.
النظام يدعم عرض أنواع مختلفة من الملفات بما في ذلك:
- الملفات النصية (TXT)
- ملفات PDF
- الصور (JPG, PNG, GIF, إلخ)
- ملفات Word (DOCX)
- ملفات Excel (XLSX)
- ملفات JSON و XML
- ملفات الصوت والفيديو

شكراً لاستخدام النظام!
""")
    
    # ملف JSON
    test_data = {
        "system_info": {
            "name": "نظام المراسلات الإلكترونية",
            "version": "1.0.0",
            "language": "العربية",
            "encoding": "UTF-8"
        },
        "features": [
            "إدارة الرسائل",
            "رفع الملفات",
            "عرض الملفات",
            "نظام الموافقات",
            "إدارة المستخدمين",
            "إدارة الأقسام",
            "إدارة الجهات",
            "الإعدادات المتقدمة"
        ],
        "file_types_supported": {
            "documents": ["PDF", "DOCX", "TXT", "RTF"],
            "images": ["JPG", "PNG", "GIF", "BMP", "SVG"],
            "audio": ["MP3", "WAV", "OGG"],
            "video": ["MP4", "AVI", "MOV", "WEBM"],
            "data": ["JSON", "XML", "CSV"],
            "code": ["HTML", "CSS", "JS", "PY"],
            "archives": ["ZIP", "RAR", "7Z"]
        },
        "statistics": {
            "total_users": 25,
            "total_messages": 150,
            "total_files": 89,
            "total_departments": 8
        }
    }
    
    with open(f"{test_files_dir}/system_data.json", "w", encoding="utf-8") as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    # ملف HTML
    with open(f"{test_files_dir}/test_page.html", "w", encoding="utf-8") as f:
        f.write("""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة اختبار HTML</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #0066cc, #004499);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 نظام المراسلات الإلكترونية</h1>
        <p>صفحة اختبار HTML لعرض الملفات</p>
    </div>
    
    <div class="content">
        <h2>مرحباً بكم في نظام عرض الملفات</h2>
        
        <div class="highlight">
            <strong>ملاحظة:</strong> هذه صفحة HTML تجريبية لاختبار عرض ملفات HTML في النظام.
        </div>
        
        <h3>الميزات المتاحة:</h3>
        <div class="feature-list">
            <div class="feature-item">
                <h4>📄 عرض المستندات</h4>
                <p>PDF, DOCX, TXT</p>
            </div>
            <div class="feature-item">
                <h4>🖼️ عرض الصور</h4>
                <p>JPG, PNG, GIF, SVG</p>
            </div>
            <div class="feature-item">
                <h4>🎵 تشغيل الصوت</h4>
                <p>MP3, WAV, OGG</p>
            </div>
            <div class="feature-item">
                <h4>🎬 تشغيل الفيديو</h4>
                <p>MP4, AVI, MOV</p>
            </div>
            <div class="feature-item">
                <h4>📊 عرض البيانات</h4>
                <p>JSON, XML, CSV</p>
            </div>
            <div class="feature-item">
                <h4>💻 عرض الكود</h4>
                <p>HTML, CSS, JS</p>
            </div>
        </div>
        
        <h3>معلومات تقنية:</h3>
        <ul>
            <li><strong>التاريخ:</strong> 2025-06-06</li>
            <li><strong>الإصدار:</strong> 1.0.0</li>
            <li><strong>اللغة:</strong> العربية</li>
            <li><strong>الترميز:</strong> UTF-8</li>
        </ul>
        
        <div class="highlight">
            <p>تم إنشاء هذا الملف لاختبار عرض ملفات HTML في نظام المراسلات الإلكترونية.</p>
        </div>
    </div>
    
    <script>
        console.log('صفحة HTML تم تحميلها بنجاح');
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM جاهز');
        });
    </script>
</body>
</html>""")
    
    # ملف CSS
    with open(f"{test_files_dir}/styles.css", "w", encoding="utf-8") as f:
        f.write("""/* ملف CSS لاختبار عرض الملفات */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* الحاوي الرئيسي */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    color: #0066cc;
    margin-bottom: 15px;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }

/* البطاقات */
.card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #0066cc;
    color: white;
}

.btn-primary:hover {
    background-color: #0052a3;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

/* الجداول */
.table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.table th,
.table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 16px;
}

.form-control:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.25);
}

/* التنبيهات */
.alert {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 15px;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: 0 15px;
}

/* الأدوات المساعدة */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .col-md-6,
    .col-md-4,
    .col-md-3 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .container {
        padding: 10px;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
}

/* تحسينات للطباعة */
@media print {
    .btn,
    .alert {
        display: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}""")
    
    print(f"✅ تم إنشاء ملفات الاختبار في مجلد {test_files_dir}")
    return test_files_dir

def test_file_viewer_functions():
    """اختبار دوال عارض الملفات"""
    print("\n🧪 اختبار دوال عارض الملفات...")
    
    try:
        from utils.simple_file_viewer import (
            get_file_info, can_view_in_browser, 
            get_file_icon_class, format_file_size
        )
        
        # اختبار تنسيق حجم الملف
        sizes = [0, 512, 1024, 1048576, 1073741824]
        expected = ["0 B", "512.0 B", "1.0 KB", "1.0 MB", "1.0 GB"]
        
        for size, exp in zip(sizes, expected):
            result = format_file_size(size)
            status = "✅" if result == exp else "❌"
            print(f"  {status} حجم {size} = {result} (متوقع: {exp})")
        
        # اختبار أنواع الملفات
        test_files = [
            ("document.pdf", True, "fa-file-pdf"),
            ("image.jpg", True, "fa-file-image"),
            ("video.mp4", True, "fa-file-video"),
            ("audio.mp3", True, "fa-file-audio"),
            ("data.json", True, "fa-file"),
            ("archive.zip", False, "fa-file-archive"),
            ("unknown.xyz", False, "fa-file")
        ]
        
        for filename, can_view_expected, icon_expected in test_files:
            can_view = can_view_in_browser(filename)
            icon = get_file_icon_class(filename)
            
            view_status = "✅" if can_view == can_view_expected else "❌"
            icon_status = "✅" if icon == icon_expected else "❌"
            
            print(f"  📄 {filename}:")
            print(f"    {view_status} قابل للعرض: {can_view}")
            print(f"    {icon_status} الأيقونة: {icon}")
        
        print("  ✅ اختبار دوال عارض الملفات نجح")
        return True
        
    except ImportError as e:
        print(f"  ❌ خطأ في الاستيراد: {str(e)}")
        return False
    except Exception as e:
        print(f"  ❌ خطأ في الاختبار: {str(e)}")
        return False

def test_system_integration():
    """اختبار تكامل النظام"""
    print("\n🌐 اختبار تكامل النظام...")
    
    # تشغيل الخادم في الخلفية
    print("🚀 تشغيل الخادم...")
    try:
        server_process = subprocess.Popen([
            'python', 'debug_start.py'
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # انتظار تشغيل الخادم
        time.sleep(5)
        
        base_url = "http://localhost:8585"
        
        # إنشاء جلسة
        session = requests.Session()
        
        # تسجيل الدخول
        print("🔐 تسجيل الدخول...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        # اختبار الصفحة الرئيسية
        print("🏠 اختبار الصفحة الرئيسية...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل")
        else:
            print(f"❌ مشكلة في الصفحة الرئيسية: {response.status_code}")
        
        # اختبار صفحة الرسائل
        print("📧 اختبار صفحة الرسائل...")
        response = session.get(f"{base_url}/messages")
        if response.status_code == 200:
            print("✅ صفحة الرسائل تعمل")
        else:
            print(f"❌ مشكلة في صفحة الرسائل: {response.status_code}")
        
        # اختبار صفحة قائمة الملفات
        print("📁 اختبار صفحة قائمة الملفات...")
        response = session.get(f"{base_url}/messages/files")
        if response.status_code == 200:
            print("✅ صفحة قائمة الملفات تعمل")
            
            content = response.text
            if 'قائمة الملفات' in content:
                print("✅ محتوى الصفحة صحيح")
            if 'عرض الملف' in content:
                print("✅ أزرار عرض الملفات موجودة")
        else:
            print(f"❌ مشكلة في صفحة قائمة الملفات: {response.status_code}")
        
        print("✅ تم اكتمال اختبار تكامل النظام")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False
    
    finally:
        # إيقاف الخادم
        try:
            server_process.terminate()
            server_process.wait(timeout=5)
        except:
            try:
                server_process.kill()
            except:
                pass

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار شامل لنظام عرض الملفات المحدث")
    print("=" * 60)
    
    # إنشاء ملفات الاختبار
    test_files_dir = create_test_files()
    
    # اختبار دوال عارض الملفات
    viewer_test = test_file_viewer_functions()
    
    # اختبار تكامل النظام
    integration_test = test_system_integration()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    
    if viewer_test:
        print("✅ دوال عارض الملفات تعمل بشكل صحيح")
    else:
        print("❌ مشكلة في دوال عارض الملفات")
    
    if integration_test:
        print("✅ تكامل النظام يعمل بشكل صحيح")
    else:
        print("❌ مشكلة في تكامل النظام")
    
    if viewer_test and integration_test:
        print("\n🎉 نظام عرض الملفات يعمل بشكل مثالي!")
        print("\n🌟 الميزات المتاحة:")
        print("  📄 عرض ملفات PDF, TXT, HTML, JSON, CSS")
        print("  🖼️ عرض الصور JPG, PNG, GIF, SVG")
        print("  🎵 تشغيل ملفات الصوت MP3, WAV")
        print("  🎬 تشغيل ملفات الفيديو MP4, WEBM")
        print("  📊 أيقونات مناسبة لكل نوع ملف")
        print("  💾 تحميل جميع أنواع الملفات")
        print("  🔍 معلومات تفصيلية عن الملفات")
        
        print(f"\n📁 ملفات الاختبار متوفرة في: {test_files_dir}")
        print("🔗 النظام متاح على: http://localhost:8585")
        
        return True
    else:
        print("\n❌ هناك مشاكل في نظام عرض الملفات")
        print("\n🔧 تحقق من:")
        print("  • تثبيت المتطلبات: pip install Pillow python-docx PyPDF2")
        print("  • وجود ملفات عارض الملفات")
        print("  • صحة مسارات النظام")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
