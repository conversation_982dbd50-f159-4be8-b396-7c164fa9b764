#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء جدول الإعدادات وإضافة الإعدادات الافتراضية
"""

from app import create_app
from models import db, SystemSettings, User
from datetime import datetime

def create_settings_table():
    """إنشاء جدول الإعدادات وإضافة الإعدادات الافتراضية"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 إنشاء جدول الإعدادات...")
            
            # إنشاء الجداول
            db.create_all()
            
            print("✅ تم إنشاء جدول الإعدادات بنجاح")
            
            # التحقق من وجود مستخدم admin
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ لم يتم العثور على مستخدم admin")
                return False
            
            # إضافة الإعدادات الافتراضية
            print("\n🎨 إضافة إعدادات الألوان الافتراضية...")
            
            color_settings = [
                ('primary_color', '#0d6efd', 'color', 'colors', 'اللون الأساسي', 'اللون الأساسي للنظام'),
                ('secondary_color', '#6c757d', 'color', 'colors', 'اللون الثانوي', 'اللون الثانوي للنظام'),
                ('success_color', '#198754', 'color', 'colors', 'لون النجاح', 'لون رسائل النجاح'),
                ('danger_color', '#dc3545', 'color', 'colors', 'لون الخطر', 'لون رسائل الخطر'),
                ('warning_color', '#ffc107', 'color', 'colors', 'لون التحذير', 'لون رسائل التحذير'),
                ('info_color', '#0dcaf0', 'color', 'colors', 'لون المعلومات', 'لون رسائل المعلومات'),
                ('sidebar_bg', '#343a40', 'color', 'colors', 'خلفية الشريط الجانبي', 'لون خلفية الشريط الجانبي'),
                ('navbar_bg', '#0d6efd', 'color', 'colors', 'خلفية شريط التنقل', 'لون خلفية شريط التنقل'),
                ('card_bg', '#ffffff', 'color', 'colors', 'خلفية البطاقات', 'لون خلفية البطاقات'),
                ('text_color', '#212529', 'color', 'colors', 'لون النص', 'لون النص الأساسي'),
            ]
            
            for key, value, setting_type, category, display_name, description in color_settings:
                existing = SystemSettings.query.filter_by(setting_key=key).first()
                if not existing:
                    setting = SystemSettings(
                        setting_key=key,
                        setting_value=value,
                        setting_type=setting_type,
                        category=category,
                        display_name=display_name,
                        description=description,
                        updated_by=admin_user.id
                    )
                    db.session.add(setting)
                    print(f"  ✅ تم إضافة: {display_name}")
                else:
                    print(f"  ⚠️ موجود مسبقاً: {display_name}")
            
            print("\n📱 إضافة إعدادات العرض الافتراضية...")
            
            display_settings = [
                ('show_days_only', 'true', 'boolean', 'display', 'عرض الأيام كأرقام فقط', 'عرض الأيام كأرقام بدلاً من "منذ X أيام"'),
                ('date_format', 'dd/mm/yyyy', 'text', 'display', 'تنسيق التاريخ', 'تنسيق عرض التاريخ'),
                ('time_format', '24h', 'text', 'display', 'تنسيق الوقت', 'تنسيق عرض الوقت (12h أو 24h)'),
                ('items_per_page', '20', 'number', 'display', 'عدد العناصر في الصفحة', 'عدد العناصر المعروضة في كل صفحة'),
                ('show_sidebar', 'true', 'boolean', 'display', 'إظهار الشريط الجانبي', 'إظهار الشريط الجانبي بشكل افتراضي'),
                ('compact_mode', 'false', 'boolean', 'display', 'الوضع المضغوط', 'تقليل المسافات لعرض المزيد من المحتوى'),
            ]
            
            for key, value, setting_type, category, display_name, description in display_settings:
                existing = SystemSettings.query.filter_by(setting_key=key).first()
                if not existing:
                    setting = SystemSettings(
                        setting_key=key,
                        setting_value=value,
                        setting_type=setting_type,
                        category=category,
                        display_name=display_name,
                        description=description,
                        updated_by=admin_user.id
                    )
                    db.session.add(setting)
                    print(f"  ✅ تم إضافة: {display_name}")
                else:
                    print(f"  ⚠️ موجود مسبقاً: {display_name}")
            
            print("\n🏢 إضافة الإعدادات العامة الافتراضية...")
            
            general_settings = [
                ('system_name', 'نظام المراسلات الإلكترونية', 'text', 'general', 'اسم النظام', 'اسم النظام المعروض'),
                ('system_description', 'نظام إدارة المراسلات الإلكترونية المتطور', 'text', 'general', 'وصف النظام', 'وصف مختصر عن النظام'),
                ('contact_email', '<EMAIL>', 'text', 'general', 'البريد الإلكتروني', 'البريد الإلكتروني للتواصل'),
                ('contact_phone', '+966 11 123 4567', 'text', 'general', 'رقم الهاتف', 'رقم الهاتف للتواصل'),
                ('address', 'المملكة العربية السعودية', 'text', 'general', 'العنوان', 'عنوان المؤسسة'),
                ('logo_url', '/static/images/logo.png', 'text', 'general', 'رابط الشعار', 'رابط شعار النظام'),
            ]
            
            for key, value, setting_type, category, display_name, description in general_settings:
                existing = SystemSettings.query.filter_by(setting_key=key).first()
                if not existing:
                    setting = SystemSettings(
                        setting_key=key,
                        setting_value=value,
                        setting_type=setting_type,
                        category=category,
                        display_name=display_name,
                        description=description,
                        updated_by=admin_user.id
                    )
                    db.session.add(setting)
                    print(f"  ✅ تم إضافة: {display_name}")
                else:
                    print(f"  ⚠️ موجود مسبقاً: {display_name}")
            
            db.session.commit()
            
            # عرض الإحصائيات
            total_settings = SystemSettings.query.count()
            color_count = SystemSettings.query.filter_by(category='colors').count()
            display_count = SystemSettings.query.filter_by(category='display').count()
            general_count = SystemSettings.query.filter_by(category='general').count()
            
            print(f"\n📊 إحصائيات الإعدادات:")
            print(f"  📋 إجمالي الإعدادات: {total_settings}")
            print(f"  🎨 إعدادات الألوان: {color_count}")
            print(f"  📱 إعدادات العرض: {display_count}")
            print(f"  🏢 الإعدادات العامة: {general_count}")
            
            print("\n🎉 تم إنشاء قسم الإعدادات بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الإعدادات: {str(e)}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("⚙️ إنشاء قسم الإعدادات")
    print("=" * 50)
    
    success = create_settings_table()
    
    if success:
        print("\n✅ تم إنشاء قسم الإعدادات بنجاح!")
        print("\n🌐 يمكنك الآن الوصول إلى:")
        print("  ⚙️ الصفحة الرئيسية: http://localhost:8585/settings")
        print("  🎨 إعدادات الألوان: http://localhost:8585/settings/colors")
        print("  📱 إعدادات العرض: http://localhost:8585/settings/display")
        print("  🏢 الإعدادات العامة: http://localhost:8585/settings/general")
        print("  🔌 API الألوان: http://localhost:8585/settings/api/colors")
        print("  📊 API العرض: http://localhost:8585/settings/api/display")
        print("\n💡 الميزات المتاحة:")
        print("  🎨 تخصيص ألوان النظام بالكامل")
        print("  📱 تحكم في طريقة عرض التاريخ والوقت")
        print("  📊 عرض الأيام كأرقام فقط في الرسائل الحديثة")
        print("  🏢 إدارة معلومات النظام العامة")
        print("  👁️ معاينة مباشرة للتغييرات")
        print("  🔄 إعادة تعيين للقيم الافتراضية")
        print("  📤 تصدير واستيراد الإعدادات")
        print("\n🎯 الإعدادات الجديدة:")
        print("  📊 عرض الأيام كأرقام فقط: مفعل")
        print("  🎨 الألوان: الألوان الافتراضية")
        print("  📱 العرض: 20 عنصر في الصفحة")
        print("  🏢 النظام: نظام المراسلات الإلكترونية")
    else:
        print("\n❌ فشل في إنشاء قسم الإعدادات!")
        print("تحقق من:")
        print("  • اتصال قاعدة البيانات")
        print("  • وجود مستخدم admin")
        print("  • صحة النماذج")

def test_settings_functionality():
    """اختبار وظائف الإعدادات"""
    app = create_app()

    with app.app_context():
        try:
            print("\n🧪 اختبار وظائف الإعدادات...")

            # اختبار الحصول على إعداد
            show_days_only = SystemSettings.get_setting('show_days_only', 'false')
            print(f"  📊 عرض الأيام كأرقام فقط: {show_days_only}")

            # اختبار تعيين إعداد
            SystemSettings.set_setting('test_setting', 'test_value', 1)
            test_value = SystemSettings.get_setting('test_setting')
            print(f"  🧪 اختبار تعيين الإعداد: {test_value}")

            # اختبار إعدادات الألوان
            colors = SystemSettings.get_color_settings()
            print(f"  🎨 عدد الألوان المحملة: {len(colors)}")

            # اختبار إعدادات العرض
            display = SystemSettings.get_display_settings()
            print(f"  📱 عدد إعدادات العرض: {len(display)}")

            print("  ✅ جميع الاختبارات نجحت!")
            return True

        except Exception as e:
            print(f"  ❌ فشل في الاختبار: {str(e)}")
            return False

if __name__ == "__main__":
    print("⚙️ إنشاء قسم الإعدادات")
    print("=" * 50)

    success = create_settings_table()

    if success:
        # تشغيل الاختبارات
        test_success = test_settings_functionality()

        if test_success:
            print("\n✅ تم إنشاء قسم الإعدادات بنجاح!")
            print("\n🌐 يمكنك الآن الوصول إلى:")
            print("  ⚙️ الصفحة الرئيسية: http://localhost:8585/settings")
            print("  🎨 إعدادات الألوان: http://localhost:8585/settings/colors")
            print("  📱 إعدادات العرض: http://localhost:8585/settings/display")
            print("  🏢 الإعدادات العامة: http://localhost:8585/settings/general")
            print("  🔌 API الألوان: http://localhost:8585/settings/api/colors")
            print("  📊 API العرض: http://localhost:8585/settings/api/display")
            print("\n💡 الميزات المتاحة:")
            print("  🎨 تخصيص ألوان النظام بالكامل")
            print("  📱 تحكم في طريقة عرض التاريخ والوقت")
            print("  📊 عرض الأيام كأرقام فقط في الرسائل الحديثة")
            print("  🏢 إدارة معلومات النظام العامة")
            print("  👁️ معاينة مباشرة للتغييرات")
            print("  🔄 إعادة تعيين للقيم الافتراضية")
            print("  📤 تصدير واستيراد الإعدادات")
            print("\n🎯 الإعدادات الجديدة:")
            print("  📊 عرض الأيام كأرقام فقط: مفعل")
            print("  🎨 الألوان: الألوان الافتراضية")
            print("  📱 العرض: 20 عنصر في الصفحة")
            print("  🏢 النظام: نظام المراسلات الإلكترونية")
        else:
            print("\n⚠️ تم إنشاء الإعدادات لكن الاختبارات فشلت!")
    else:
        print("\n❌ فشل في إنشاء قسم الإعدادات!")
        print("تحقق من:")
        print("  • اتصال قاعدة البيانات")
        print("  • وجود مستخدم admin")
        print("  • صحة النماذج")
