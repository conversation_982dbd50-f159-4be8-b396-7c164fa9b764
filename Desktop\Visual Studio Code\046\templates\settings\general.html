{% extends "base.html" %}

{% block title %}الإعدادات العامة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-cogs me-2"></i>
                    الإعدادات العامة
                </h2>
                <div>
                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للإعدادات
                    </a>
                </div>
            </div>

            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <!-- معلومات النظام -->
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>معلومات النظام
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="system_name" class="form-label">اسم النظام</label>
                                        <input type="text" class="form-control" id="system_name" name="system_name" 
                                               value="{{ general.system_name or 'نظام المراسلات الإلكترونية' }}" 
                                               placeholder="اسم النظام">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="logo_url" class="form-label">رابط الشعار</label>
                                        <input type="url" class="form-control" id="logo_url" name="logo_url" 
                                               value="{{ general.logo_url or '' }}" 
                                               placeholder="https://example.com/logo.png">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="system_description" class="form-label">وصف النظام</label>
                                    <textarea class="form-control" id="system_description" name="system_description" 
                                              rows="3" placeholder="وصف مختصر عن النظام">{{ general.system_description or 'نظام إدارة المراسلات الإلكترونية المتطور' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-phone me-2"></i>معلومات الاتصال
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                               value="{{ general.contact_email or '' }}" 
                                               placeholder="<EMAIL>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                                               value="{{ general.contact_phone or '' }}" 
                                               placeholder="+966 11 123 4567">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" 
                                              rows="3" placeholder="العنوان الكامل للمؤسسة">{{ general.address or '' }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المعاينة والأدوات -->
                    <div class="col-lg-4">
                        <!-- معاينة المعلومات -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-eye me-2"></i>معاينة المعلومات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div id="logo-preview" class="mb-3">
                                        <img id="logo-img" src="{{ general.logo_url or '/static/images/default-logo.png' }}" 
                                             alt="شعار النظام" class="img-fluid" style="max-height: 80px;" 
                                             onerror="this.src='/static/images/default-logo.png'">
                                    </div>
                                    <h5 id="system-name-preview">{{ general.system_name or 'نظام المراسلات الإلكترونية' }}</h5>
                                    <p class="text-muted" id="system-desc-preview">{{ general.system_description or 'نظام إدارة المراسلات الإلكترونية المتطور' }}</p>
                                </div>
                                <hr>
                                <div class="contact-info">
                                    <div class="mb-2" id="email-preview">
                                        <i class="fas fa-envelope text-primary me-2"></i>
                                        <span>{{ general.contact_email or '<EMAIL>' }}</span>
                                    </div>
                                    <div class="mb-2" id="phone-preview">
                                        <i class="fas fa-phone text-success me-2"></i>
                                        <span>{{ general.contact_phone or '+966 11 123 4567' }}</span>
                                    </div>
                                    <div id="address-preview">
                                        <i class="fas fa-map-marker-alt text-info me-2"></i>
                                        <span>{{ general.address or 'العنوان غير محدد' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات النظام -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-server me-2"></i>معلومات النظام
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>إصدار النظام:</strong> 
                                    <span class="badge bg-primary">v1.0.0</span>
                                </div>
                                <div class="mb-2">
                                    <strong>تاريخ التحديث:</strong> 
                                    <span class="text-muted">{{ moment().strftime('%Y-%m-%d') if moment else '2025-06-06' }}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>حالة النظام:</strong> 
                                    <span class="badge bg-success">نشط</span>
                                </div>
                                <div class="mb-2">
                                    <strong>قاعدة البيانات:</strong> 
                                    <span class="text-muted">SQLite</span>
                                </div>
                                <div>
                                    <strong>الخادم:</strong> 
                                    <span class="text-muted">Flask Development Server</span>
                                </div>
                            </div>
                        </div>

                        <!-- حفظ الإعدادات -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-1"></i>حفظ الإعدادات العامة
                                    </button>
                                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث المعاينة عند تغيير الحقول
    const systemNameInput = document.getElementById('system_name');
    const systemDescInput = document.getElementById('system_description');
    const logoUrlInput = document.getElementById('logo_url');
    const contactEmailInput = document.getElementById('contact_email');
    const contactPhoneInput = document.getElementById('contact_phone');
    const addressInput = document.getElementById('address');
    
    // إضافة مستمعي الأحداث
    systemNameInput.addEventListener('input', updateSystemNamePreview);
    systemDescInput.addEventListener('input', updateSystemDescPreview);
    logoUrlInput.addEventListener('input', updateLogoPreview);
    contactEmailInput.addEventListener('input', updateEmailPreview);
    contactPhoneInput.addEventListener('input', updatePhonePreview);
    addressInput.addEventListener('input', updateAddressPreview);
});

function updateSystemNamePreview() {
    const value = document.getElementById('system_name').value || 'نظام المراسلات الإلكترونية';
    document.getElementById('system-name-preview').textContent = value;
}

function updateSystemDescPreview() {
    const value = document.getElementById('system_description').value || 'نظام إدارة المراسلات الإلكترونية المتطور';
    document.getElementById('system-desc-preview').textContent = value;
}

function updateLogoPreview() {
    const value = document.getElementById('logo_url').value || '/static/images/default-logo.png';
    const logoImg = document.getElementById('logo-img');
    logoImg.src = value;
    logoImg.onerror = function() {
        this.src = '/static/images/default-logo.png';
    };
}

function updateEmailPreview() {
    const value = document.getElementById('contact_email').value || '<EMAIL>';
    document.getElementById('email-preview').innerHTML = 
        '<i class="fas fa-envelope text-primary me-2"></i><span>' + value + '</span>';
}

function updatePhonePreview() {
    const value = document.getElementById('contact_phone').value || '+966 11 123 4567';
    document.getElementById('phone-preview').innerHTML = 
        '<i class="fas fa-phone text-success me-2"></i><span>' + value + '</span>';
}

function updateAddressPreview() {
    const value = document.getElementById('address').value || 'العنوان غير محدد';
    document.getElementById('address-preview').innerHTML = 
        '<i class="fas fa-map-marker-alt text-info me-2"></i><span>' + value + '</span>';
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.contact-info {
    font-size: 0.9rem;
}

#logo-img {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    background: white;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
