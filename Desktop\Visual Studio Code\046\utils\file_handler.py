import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app

def allowed_file(filename):
    """Check if file extension is allowed"""
    if not filename or '.' not in filename:
        return False

    try:
        extension = filename.rsplit('.', 1)[1].lower()
        return extension in current_app.config['ALLOWED_EXTENSIONS']
    except (IndexError, AttributeError):
        return False

def save_uploaded_file(file, registration_number):
    """Save uploaded file and return path and original name"""
    if not file or not file.filename:
        return None, None

    if not allowed_file(file.filename):
        return None, None

    try:
        # Create secure filename
        original_filename = secure_filename(file.filename)

        # Double check if file has extension after securing
        if '.' not in original_filename:
            return None, None

        file_extension = original_filename.rsplit('.', 1)[1].lower()

        # Validate extension again
        if not file_extension or file_extension not in current_app.config['ALLOWED_EXTENSIONS']:
            return None, None

        # Create unique filename with registration number
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_filename = f"{registration_number}_{timestamp}_{uuid.uuid4().hex[:8]}.{file_extension}"

        # Create year-based directory structure
        year = datetime.now().year
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], str(year))
        os.makedirs(upload_dir, exist_ok=True)

        # Save file
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # Return relative path for database storage
        relative_path = os.path.join(str(year), unique_filename)
        return relative_path, original_filename

    except Exception as e:
        # Log the error if needed
        print(f"Error saving file: {e}")
        return None, None

def get_file_size(file_path):
    """Get file size in human readable format"""
    try:
        size = os.path.getsize(file_path)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    except OSError:
        return "Unknown"

def delete_file(file_path):
    """Safely delete a file"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
    except OSError:
        pass
    return False

def get_file_icon(filename):
    """Get appropriate icon class for file type"""
    if not filename:
        return 'fa-file'
    
    extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    icon_map = {
        # Documents
        'pdf': 'fa-file-pdf',
        'doc': 'fa-file-word',
        'docx': 'fa-file-word',
        'rtf': 'fa-file-word',
        'odt': 'fa-file-word',
        # Spreadsheets
        'xls': 'fa-file-excel',
        'xlsx': 'fa-file-excel',
        'ods': 'fa-file-excel',
        'csv': 'fa-file-csv',
        # Presentations
        'ppt': 'fa-file-powerpoint',
        'pptx': 'fa-file-powerpoint',
        'odp': 'fa-file-powerpoint',
        # Images
        'jpg': 'fa-file-image',
        'jpeg': 'fa-file-image',
        'png': 'fa-file-image',
        'gif': 'fa-file-image',
        'bmp': 'fa-file-image',
        'tiff': 'fa-file-image',
        'svg': 'fa-file-image',
        'webp': 'fa-file-image',
        # Text files
        'txt': 'fa-file-alt',
        'xml': 'fa-file-code',
        'json': 'fa-file-code',
        'html': 'fa-file-code',
        'css': 'fa-file-code',
        'js': 'fa-file-code',
        # Archives
        'zip': 'fa-file-archive',
        'rar': 'fa-file-archive',
        '7z': 'fa-file-archive',
        'tar': 'fa-file-archive',
        'gz': 'fa-file-archive',
        # Audio
        'mp3': 'fa-file-audio',
        'wav': 'fa-file-audio',
        # Video
        'mp4': 'fa-file-video',
        'avi': 'fa-file-video',
        'mov': 'fa-file-video',
        'wmv': 'fa-file-video',
        'flv': 'fa-file-video',
    }
    
    return icon_map.get(extension, 'fa-file')
