#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مسارات إدارة الجهات والمؤسسات
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy import or_
from models import db, Entity, User
from functools import wraps

entities_bp = Blueprint('entities', __name__, url_prefix='/entities')

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات الإدارة"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        if not (current_user.is_admin() or current_user.is_director() or 
                current_user.is_department_head() or current_user.is_manager()):
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

@entities_bp.route('/')
@login_required
def list_entities():
    """قائمة الجهات"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    entity_type = request.args.get('type', '')
    category = request.args.get('category', '')
    status = request.args.get('status', '')
    
    # بناء الاستعلام
    query = Entity.query
    
    # البحث
    if search:
        query = query.filter(
            or_(
                Entity.name.contains(search),
                Entity.name_en.contains(search),
                Entity.contact_person.contains(search),
                Entity.reference_number.contains(search)
            )
        )
    
    # فلترة حسب النوع
    if entity_type:
        query = query.filter(Entity.entity_type == entity_type)
    
    # فلترة حسب الفئة
    if category:
        query = query.filter(Entity.category == category)
    
    # فلترة حسب الحالة
    if status == 'active':
        query = query.filter(Entity.is_active == True)
    elif status == 'inactive':
        query = query.filter(Entity.is_active == False)
    elif status == 'government':
        query = query.filter(Entity.is_government == True)
    elif status == 'frequent':
        query = query.filter(Entity.is_frequent == True)
    
    # ترتيب وتقسيم الصفحات
    entities = query.order_by(Entity.name).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # إحصائيات
    stats = {
        'total': Entity.query.count(),
        'active': Entity.query.filter_by(is_active=True).count(),
        'government': Entity.query.filter_by(is_government=True).count(),
        'frequent': Entity.query.filter_by(is_frequent=True).count()
    }
    
    return render_template('entities/list.html', 
                         entities=entities, 
                         stats=stats,
                         search=search,
                         entity_type=entity_type,
                         category=category,
                         status=status)

@entities_bp.route('/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_entity():
    """إنشاء جهة جديدة"""
    if request.method == 'POST':
        name = request.form.get('name')
        name_en = request.form.get('name_en')
        entity_type = request.form.get('entity_type')
        category = request.form.get('category')
        
        if not name or not entity_type:
            flash('اسم الجهة ونوعها مطلوبان', 'error')
            return render_template('entities/new.html')
        
        # التحقق من عدم وجود جهة بنفس الاسم
        if Entity.query.filter_by(name=name).first():
            flash('يوجد جهة بهذا الاسم مسبقاً', 'error')
            return render_template('entities/new.html')
        
        # إنشاء الجهة الجديدة
        entity = Entity(
            name=name,
            name_en=name_en,
            entity_type=entity_type,
            category=category,
            address=request.form.get('address'),
            phone=request.form.get('phone'),
            fax=request.form.get('fax'),
            email=request.form.get('email'),
            website=request.form.get('website'),
            contact_person=request.form.get('contact_person'),
            contact_title=request.form.get('contact_title'),
            contact_phone=request.form.get('contact_phone'),
            contact_email=request.form.get('contact_email'),
            reference_number=request.form.get('reference_number'),
            notes=request.form.get('notes'),
            is_government=bool(request.form.get('is_government')),
            is_frequent=bool(request.form.get('is_frequent')),
            created_by=current_user.id
        )
        
        db.session.add(entity)
        db.session.commit()
        
        flash('تم إنشاء الجهة بنجاح', 'success')
        return redirect(url_for('entities.view_entity', id=entity.id))
    
    return render_template('entities/new.html')

@entities_bp.route('/<int:id>')
@login_required
def view_entity(id):
    """عرض تفاصيل الجهة"""
    entity = Entity.query.get_or_404(id)
    return render_template('entities/view.html', entity=entity)

@entities_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_entity(id):
    """تعديل الجهة"""
    entity = Entity.query.get_or_404(id)
    
    if request.method == 'POST':
        entity.name = request.form.get('name')
        entity.name_en = request.form.get('name_en')
        entity.entity_type = request.form.get('entity_type')
        entity.category = request.form.get('category')
        entity.address = request.form.get('address')
        entity.phone = request.form.get('phone')
        entity.fax = request.form.get('fax')
        entity.email = request.form.get('email')
        entity.website = request.form.get('website')
        entity.contact_person = request.form.get('contact_person')
        entity.contact_title = request.form.get('contact_title')
        entity.contact_phone = request.form.get('contact_phone')
        entity.contact_email = request.form.get('contact_email')
        entity.reference_number = request.form.get('reference_number')
        entity.notes = request.form.get('notes')
        entity.is_government = bool(request.form.get('is_government'))
        entity.is_frequent = bool(request.form.get('is_frequent'))
        entity.is_active = bool(request.form.get('is_active', True))
        
        if not entity.name or not entity.entity_type:
            flash('اسم الجهة ونوعها مطلوبان', 'error')
            return render_template('entities/edit.html', entity=entity)
        
        db.session.commit()
        flash('تم تحديث الجهة بنجاح', 'success')
        return redirect(url_for('entities.view_entity', id=id))
    
    return render_template('entities/edit.html', entity=entity)

@entities_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_entity(id):
    """حذف الجهة"""
    entity = Entity.query.get_or_404(id)
    
    # التحقق من عدم وجود رسائل مرتبطة بالجهة
    # (يمكن إضافة هذا التحقق لاحقاً عند ربط الجهات بالرسائل)
    
    db.session.delete(entity)
    db.session.commit()
    
    flash('تم حذف الجهة بنجاح', 'success')
    return redirect(url_for('entities.list_entities'))

@entities_bp.route('/api/search')
@login_required
def api_search():
    """API للبحث عن الجهات (للاستخدام في النماذج)"""
    query = request.args.get('q', '')
    limit = request.args.get('limit', 10, type=int)
    
    if not query:
        return jsonify([])
    
    entities = Entity.query.filter(
        Entity.is_active == True,
        or_(
            Entity.name.contains(query),
            Entity.name_en.contains(query)
        )
    ).limit(limit).all()
    
    results = []
    for entity in entities:
        results.append({
            'id': entity.id,
            'name': entity.name,
            'name_en': entity.name_en,
            'type': entity.get_type_display(),
            'category': entity.get_category_display()
        })
    
    return jsonify(results)

@entities_bp.route('/stats')
@login_required
def stats():
    """إحصائيات الجهات"""
    stats = {
        'total': Entity.query.count(),
        'active': Entity.query.filter_by(is_active=True).count(),
        'inactive': Entity.query.filter_by(is_active=False).count(),
        'government': Entity.query.filter_by(is_government=True).count(),
        'private': Entity.query.filter_by(entity_type='private').count(),
        'frequent': Entity.query.filter_by(is_frequent=True).count(),
        'by_type': {},
        'by_category': {}
    }
    
    # إحصائيات حسب النوع
    from sqlalchemy import func
    type_stats = db.session.query(
        Entity.entity_type, 
        func.count(Entity.id)
    ).group_by(Entity.entity_type).all()
    
    for entity_type, count in type_stats:
        stats['by_type'][entity_type] = count
    
    # إحصائيات حسب الفئة
    category_stats = db.session.query(
        Entity.category, 
        func.count(Entity.id)
    ).group_by(Entity.category).all()
    
    for category, count in category_stats:
        if category:
            stats['by_category'][category] = count
    
    return jsonify(stats)
