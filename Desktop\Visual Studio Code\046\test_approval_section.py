#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قسم الاعتماد والموافقة
"""

import requests
import sys

def test_approval_section():
    """اختبار قسم الاعتماد والموافقة"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار الصفحة الرئيسية للتحقق من وجود رابط الاعتماد
        print("\n🏠 اختبار الصفحة الرئيسية...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            if 'في انتظار الاعتماد' in content or 'awaiting_approval' in content:
                print("✅ رابط قسم الاعتماد موجود في الصفحة الرئيسية")
            else:
                print("⚠️ رابط قسم الاعتماد غير موجود في الصفحة الرئيسية")
        else:
            print(f"❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
        
        # اختبار صفحة الرسائل المعلقة للاعتماد
        print("\n📋 اختبار صفحة الرسائل المعلقة للاعتماد...")
        response = session.get(f"{base_url}/messages/pending_approval")
        if response.status_code == 200:
            print("✅ صفحة الرسائل المعلقة للاعتماد تعمل")
            
            content = response.text
            if 'الرسائل المعلقة للاعتماد' in content:
                print("✅ عنوان الصفحة صحيح")
            
            if 'لا توجد رسائل معلقة للاعتماد' in content:
                print("✅ رسالة عدم وجود رسائل معلقة معروضة")
            elif 'table' in content:
                print("✅ جدول الرسائل المعلقة معروض")
            
        elif response.status_code == 403:
            print("⚠️ المستخدم لا يملك صلاحية الوصول لصفحة الاعتماد")
        else:
            print(f"❌ فشل في الوصول لصفحة الرسائل المعلقة: {response.status_code}")
        
        # إنشاء رسالة تتطلب اعتماد للاختبار
        print("\n📝 إنشاء رسالة تتطلب اعتماد للاختبار...")
        message_data = {
            'registration_number': 'TEST-APPROVAL-001',
            'subject': 'رسالة اختبار الاعتماد',
            'content': 'هذه رسالة لاختبار نظام الاعتماد والموافقة',
            'message_type': 'outgoing',
            'priority': 'high',
            'requires_approval': 'on',  # تتطلب اعتماد
            'department': 'قسم الاختبار',
            'from_department': 'قسم الاختبار',
            'to_department': 'قسم آخر'
        }
        
        response = session.post(f"{base_url}/messages/new", data=message_data)
        if response.status_code == 200:
            print("✅ تم إنشاء رسالة تتطلب اعتماد")
            
            # اختبار صفحة الرسائل المعلقة مرة أخرى
            print("\n🔄 اختبار صفحة الرسائل المعلقة بعد إنشاء رسالة...")
            response = session.get(f"{base_url}/messages/pending_approval")
            if response.status_code == 200:
                content = response.text
                if 'TEST-APPROVAL-001' in content:
                    print("✅ الرسالة الجديدة ظهرت في قائمة الرسائل المعلقة")
                else:
                    print("⚠️ الرسالة الجديدة لم تظهر في قائمة الرسائل المعلقة")
            
        else:
            print(f"⚠️ مشكلة في إنشاء رسالة الاختبار: {response.status_code}")
        
        # اختبار عرض رسالة تتطلب اعتماد
        print("\n👁️ اختبار عرض رسالة تتطلب اعتماد...")
        
        # البحث عن رسالة تتطلب اعتماد
        response = session.get(f"{base_url}/messages")
        if response.status_code == 200:
            content = response.text
            
            # البحث عن معرف رسالة في المحتوى
            import re
            message_links = re.findall(r'/messages/(\d+)', content)
            
            if message_links:
                message_id = message_links[0]
                print(f"🔍 اختبار عرض الرسالة {message_id}...")
                
                response = session.get(f"{base_url}/messages/{message_id}")
                if response.status_code == 200:
                    content = response.text
                    
                    approval_features = []
                    if 'قسم الاعتماد والموافقة' in content:
                        approval_features.append("قسم الاعتماد")
                    if 'حالة الاعتماد' in content:
                        approval_features.append("حالة الاعتماد")
                    if 'اعتماد' in content and 'رفض' in content:
                        approval_features.append("أزرار الاعتماد والرفض")
                    if 'ملاحظات الاعتماد' in content:
                        approval_features.append("ملاحظات الاعتماد")
                    
                    if approval_features:
                        print(f"✅ ميزات الاعتماد الموجودة: {', '.join(approval_features)}")
                    else:
                        print("⚠️ ميزات الاعتماد غير موجودة في صفحة عرض الرسالة")
                else:
                    print(f"❌ فشل في عرض الرسالة: {response.status_code}")
            else:
                print("⚠️ لم يتم العثور على رسائل للاختبار")
        
        # اختبار القائمة الجانبية
        print("\n📋 اختبار القائمة الجانبية...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            
            nav_features = []
            if 'pending_approval' in content:
                nav_features.append("رابط الرسائل المعلقة")
            if 'في انتظار الاعتماد' in content:
                nav_features.append("نص الرابط بالعربية")
            if 'fas fa-stamp' in content:
                nav_features.append("أيقونة الاعتماد")
            
            if nav_features:
                print(f"✅ عناصر القائمة الجانبية: {', '.join(nav_features)}")
            else:
                print("⚠️ عناصر قسم الاعتماد غير موجودة في القائمة الجانبية")
        
        print("\n🎉 تم اكتمال اختبار قسم الاعتماد والموافقة!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار قسم الاعتماد والموافقة")
    print("=" * 60)
    
    success = test_approval_section()
    
    if success:
        print("\n✅ قسم الاعتماد والموافقة يعمل!")
        print("\n📋 الميزات المتاحة:")
        print("  📋 عرض الرسائل المعلقة للاعتماد")
        print("  ✅ اعتماد الرسائل")
        print("  ❌ رفض الرسائل")
        print("  📝 إضافة ملاحظات الاعتماد")
        print("  📊 عرض حالة الاعتماد")
        print("  📈 تتبع تاريخ الاعتمادات")
        print("\n🌐 للوصول لقسم الاعتماد:")
        print("  📋 الرسائل المعلقة: http://localhost:8585/messages/pending_approval")
        print("  📝 جميع الرسائل: http://localhost:8585/messages")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n💡 ملاحظة:")
        print("  • فقط المديرين والسكرتارية يمكنهم الوصول لقسم الاعتماد")
        print("  • الرسائل التي تتطلب اعتماد تظهر في قائمة منفصلة")
        print("  • يمكن اعتماد أو رفض الرسائل مع إضافة ملاحظات")
        sys.exit(0)
    else:
        print("\n❌ فشل في اختبار قسم الاعتماد والموافقة!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم")
        print("  • صلاحيات المستخدم")
        print("  • وجود رسائل تتطلب اعتماد")
        sys.exit(1)
