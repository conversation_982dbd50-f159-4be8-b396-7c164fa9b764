{% extends "base.html" %}

{% block title %}الأرشيف - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-archive me-2"></i>
                إدارة الأرشيف
            </h1>
            <div>
                <a href="{{ url_for('archive.search') }}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-2"></i>
                    البحث في الأرشيف
                </a>
                <a href="{{ url_for('archive.new_archive') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    أرشيف جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_archives }}</h4>
                        <p class="card-text">إجمالي الأرشيف</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-archive fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_archived_messages }}</h4>
                        <p class="card-text">الرسائل المؤرشفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-envelope-open fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "%.1f"|format(stats.total_size_mb) }} MB</h4>
                        <p class="card-text">حجم الأرشيف</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.active_messages }}</h4>
                        <p class="card-text">الرسائل النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-envelope fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Archives List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الأرشيف
                </h5>
            </div>
            <div class="card-body">
                {% if archives %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الأرشيف</th>
                                <th>النوع</th>
                                <th>الفترة</th>
                                <th>عدد الرسائل</th>
                                <th>الحجم</th>
                                <th>المنشئ</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for archive in archives %}
                            <tr>
                                <td>
                                    <strong>{{ archive.name }}</strong>
                                    {% if archive.description %}
                                    <br><small class="text-muted">{{ archive.description[:50] }}{% if archive.description|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if archive.archive_type == 'yearly' %}
                                        <span class="badge bg-primary">سنوي</span>
                                    {% elif archive.archive_type == 'monthly' %}
                                        <span class="badge bg-success">شهري</span>
                                    {% elif archive.archive_type == 'custom' %}
                                        <span class="badge bg-info">مخصص</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if archive.archive_type == 'yearly' and archive.year %}
                                        {{ archive.year }}
                                    {% elif archive.archive_type == 'monthly' and archive.year and archive.month %}
                                        {{ archive.month }}/{{ archive.year }}
                                    {% elif archive.archive_type == 'custom' and archive.start_date and archive.end_date %}
                                        {{ archive.start_date.strftime('%Y-%m-%d') }} - {{ archive.end_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ archive.get_messages_count() }}</span>
                                </td>
                                <td>
                                    {{ "%.1f"|format(archive.get_size_mb()) }} MB
                                </td>
                                <td>
                                    {{ archive.creator.name }}
                                </td>
                                <td>
                                    {{ archive.created_at.strftime('%Y-%m-%d') }}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('archive.view_archive', archive_id=archive.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('archive.add_messages', archive_id=archive.id) }}" 
                                           class="btn btn-outline-success" title="إضافة رسائل">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <a href="{{ url_for('archive.export_archive', archive_id=archive.id) }}" 
                                           class="btn btn-outline-info" title="تصدير">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {% if current_user.can_delete_messages() %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#deleteArchiveModal{{ archive.id }}"
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد أرشيف</h5>
                    <p class="text-muted">ابدأ بإنشاء أرشيف جديد لتنظيم رسائلك</p>
                    <a href="{{ url_for('archive.new_archive') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء أرشيف جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Archive Modals -->
{% for archive in archives %}
{% if current_user.can_delete_messages() %}
<div class="modal fade" id="deleteArchiveModal{{ archive.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حذف الأرشيف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الأرشيف <strong>{{ archive.name }}</strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيتم حذف جميع الرسائل المؤرشفة ({{ archive.get_messages_count() }} رسالة). هذا الإجراء لا يمكن التراجع عنه.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('archive.delete_archive', archive_id=archive.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-danger">حذف الأرشيف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}

{% block extra_css %}
<style>
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
