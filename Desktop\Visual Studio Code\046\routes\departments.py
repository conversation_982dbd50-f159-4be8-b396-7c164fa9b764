from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Department, User, UserRole, Message
from datetime import datetime

departments_bp = Blueprint('departments', __name__)

@departments_bp.route('/')
@login_required
def list_departments():
    """عرض جميع الأقسام"""
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لإدارة الأقسام', 'error')
        return redirect(url_for('index'))
    
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    
    departments = Department.query
    
    if search:
        departments = departments.filter(
            db.or_(
                Department.name.contains(search),
                Department.description.contains(search)
            )
        )
    
    if status == 'active':
        departments = departments.filter_by(is_active=True)
    elif status == 'inactive':
        departments = departments.filter_by(is_active=False)
    
    departments = departments.order_by(Department.name).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # إحصائيات الأقسام
    stats = {
        'total': Department.query.count(),
        'active': Department.query.filter_by(is_active=True).count(),
        'inactive': Department.query.filter_by(is_active=False).count(),
        'with_manager': Department.query.filter(Department.manager_id.isnot(None)).count()
    }
    
    return render_template('departments/list.html', departments=departments, stats=stats)

@departments_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_department():
    """إنشاء قسم جديد"""
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لإنشاء أقسام جديدة', 'error')
        return redirect(url_for('departments.list_departments'))
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        manager_id = request.form.get('manager_id')
        is_active = bool(request.form.get('is_active'))
        
        # التحقق من صحة البيانات
        if not name:
            flash('يرجى إدخال اسم القسم', 'error')
            return render_template('departments/new.html', users=get_available_managers())
        
        # التحقق من عدم وجود قسم بنفس الاسم
        existing_dept = Department.query.filter_by(name=name).first()
        if existing_dept:
            flash('يوجد قسم بهذا الاسم مسبقاً', 'error')
            return render_template('departments/new.html', users=get_available_managers())
        
        # إنشاء القسم الجديد
        department = Department(
            name=name,
            description=description,
            manager_id=int(manager_id) if manager_id else None,
            is_active=is_active if 'is_active' in request.form else True
        )
        
        db.session.add(department)
        db.session.commit()
        
        flash('تم إنشاء القسم بنجاح', 'success')
        return redirect(url_for('departments.view_department', id=department.id))
    
    users = get_available_managers()
    return render_template('departments/new.html', users=users)

@departments_bp.route('/<int:id>')
@login_required
def view_department(id):
    """عرض تفاصيل القسم"""
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لعرض تفاصيل الأقسام', 'error')
        return redirect(url_for('index'))
    
    department = Department.query.get_or_404(id)
    
    # إحصائيات القسم
    stats = {
        'total_messages': Message.query.filter(
            db.or_(
                Message.department == department.name,
                Message.from_department == department.name,
                Message.to_department == department.name
            )
        ).count(),
        'internal_messages': Message.query.filter(
            db.or_(
                Message.from_department == department.name,
                Message.to_department == department.name
            )
        ).count(),
        'pending_responses': Message.query.filter(
            Message.to_department == department.name,
            Message.requires_response == True,
            ~Message.responses.any()
        ).count()
    }
    
    # الرسائل الحديثة للقسم
    recent_messages = Message.query.filter(
        db.or_(
            Message.department == department.name,
            Message.from_department == department.name,
            Message.to_department == department.name
        )
    ).order_by(Message.date_created.desc()).limit(5).all()
    
    return render_template('departments/view.html', 
                         department=department, 
                         stats=stats, 
                         recent_messages=recent_messages)

@departments_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_department(id):
    """تعديل القسم"""
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لتعديل الأقسام', 'error')
        return redirect(url_for('departments.view_department', id=id))
    
    department = Department.query.get_or_404(id)
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        manager_id = request.form.get('manager_id')
        is_active = bool(request.form.get('is_active'))
        
        # التحقق من صحة البيانات
        if not name:
            flash('يرجى إدخال اسم القسم', 'error')
            return render_template('departments/edit.html', 
                                 department=department, 
                                 users=get_available_managers())
        
        # التحقق من عدم وجود قسم آخر بنفس الاسم
        existing_dept = Department.query.filter(
            Department.name == name,
            Department.id != department.id
        ).first()
        if existing_dept:
            flash('يوجد قسم آخر بهذا الاسم', 'error')
            return render_template('departments/edit.html', 
                                 department=department, 
                                 users=get_available_managers())
        
        # تحديث بيانات القسم
        department.name = name
        department.description = description
        department.manager_id = int(manager_id) if manager_id else None
        department.is_active = is_active
        
        db.session.commit()
        
        flash('تم تحديث القسم بنجاح', 'success')
        return redirect(url_for('departments.view_department', id=id))
    
    users = get_available_managers()
    return render_template('departments/edit.html', department=department, users=users)

@departments_bp.route('/<int:id>/toggle_status', methods=['POST'])
@login_required
def toggle_department_status(id):
    """تفعيل/إلغاء تفعيل القسم"""
    if not current_user.is_manager():
        flash('ليس لديك صلاحية لتغيير حالة الأقسام', 'error')
        return redirect(url_for('departments.list_departments'))
    
    department = Department.query.get_or_404(id)
    department.is_active = not department.is_active
    db.session.commit()
    
    status = 'تم تفعيل' if department.is_active else 'تم إلغاء تفعيل'
    flash(f'{status} القسم بنجاح', 'success')
    return redirect(url_for('departments.list_departments'))

@departments_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete_department(id):
    """حذف القسم"""
    if not current_user.is_manager():
        flash('ليس لديك صلاحية لحذف الأقسام', 'error')
        return redirect(url_for('departments.view_department', id=id))
    
    department = Department.query.get_or_404(id)
    
    # التحقق من وجود رسائل مرتبطة بالقسم
    related_messages = Message.query.filter(
        db.or_(
            Message.department == department.name,
            Message.from_department == department.name,
            Message.to_department == department.name
        )
    ).count()
    
    if related_messages > 0:
        flash(f'لا يمكن حذف القسم لوجود {related_messages} رسالة مرتبطة به', 'error')
        return redirect(url_for('departments.view_department', id=id))
    
    db.session.delete(department)
    db.session.commit()
    
    flash('تم حذف القسم بنجاح', 'success')
    return redirect(url_for('departments.list_departments'))

@departments_bp.route('/api/departments')
@login_required
def api_departments():
    """API لجلب قائمة الأقسام (للاستخدام في JavaScript)"""
    departments = Department.query.filter_by(is_active=True).order_by(Department.name).all()
    return jsonify([{
        'id': dept.id,
        'name': dept.name,
        'description': dept.description
    } for dept in departments])

@departments_bp.route('/api/users')
@login_required
def api_users():
    """API لجلب قائمة المستخدمين (للاستخدام في JavaScript)"""
    users = User.query.filter_by(is_active=True).order_by(User.name).all()
    return jsonify([{
        'id': user.id,
        'name': user.name,
        'role': user.role.value
    } for user in users])

@departments_bp.route('/<int:id>/employees')
@login_required
def department_employees(id):
    """عرض موظفي القسم"""
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لعرض موظفي الأقسام', 'error')
        return redirect(url_for('departments.view_department', id=id))

    department = Department.query.get_or_404(id)

    # جلب موظفي القسم
    employees = User.query.filter_by(department_id=id).order_by(User.name).all()

    # جلب المستخدمين غير المعينين لأي قسم
    unassigned_users = User.query.filter_by(department_id=None, is_active=True).order_by(User.name).all()

    return render_template('departments/employees.html',
                         department=department,
                         employees=employees,
                         unassigned_users=unassigned_users)

@departments_bp.route('/<int:id>/assign_user', methods=['POST'])
@login_required
def assign_user_to_department(id):
    """تعيين مستخدم للقسم"""
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لتعيين المستخدمين للأقسام', 'error')
        return redirect(url_for('departments.view_department', id=id))

    department = Department.query.get_or_404(id)
    user_id = request.form.get('user_id')

    if not user_id:
        flash('يرجى اختيار مستخدم', 'error')
        return redirect(url_for('departments.department_employees', id=id))

    user = User.query.get_or_404(user_id)
    user.department_id = department.id
    db.session.commit()

    flash(f'تم تعيين {user.name} للقسم بنجاح', 'success')
    return redirect(url_for('departments.department_employees', id=id))

@departments_bp.route('/<int:id>/remove_user/<int:user_id>', methods=['POST'])
@login_required
def remove_user_from_department(id, user_id):
    """إزالة مستخدم من القسم"""
    if not current_user.can_manage_users():
        flash('ليس لديك صلاحية لإزالة المستخدمين من الأقسام', 'error')
        return redirect(url_for('departments.view_department', id=id))

    department = Department.query.get_or_404(id)
    user = User.query.get_or_404(user_id)

    # التحقق من أن المستخدم ليس مدير القسم
    if department.manager_id == user.id:
        flash('لا يمكن إزالة مدير القسم. يرجى تعيين مدير آخر أولاً', 'error')
        return redirect(url_for('departments.department_employees', id=id))

    user.department_id = None
    db.session.commit()

    flash(f'تم إزالة {user.name} من القسم بنجاح', 'success')
    return redirect(url_for('departments.department_employees', id=id))

def get_available_managers():
    """جلب المستخدمين المؤهلين لإدارة الأقسام"""
    return User.query.filter(
        User.is_active == True,
        User.role.in_([UserRole.MANAGER, UserRole.SECRETARY])
    ).order_by(User.name).all()
