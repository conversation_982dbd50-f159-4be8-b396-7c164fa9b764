#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل للتحديثات - الحالة والأيام في لوحة التحكم وقائمة الرسائل
"""

import requests
import sys

def test_complete_updates():
    """اختبار شامل للتحديثات"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار لوحة التحكم
        print("\n📊 اختبار لوحة التحكم...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            dashboard_checks = [
                ('الحالة', 'عمود الحالة'),
                ('الأيام', 'عمود الأيام'),
                ('منذ الإنشاء', 'نص الأيام'),
                ('get_status_display', 'دالة عرض الحالة'),
                ('get_days_since', 'دالة حساب الأيام')
            ]
            
            for check, description in dashboard_checks:
                if check in content:
                    print(f"✅ {description}")
                else:
                    print(f"⚠️ {description} (قد يكون مخفي)")
        else:
            print(f"❌ فشل الوصول للوحة التحكم: {response.status_code}")
            return False
        
        # اختبار قائمة الرسائل
        print("\n📋 اختبار قائمة الرسائل...")
        response = session.get(f"{base_url}/messages")
        if response.status_code == 200:
            content = response.text
            messages_checks = [
                ('الحالة', 'عمود الحالة في قائمة الرسائل'),
                ('الأيام', 'عمود الأيام في قائمة الرسائل'),
                ('get_status_badge_class', 'دالة ألوان الحالة'),
                ('get_priority_badge_class', 'دالة ألوان الأولوية')
            ]
            
            for check, description in messages_checks:
                if check in content:
                    print(f"✅ {description}")
                else:
                    print(f"⚠️ {description} (قد يكون مخفي)")
        else:
            print(f"❌ فشل الوصول لقائمة الرسائل: {response.status_code}")
            return False
        
        # اختبار إنشاء رسالة بأولوية عالية
        print("\n📝 اختبار إنشاء رسالة بأولوية عالية...")
        message_data = {
            'message_type': 'incoming',
            'registration_number': 'HIGH-2025-001',
            'destination': 'جهة مهمة',
            'subject': 'رسالة عالية الأولوية',
            'content': 'محتوى مهم',
            'message_date': '2025-01-15',
            'priority': 'high',
            'status': 'active'
        }
        
        response = session.post(f"{base_url}/messages/new", data=message_data)
        if response.status_code == 200:
            print("✅ تم إنشاء رسالة عالية الأولوية")
        else:
            print(f"⚠️ مشكلة في إنشاء الرسالة: {response.status_code}")
        
        # اختبار إنشاء رسالة عاجلة
        print("\n🚨 اختبار إنشاء رسالة عاجلة...")
        urgent_message_data = {
            'message_type': 'outgoing',
            'registration_number': 'URGENT-2025-001',
            'destination': 'جهة عاجلة',
            'subject': 'رسالة عاجلة جداً',
            'content': 'محتوى عاجل',
            'message_date': '2025-01-15',
            'priority': 'urgent',
            'status': 'active'
        }
        
        response = session.post(f"{base_url}/messages/new", data=urgent_message_data)
        if response.status_code == 200:
            print("✅ تم إنشاء رسالة عاجلة")
        else:
            print(f"⚠️ مشكلة في إنشاء الرسالة العاجلة: {response.status_code}")
        
        # اختبار عرض الرسائل الجديدة
        print("\n🔄 اختبار عرض الرسائل الجديدة...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            if 'HIGH-2025-001' in content or 'URGENT-2025-001' in content:
                print("✅ تم العثور على الرسائل الجديدة في لوحة التحكم")
            else:
                print("⚠️ لم يتم العثور على الرسائل الجديدة")
        
        response = session.get(f"{base_url}/messages")
        if response.status_code == 200:
            content = response.text
            if 'HIGH-2025-001' in content or 'URGENT-2025-001' in content:
                print("✅ تم العثور على الرسائل الجديدة في قائمة الرسائل")
            else:
                print("⚠️ لم يتم العثور على الرسائل الجديدة في القائمة")
        
        print("\n🎉 تم اكتمال جميع الاختبارات!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل للتحديثات - الحالة والأيام")
    print("=" * 60)
    
    success = test_complete_updates()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        print("\n📋 التحديثات المطبقة:")
        print("🏠 لوحة التحكم:")
        print("  • إضافة عمود الحالة مع الألوان")
        print("  • إضافة عمود الأيام منذ الإنشاء")
        print("  • عرض الأولوية والاعتماد")
        print("  • تحسين التصميم المتجاوب")
        print("\n📋 قائمة الرسائل:")
        print("  • إضافة عمود الحالة")
        print("  • إضافة عمود الأيام")
        print("  • تحسين عرض الأولوية")
        print("  • تحسين التصميم للشاشات الصغيرة")
        print("\n🎨 الميزات الجديدة:")
        print("  • ألوان مختلفة للحالات (نشطة، مؤرشفة، محذوفة)")
        print("  • ألوان مختلفة للأولوية (عادية، عالية، عاجلة)")
        print("  • عرض حالة الاعتماد (معلقة، معتمدة، مرفوضة)")
        print("  • حساب الأيام منذ الإنشاء ومن تاريخ الرسالة")
        print("\n🌐 يمكنك الآن مشاهدة التحديثات على:")
        print("http://localhost:8585")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض الاختبارات!")
        sys.exit(1)
