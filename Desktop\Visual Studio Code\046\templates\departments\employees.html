{% extends "base.html" %}

{% block title %}موظفو {{ department.name }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users me-2"></i>
                موظفو قسم {{ department.name }}
            </h1>
            <div>
                <a href="{{ url_for('departments.view_department', id=department.id) }}" class="btn btn-info">
                    <i class="fas fa-building me-2"></i>
                    عرض القسم
                </a>
                <a href="{{ url_for('departments.list_departments') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للأقسام
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Department Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>اسم القسم:</strong>
                        <p class="mb-0">{{ department.name }}</p>
                    </div>
                    <div class="col-md-3">
                        <strong>مدير القسم:</strong>
                        <p class="mb-0">
                            {% if department.manager %}
                                {{ department.manager.name }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-3">
                        <strong>عدد الموظفين:</strong>
                        <p class="mb-0">{{ employees|length }}</p>
                    </div>
                    <div class="col-md-3">
                        <strong>الحالة:</strong>
                        <p class="mb-0">
                            {% if department.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Employee -->
{% if unassigned_users %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة موظف للقسم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('departments.assign_user_to_department', id=department.id) }}" class="row g-3">
                    <div class="col-md-8">
                        <label for="user_id" class="form-label">اختر الموظف</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="">اختر موظف لإضافته للقسم</option>
                            {% for user in unassigned_users %}
                            <option value="{{ user.id }}">
                                {{ user.name }} ({{ user.role.value }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة للقسم
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Current Employees -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    موظفو القسم الحاليون
                </h5>
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الاسم</th>
                                <th>اسم المستخدم</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ الانضمام</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <strong>{{ employee.name }}</strong>
                                            {% if department.manager_id == employee.id %}
                                                <span class="badge bg-warning ms-2">
                                                    <i class="fas fa-crown me-1"></i>مدير القسم
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>{{ employee.username }}</td>
                                <td>
                                    {% if employee.role.value == 'manager' %}
                                        <span class="badge bg-danger">مدير</span>
                                    {% elif employee.role.value == 'secretary' %}
                                        <span class="badge bg-warning">سكرتير</span>
                                    {% else %}
                                        <span class="badge bg-info">موظف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if employee.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ employee.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('users.view_user', id=employee.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('users.edit_user', id=employee.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if department.manager_id != employee.id %}
                                        <form method="POST" 
                                              action="{{ url_for('departments.remove_user_from_department', id=department.id, user_id=employee.id) }}" 
                                              style="display: inline;"
                                              onsubmit="return confirm('هل أنت متأكد من إزالة {{ employee.name }} من القسم؟')">
                                            <button type="submit" class="btn btn-outline-danger btn-sm" title="إزالة من القسم">
                                                <i class="fas fa-user-minus"></i>
                                            </button>
                                        </form>
                                        {% else %}
                                        <button class="btn btn-outline-secondary btn-sm" disabled title="لا يمكن إزالة مدير القسم">
                                            <i class="fas fa-lock"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-users fa-4x mb-3"></i>
                    <h4>لا يوجد موظفون في هذا القسم</h4>
                    <p>لم يتم تعيين أي موظفين لهذا القسم حتى الآن</p>
                    {% if unassigned_users %}
                    <p class="text-info">يمكنك إضافة موظفين من النموذج أعلاه</p>
                    {% else %}
                    <p class="text-warning">جميع المستخدمين معينون لأقسام أخرى</p>
                    <a href="{{ url_for('users.new_user') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إنشاء مستخدم جديد
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ employees|length }}</h3>
                <p class="mb-0">إجمالي الموظفين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ employees|selectattr('is_active', 'equalto', true)|list|length }}</h3>
                <p class="mb-0">الموظفون النشطون</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ employees|selectattr('role.value', 'equalto', 'secretary')|list|length + employees|selectattr('role.value', 'equalto', 'manager')|list|length }}</h3>
                <p class="mb-0">الإداريون</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ employees|selectattr('role.value', 'equalto', 'employee')|list|length }}</h3>
                <p class="mb-0">الموظفون العاديون</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('users.new_user') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            مستخدم جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('departments.edit_department', id=department.id) }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-edit me-2"></i>
                            تعديل القسم
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('messages.internal_messages', department=department.name) }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-envelope me-2"></i>
                            مراسلات القسم
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary btn-lg w-100">
                            <i class="fas fa-users me-2"></i>
                            جميع المستخدمين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}
</style>
{% endblock %}
