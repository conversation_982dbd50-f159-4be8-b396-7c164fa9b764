{% extends "base.html" %}

{% block title %}الرد على المراسلة {{ message.registration_number }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-reply me-2"></i>
                الرد على المراسلة الداخلية
            </h1>
            <div>
                <a href="{{ url_for('messages.view_message', id=message.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمراسلة
                </a>
                <a href="{{ url_for('messages.internal_messages') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-building me-2"></i>
                    المراسلات الداخلية
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Original Message -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    المراسلة الأصلية
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-md-6">
                        <strong>رقم التسجيل:</strong> {{ message.registration_number }}
                    </div>
                    <div class="col-md-6">
                        <strong>التاريخ:</strong> {{ message.message_date.strftime('%Y-%m-%d') }}
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-6">
                        <strong>من القسم:</strong> {{ message.from_department or 'غير محدد' }}
                    </div>
                    <div class="col-md-6">
                        <strong>إلى القسم:</strong> {{ message.to_department or 'غير محدد' }}
                    </div>
                </div>
                <div class="mb-2">
                    <strong>الموضوع:</strong> {{ message.subject }}
                </div>
                {% if message.content %}
                <div class="mb-2">
                    <strong>المحتوى:</strong>
                    <div class="border rounded p-2 bg-light mt-1">
                        {{ message.content|nl2br }}
                    </div>
                </div>
                {% endif %}
                {% if message.due_date %}
                <div class="mb-2">
                    <strong>تاريخ الاستحقاق:</strong> 
                    <span class="{% if message.due_date < moment().date() %}text-danger{% endif %}">
                        {{ message.due_date.strftime('%Y-%m-%d') }}
                        {% if message.due_date < moment().date() %}
                            <i class="fas fa-exclamation-triangle ms-1" title="متأخر"></i>
                        {% endif %}
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Response Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    كتابة الرد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="response_content" class="form-label">محتوى الرد <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="response_content" name="response_content" rows="6" 
                                  placeholder="اكتب ردك على المراسلة هنا..." required></textarea>
                        <div class="invalid-feedback">
                            يرجى إدخال محتوى الرد
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">مرفق الرد (اختياري)</label>
                        <div class="file-upload-area">
                            <input type="file" name="attachment" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt" style="display: none;">
                            <i class="fas fa-cloud-upload-alt fa-2x mb-2 text-muted"></i>
                            <p class="mb-0">اسحب الملف هنا أو انقر للاختيار</p>
                            <small class="text-muted">الملفات المدعومة: PDF, Word, صور, نصوص (حد أقصى 16 ميجابايت)</small>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم إرسال إشعار للمرسل الأصلي عند إرسال الرد.
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال الرد
                        </button>
                        <a href="{{ url_for('messages.view_message', id=message.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Response Guidelines -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    إرشادات الرد
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اكتب رداً واضحاً ومفصلاً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أرفق المستندات الداعمة إن وجدت
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من الإجابة على جميع النقاط
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم لغة مهنية ومناسبة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        راجع الرد قبل الإرسال
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Message Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المراسلة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>المرسل:</strong> {{ message.creator.name }}
                </div>
                <div class="mb-2">
                    <strong>تاريخ الإرسال:</strong> {{ message.date_created.strftime('%Y-%m-%d %H:%M') }}
                </div>
                {% if message.is_urgent %}
                <div class="mb-2">
                    <span class="badge bg-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        عاجل
                    </span>
                </div>
                {% endif %}
                {% if message.priority != 'normal' %}
                <div class="mb-2">
                    <strong>الأولوية:</strong> 
                    {% if message.priority == 'high' %}
                        <span class="badge bg-warning">مهم</span>
                    {% elif message.priority == 'urgent' %}
                        <span class="badge bg-danger">عاجل</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Previous Responses -->
        {% if message.responses %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    الردود السابقة
                </h5>
            </div>
            <div class="card-body">
                {% for response in message.responses %}
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between">
                        <strong>{{ response.responder.name }}</strong>
                        <small class="text-muted">{{ response.response_date.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    <p class="mb-1 small">{{ response.response_content[:100] }}{% if response.response_content|length > 100 %}...{% endif %}</p>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textarea
    const textarea = document.getElementById('response_content');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });
    
    // Character counter
    textarea.addEventListener('input', function() {
        const charCount = this.value.length;
        let counter = document.getElementById('char-counter');
        if (!counter) {
            counter = document.createElement('small');
            counter.id = 'char-counter';
            counter.className = 'text-muted';
            this.parentNode.appendChild(counter);
        }
        counter.textContent = `${charCount} حرف`;
    });
});
</script>
{% endblock %}
