{% extends "base.html" %}

{% block title %}{{ department.name }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-building me-2"></i>
                {{ department.name }}
                {% if not department.is_active %}
                    <span class="badge bg-secondary ms-2">غير نشط</span>
                {% endif %}
            </h1>
            <div>
                <a href="{{ url_for('departments.edit_department', id=department.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل القسم
                </a>
                <a href="{{ url_for('departments.list_departments') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Department Info -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات القسم
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>اسم القسم:</strong>
                        <p class="mb-0">{{ department.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>الحالة:</strong>
                        <p class="mb-0">
                            {% if department.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>مدير القسم:</strong>
                        <p class="mb-0">
                            {% if department.manager_id %}
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-2">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    {{ department.manager.name }}
                                    <span class="badge bg-info ms-2">{{ department.manager.role.value }}</span>
                                </div>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الإنشاء:</strong>
                        <p class="mb-0">{{ department.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
                
                {% if department.description %}
                <div class="mb-3">
                    <strong>وصف القسم:</strong>
                    <p class="mb-0 mt-1">{{ department.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Statistics -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات القسم
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <div class="stat-item">
                            <h3 class="text-primary">{{ stats.total_messages }}</h3>
                            <p class="mb-0 small">إجمالي الرسائل</p>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="stat-item">
                            <h4 class="text-warning">{{ stats.internal_messages }}</h4>
                            <p class="mb-0 small">المراسلات الداخلية</p>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="stat-item">
                            <h4 class="text-danger">{{ stats.pending_responses }}</h4>
                            <p class="mb-0 small">بانتظار الرد</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Messages -->
{% if recent_messages %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل الحديثة للقسم
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>الموضوع</th>
                                <th>النوع</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in recent_messages %}
                            <tr>
                                <td>
                                    <strong>{{ message.registration_number }}</strong>
                                    {% if message.is_urgent %}
                                        <i class="fas fa-exclamation-triangle text-danger ms-1" title="عاجل"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject[:40] }}{% if message.subject|length > 40 %}...{% endif %}
                                    </a>
                                </td>
                                <td>
                                    {% if message.is_incoming() %}
                                        <span class="badge bg-success">واردة</span>
                                    {% elif message.is_outgoing() %}
                                        <span class="badge bg-info">صادرة</span>
                                    {% else %}
                                        <span class="badge bg-warning">داخلية</span>
                                    {% endif %}
                                </td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if message.is_internal() and message.requires_response %}
                                        {% if message.responses %}
                                            <span class="badge bg-success">تم الرد</span>
                                        {% else %}
                                            <span class="badge bg-warning">بانتظار الرد</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-info">{{ message.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('messages.list_messages', department=department.name) }}" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>
                        عرض جميع رسائل القسم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    إجراءات القسم
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('departments.edit_department', id=department.id) }}" 
                           class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-edit me-2"></i>
                            تعديل القسم
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('messages.new_message') }}?department={{ department.name }}" 
                           class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus me-2"></i>
                            رسالة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('departments.department_employees', id=department.id) }}"
                           class="btn btn-success btn-lg w-100">
                            <i class="fas fa-users me-2"></i>
                            إدارة الموظفين
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('messages.internal_messages', department=department.name) }}"
                           class="btn btn-info btn-lg w-100">
                            <i class="fas fa-building me-2"></i>
                            المراسلات الداخلية
                        </a>
                    </div>
                    {% if current_user.is_manager() %}
                    <div class="col-md-3 mb-3">
                        <form method="POST" action="{{ url_for('departments.toggle_department_status', id=department.id) }}" 
                              onsubmit="return confirm('هل أنت متأكد من تغيير حالة القسم؟')">
                            <button type="submit" 
                                    class="btn btn-{{ 'secondary' if department.is_active else 'success' }} btn-lg w-100">
                                <i class="fas fa-{{ 'ban' if department.is_active else 'check' }} me-2"></i>
                                {{ 'إلغاء التفعيل' if department.is_active else 'تفعيل القسم' }}
                            </button>
                        </form>
                    </div>
                    {% endif %}
                </div>
                
                {% if current_user.is_manager() %}
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                منطقة خطر
                            </h6>
                            <p class="mb-2">حذف القسم سيؤثر على جميع الرسائل المرتبطة به.</p>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash me-2"></i>
                                حذف القسم
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.stat-item {
    padding: 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف هذا القسم؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!')) {
        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("departments.delete_department", id=department.id) }}';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
