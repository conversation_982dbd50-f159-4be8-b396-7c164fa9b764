#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دليل استكشاف الأخطاء وإصلاحها
"""

import sys
import os
import subprocess
import importlib

def check_python():
    """التحقق من Python"""
    print("🐍 فحص Python:")
    print(f"  الإصدار: {sys.version}")
    print(f"  المسار: {sys.executable}")
    
    if sys.version_info < (3, 8):
        print("  ⚠️ يُنصح بـ Python 3.8 أو أحدث")
    else:
        print("  ✅ إصدار Python مناسب")

def check_packages():
    """التحقق من الحزم"""
    print("\n📦 فحص الحزم المطلوبة:")
    
    packages = {
        'flask': 'Flask',
        'flask_sqlalchemy': 'Flask-SQLAlchemy', 
        'flask_login': 'Flask-Login',
        'werkzeug': 'Werkzeug',
        'jinja2': 'Jinja2'
    }
    
    missing = []
    
    for package, name in packages.items():
        try:
            importlib.import_module(package)
            print(f"  ✅ {name}")
        except ImportError:
            print(f"  ❌ {name} - مفقود")
            missing.append(name.lower())
    
    if missing:
        print(f"\n📥 لتثبيت الحزم المفقودة:")
        print(f"  pip install {' '.join(missing)}")
        return False
    
    return True

def check_files():
    """التحقق من الملفات"""
    print("\n📁 فحص الملفات الأساسية:")
    
    files = [
        'app.py',
        'models.py', 
        'routes/auth.py',
        'routes/messages.py',
        'templates/base.html'
    ]
    
    missing = []
    
    for file in files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - مفقود")
            missing.append(file)
    
    return len(missing) == 0

def check_database():
    """التحقق من قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات:")
    
    if os.path.exists('correspondence.db'):
        print("  ✅ ملف قاعدة البيانات موجود")
        return True
    else:
        print("  ⚠️ ملف قاعدة البيانات غير موجود")
        print("  💡 سيتم إنشاؤه عند أول تشغيل")
        return True

def test_import():
    """اختبار الاستيراد"""
    print("\n🧪 اختبار استيراد التطبيق:")
    
    try:
        from app import create_app
        print("  ✅ تم استيراد create_app")
        
        app = create_app()
        print("  ✅ تم إنشاء التطبيق")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء التطبيق: {e}")
        return False

def check_port():
    """التحقق من المنفذ"""
    print("\n🔌 فحص المنفذ 8585:")
    
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8585))
        sock.close()
        
        if result == 0:
            print("  ⚠️ المنفذ 8585 محجوز")
            print("  💡 جرب منفذ آخر أو أوقف التطبيق الآخر")
            return False
        else:
            print("  ✅ المنفذ 8585 متاح")
            return True
            
    except Exception as e:
        print(f"  ⚠️ لا يمكن فحص المنفذ: {e}")
        return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📥 تثبيت المتطلبات:")
    
    requirements = [
        'flask',
        'flask-sqlalchemy',
        'flask-login', 
        'werkzeug',
        'jinja2'
    ]
    
    for req in requirements:
        try:
            print(f"  📦 تثبيت {req}...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', req
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"  ✅ تم تثبيت {req}")
        except subprocess.CalledProcessError:
            print(f"  ❌ فشل تثبيت {req}")

def create_minimal_app():
    """إنشاء تطبيق مبسط للاختبار"""
    print("\n🔧 إنشاء تطبيق اختبار مبسط:")
    
    minimal_app = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return """
    <h1>🏢 نظام المراسلات الإلكترونية</h1>
    <p>النظام يعمل بشكل أساسي!</p>
    <p><a href="/test">اختبار الصفحة</a></p>
    """

@app.route('/test')
def test():
    return "<h2>✅ الاختبار نجح!</h2>"

if __name__ == '__main__':
    print("🚀 تشغيل تطبيق الاختبار...")
    print("🌐 الرابط: http://localhost:8585")
    app.run(host='0.0.0.0', port=8585, debug=True)
'''
    
    with open('test_app.py', 'w', encoding='utf-8') as f:
        f.write(minimal_app)
    
    print("  ✅ تم إنشاء test_app.py")
    print("  💡 شغل: python test_app.py للاختبار")

def main():
    """الدالة الرئيسية"""
    print("🔍 دليل استكشاف الأخطاء وإصلاحها")
    print("=" * 50)
    
    # فحص شامل
    python_ok = True
    packages_ok = check_packages()
    files_ok = check_files()
    db_ok = check_database()
    import_ok = test_import() if packages_ok else False
    port_ok = check_port()
    
    check_python()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    
    if not packages_ok:
        print("❌ حزم مفقودة - تثبيت المتطلبات")
        install_requirements()
    
    if not files_ok:
        print("❌ ملفات مفقودة - تحقق من اكتمال النظام")
    
    if not import_ok:
        print("❌ مشكلة في الاستيراد - تحقق من الأخطاء أعلاه")
    
    if not port_ok:
        print("❌ مشكلة في المنفذ - استخدم منفذ آخر")
    
    if packages_ok and files_ok and import_ok and port_ok:
        print("✅ جميع الفحوصات نجحت!")
        print("\n🚀 طرق التشغيل:")
        print("  1. python simple_start.py")
        print("  2. python app.py")
        print("  3. run.bat (على Windows)")
    else:
        print("\n🔧 خطوات الإصلاح:")
        if not packages_ok:
            print("  1. تثبيت المتطلبات: pip install flask flask-sqlalchemy flask-login")
        if not files_ok:
            print("  2. التأكد من وجود جميع الملفات")
        if not import_ok:
            print("  3. إصلاح أخطاء الاستيراد")
        if not port_ok:
            print("  4. تغيير المنفذ أو إيقاف التطبيق الآخر")
        
        print("\n🧪 للاختبار السريع:")
        create_minimal_app()

if __name__ == "__main__":
    main()
