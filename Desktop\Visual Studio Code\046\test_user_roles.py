#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تعديل أدوار المستخدمين
"""

import requests
import sys

def test_user_roles():
    """اختبار تعديل أدوار المستخدمين"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار الوصول لصفحة المستخدمين
        print("\n📋 اختبار الوصول لصفحة المستخدمين...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            print("✅ تم الوصول لصفحة المستخدمين بنجاح")
        else:
            print(f"❌ فشل الوصول لصفحة المستخدمين: {response.status_code}")
            return False
        
        # اختبار الوصول لصفحة الصلاحيات
        print("\n🔐 اختبار الوصول لصفحة الصلاحيات...")
        response = session.get(f"{base_url}/permissions")
        if response.status_code == 200:
            print("✅ تم الوصول لصفحة الصلاحيات بنجاح")
        else:
            print(f"❌ فشل الوصول لصفحة الصلاحيات: {response.status_code}")
            return False
        
        # اختبار الوصول لصفحة الأدوار
        print("\n📝 اختبار الوصول لصفحة الأدوار...")
        response = session.get(f"{base_url}/permissions/roles")
        if response.status_code == 200:
            print("✅ تم الوصول لصفحة الأدوار بنجاح")
        else:
            print(f"❌ فشل الوصول لصفحة الأدوار: {response.status_code}")
            return False
        
        # اختبار تهيئة الصلاحيات
        print("\n⚙️ اختبار تهيئة الصلاحيات...")
        response = session.post(f"{base_url}/permissions/init-permissions")
        if response.status_code == 200:
            print("✅ تم تهيئة الصلاحيات بنجاح")
        else:
            print(f"❌ فشل تهيئة الصلاحيات: {response.status_code}")
        
        # اختبار إنشاء دور جديد
        print("\n➕ اختبار إنشاء دور جديد...")
        role_data = {
            'name': 'test_role',
            'display_name': 'دور اختبار',
            'description': 'دور للاختبار',
            'permissions': ['view_message', 'create_message']
        }
        
        response = session.post(f"{base_url}/permissions/roles/new", data=role_data)
        if response.status_code == 200:
            print("✅ تم إنشاء الدور الجديد بنجاح")
        else:
            print(f"❌ فشل إنشاء الدور الجديد: {response.status_code}")
        
        # اختبار الوصول لصفحة تعديل المستخدم (المستخدم الأول)
        print("\n✏️ اختبار الوصول لصفحة تعديل المستخدم...")
        response = session.get(f"{base_url}/users/1/edit")
        if response.status_code == 200:
            print("✅ تم الوصول لصفحة تعديل المستخدم بنجاح")
            
            # التحقق من وجود خيارات الأدوار الجديدة
            if 'custom_role_id' in response.text:
                print("✅ تم العثور على خيار الدور المخصص")
            else:
                print("❌ لم يتم العثور على خيار الدور المخصص")
                
            if 'دور اختبار' in response.text:
                print("✅ تم العثور على الدور الجديد في القائمة")
            else:
                print("⚠️ لم يتم العثور على الدور الجديد في القائمة")
        else:
            print(f"❌ فشل الوصول لصفحة تعديل المستخدم: {response.status_code}")
            return False
        
        # اختبار الوصول لصفحة إدارة صلاحيات المستخدم
        print("\n🔑 اختبار الوصول لصفحة إدارة صلاحيات المستخدم...")
        response = session.get(f"{base_url}/permissions/users/1/permissions")
        if response.status_code == 200:
            print("✅ تم الوصول لصفحة إدارة صلاحيات المستخدم بنجاح")
        else:
            print(f"❌ فشل الوصول لصفحة إدارة صلاحيات المستخدم: {response.status_code}")
        
        print("\n🎉 تم اكتمال جميع الاختبارات بنجاح!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار نظام تعديل أدوار المستخدمين")
    print("=" * 50)
    
    success = test_user_roles()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض الاختبارات!")
        sys.exit(1)
