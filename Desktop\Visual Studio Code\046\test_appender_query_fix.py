#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح خطأ AppenderQuery
"""

import requests
import sys

def test_appender_query_fix():
    """اختبار إصلاح خطأ AppenderQuery"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار لوحة التحكم (التي تحتوي على pending_internal)
        print("\n📊 اختبار لوحة التحكم...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ لوحة التحكم تعمل بدون أخطاء AppenderQuery")
        else:
            print(f"❌ خطأ في لوحة التحكم: {response.status_code}")
            return False
        
        # اختبار المراسلات الداخلية (التي تحتوي على pending filter)
        print("\n📨 اختبار المراسلات الداخلية...")
        response = session.get(f"{base_url}/messages/internal")
        if response.status_code == 200:
            print("✅ صفحة المراسلات الداخلية تعمل بدون أخطاء")
        else:
            print(f"❌ خطأ في المراسلات الداخلية: {response.status_code}")
            return False
        
        # اختبار المراسلات الداخلية مع فلتر pending
        print("\n⏳ اختبار فلتر المراسلات المعلقة...")
        response = session.get(f"{base_url}/messages/internal?status=pending")
        if response.status_code == 200:
            print("✅ فلتر المراسلات المعلقة يعمل بدون أخطاء")
        else:
            print(f"❌ خطأ في فلتر المراسلات المعلقة: {response.status_code}")
            return False
        
        # اختبار تفاصيل الجهة (التي تحتوي على pending_responses)
        print("\n🏢 اختبار تفاصيل الجهة...")
        response = session.get(f"{base_url}/departments/1")
        if response.status_code == 200:
            print("✅ صفحة تفاصيل الجهة تعمل بدون أخطاء")
        else:
            print(f"⚠️ لا توجد جهة بالمعرف 1 أو مشكلة في الوصول: {response.status_code}")
        
        # اختبار إنشاء رسالة داخلية لاختبار النظام
        print("\n📝 اختبار إنشاء رسالة داخلية...")
        internal_message_data = {
            'message_type': 'internal',
            'registration_number': 'INT-2025-001',
            'destination': 'قسم الاختبار',
            'subject': 'رسالة داخلية للاختبار',
            'content': 'محتوى رسالة داخلية',
            'message_date': '2025-01-15',
            'from_department': 'الإدارة العامة',
            'to_department': 'قسم الاختبار',
            'requires_response': 'on',
            'is_urgent': 'on'
        }
        
        response = session.post(f"{base_url}/messages/new", data=internal_message_data)
        if response.status_code == 200:
            print("✅ تم إنشاء رسالة داخلية بنجاح")
        else:
            print(f"⚠️ مشكلة في إنشاء الرسالة الداخلية: {response.status_code}")
        
        # اختبار المراسلات الداخلية مرة أخرى بعد إنشاء رسالة
        print("\n🔄 اختبار المراسلات الداخلية بعد إنشاء رسالة...")
        response = session.get(f"{base_url}/messages/internal")
        if response.status_code == 200:
            print("✅ المراسلات الداخلية تعمل بعد إنشاء رسالة جديدة")
        else:
            print(f"❌ خطأ في المراسلات الداخلية بعد إنشاء رسالة: {response.status_code}")
            return False
        
        # اختبار فلتر العاجل
        print("\n🚨 اختبار فلتر المراسلات العاجلة...")
        response = session.get(f"{base_url}/messages/internal?status=urgent")
        if response.status_code == 200:
            print("✅ فلتر المراسلات العاجلة يعمل بدون أخطاء")
        else:
            print(f"❌ خطأ في فلتر المراسلات العاجلة: {response.status_code}")
            return False
        
        print("\n🎉 تم اكتمال جميع اختبارات إصلاح AppenderQuery!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار إصلاح خطأ AppenderQuery")
    print("=" * 60)
    
    success = test_appender_query_fix()
    
    if success:
        print("\n✅ جميع اختبارات إصلاح AppenderQuery نجحت!")
        print("\n🔧 الإصلاحات المطبقة:")
        print("  ✅ إصلاح خطأ AppenderQuery في routes/departments.py")
        print("  ✅ إصلاح خطأ AppenderQuery في routes/messages.py")
        print("  ✅ إصلاح خطأ AppenderQuery في app.py")
        print("\n📋 التفاصيل التقنية:")
        print("  • استبدال Message.responses == None")
        print("  • استخدام db.exists() مع InternalMessageResponse")
        print("  • التحقق من حالة الاعتماد بشكل صحيح")
        print("\n🎯 المشاكل المحلولة:")
        print("  • لوحة التحكم تعمل بدون أخطاء")
        print("  • المراسلات الداخلية تعمل بدون أخطاء")
        print("  • فلاتر المراسلات تعمل بدون أخطاء")
        print("  • تفاصيل الجهات تعمل بدون أخطاء")
        print("\n🌐 النظام جاهز للاستخدام:")
        print("http://localhost:8585")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض اختبارات إصلاح AppenderQuery!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم على المنفذ 8585")
        print("  • صحة قاعدة البيانات")
        print("  • عدم وجود أخطاء في الكود")
        sys.exit(1)
