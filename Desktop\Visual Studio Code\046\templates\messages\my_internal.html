{% extends "base.html" %}

{% block title %}مراسلاتي الداخلية - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-tie me-2"></i>
                مراسلاتي الداخلية
            </h1>
            <div>
                <a href="{{ url_for('messages.internal_messages') }}" class="btn btn-outline-primary">
                    <i class="fas fa-building me-2"></i>
                    جميع المراسلات الداخلية
                </a>
                <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    مراسلة جديدة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ messages.total }}</h3>
                <p class="mb-0">إجمالي المكلف بها</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ messages.items|selectattr('requires_response', 'equalto', true)|selectattr('responses', 'equalto', [])|list|length }}</h3>
                <p class="mb-0">بانتظار ردي</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h3>{{ messages.items|selectattr('is_urgent', 'equalto', true)|list|length }}</h3>
                <p class="mb-0">عاجلة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h3>{{ messages.items|selectattr('due_date')|selectattr('due_date', 'lt', moment().date())|list|length }}</h3>
                <p class="mb-0">متأخرة</p>
            </div>
        </div>
    </div>
</div>

<!-- Messages Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    المراسلات المكلف بها
                </h5>
            </div>
            <div class="card-body">
                {% if messages.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>الموضوع</th>
                                <th>من القسم</th>
                                <th>المرسل</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in messages.items %}
                            <tr class="{% if message.is_urgent %}table-danger{% elif message.due_date and message.due_date < moment().date() %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ message.registration_number }}</strong>
                                    {% if message.is_urgent %}
                                        <i class="fas fa-exclamation-triangle text-danger ms-1" title="عاجل"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject[:50] }}{% if message.subject|length > 50 %}...{% endif %}
                                    </a>
                                </td>
                                <td>{{ message.from_department or '-' }}</td>
                                <td>{{ message.creator.name }}</td>
                                <td>
                                    {% if message.due_date %}
                                        {{ message.due_date.strftime('%Y-%m-%d') }}
                                        {% if message.due_date < moment().date() %}
                                            <i class="fas fa-clock text-danger ms-1" title="متأخر"></i>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if message.requires_response %}
                                        {% if message.responses %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>تم الرد
                                            </span>
                                        {% else %}
                                            {% if message.due_date and message.due_date < moment().date() %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>متأخر
                                                </span>
                                            {% else %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>بانتظار الرد
                                                </span>
                                            {% endif %}
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-info-circle me-1"></i>للعلم
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if message.requires_response and not message.responses %}
                                        <a href="{{ url_for('messages.respond_internal', id=message.id) }}" 
                                           class="btn btn-success" title="رد">
                                            <i class="fas fa-reply"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('messages.download_pdf', id=message.id) }}" 
                                           class="btn btn-outline-secondary" title="تحميل PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if messages.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if messages.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('messages.my_internal_messages', page=messages.prev_num) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in messages.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != messages.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('messages.my_internal_messages', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if messages.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('messages.my_internal_messages', page=messages.next_num) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-inbox fa-4x mb-3"></i>
                    <h4>لا توجد مراسلات مكلف بها</h4>
                    <p>لم يتم تكليفك بأي مراسلات داخلية حتى الآن</p>
                    <a href="{{ url_for('messages.internal_messages') }}" class="btn btn-primary">
                        <i class="fas fa-building me-2"></i>
                        عرض جميع المراسلات الداخلية
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('messages.internal_messages', status='pending') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-clock me-2"></i>
                            المراسلات المعلقة
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('messages.internal_messages', status='urgent') }}" class="btn btn-danger btn-lg w-100">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            المراسلات العاجلة
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus me-2"></i>
                            مراسلة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}
.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}
</style>
{% endblock %}
