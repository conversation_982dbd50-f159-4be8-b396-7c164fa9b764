#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل النظام المحسن والمبسط
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def print_banner():
    """طباعة شعار النظام"""
    print("=" * 70)
    print("🏢 نظام المراسلات الإلكترونية")
    print("📧 Electronic Correspondence System")
    print("=" * 70)

def check_and_install_requirements():
    """التحقق من وتثبيت المتطلبات"""
    print("📦 التحقق من المتطلبات...")
    
    required_packages = [
        'flask',
        'flask-sqlalchemy', 
        'flask-login',
        'werkzeug',
        'python-docx',
        'fpdf',
        'PyPDF2',
        'requests'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').replace('flask_', 'flask.ext.'))
        except ImportError:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing.append(package)
    
    if missing:
        print(f"⚠️ حزم مفقودة: {', '.join(missing)}")
        print("📥 تثبيت الحزم المفقودة...")
        for package in missing:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                                    stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"✅ تم تثبيت {package}")
            except:
                print(f"❌ فشل تثبيت {package}")
    else:
        print("✅ جميع المتطلبات متوفرة")

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    try:
        from app import create_app
        from models import db, User, UserRole
        
        app = create_app()
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            
            # التحقق من وجود مستخدم admin
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    name='مدير النظام',
                    email='<EMAIL>',
                    role=UserRole.ADMIN,
                    is_active=True
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء مستخدم admin")
            else:
                print("✅ مستخدم admin موجود")
                
        print("✅ قاعدة البيانات جاهزة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 إنشاء المجلدات...")
    
    directories = [
        'static',
        'static/css',
        'static/js', 
        'static/images',
        'uploads',
        'templates',
        'routes'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ المجلدات جاهزة")

def check_port(port=8585):
    """التحقق من توفر المنفذ"""
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"⚠️ المنفذ {port} محجوز")
            return False
        else:
            print(f"✅ المنفذ {port} متاح")
            return True
            
    except:
        return True

def start_server():
    """تشغيل الخادم"""
    print("🚀 تشغيل الخادم...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        # العثور على منفذ متاح
        port = 8585
        while not check_port(port) and port < 8600:
            port += 1
        
        print("\n" + "=" * 70)
        print("✅ النظام جاهز!")
        print(f"🌐 الرابط: http://localhost:{port}")
        print("🔐 تسجيل الدخول:")
        print("   👤 المستخدم: admin")
        print("   🔐 كلمة المرور: admin123")
        print("=" * 70)
        print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
        print("=" * 70)
        
        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {str(e)}")
        print("\n💡 حلول مقترحة:")
        print("  • تأكد من تثبيت Python 3.8+")
        print("  • تأكد من تثبيت جميع المتطلبات")
        print("  • تأكد من عدم حجز المنفذ")
        print("  • تشغيل الأمر كمدير")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    try:
        # التحقق من المتطلبات
        check_and_install_requirements()
        
        # إنشاء المجلدات
        create_directories()
        
        # إعداد قاعدة البيانات
        if not setup_database():
            print("❌ فشل في إعداد قاعدة البيانات")
            return
        
        # تشغيل الخادم
        start_server()
        
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        print("\n🔧 تشغيل أداة التشخيص:")
        print("  python fix_system_issues.py")

if __name__ == "__main__":
    main()
