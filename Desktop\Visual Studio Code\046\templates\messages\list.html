{% extends "base.html" %}

{% block title %}قائمة الرسائل - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-envelope me-2"></i>
                قائمة الرسائل
            </h1>
            <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                رسالة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">نوع الرسالة</label>
                        <select name="type" class="form-select">
                            <option value="">جميع الأنواع</option>
                            <option value="incoming" {% if request.args.get('type') == 'incoming' %}selected{% endif %}>واردة</option>
                            <option value="outgoing" {% if request.args.get('type') == 'outgoing' %}selected{% endif %}>صادرة</option>
                            <option value="internal" {% if request.args.get('type') == 'internal' %}selected{% endif %}>داخلية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">السنة</label>
                        <select name="year" class="form-select">
                            <option value="">جميع السنوات</option>
                            {% for year in range(2020, 2030) %}
                            <option value="{{ year }}" {% if request.args.get('year')|int == year %}selected{% endif %}>{{ year }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" name="q" class="form-control" placeholder="رقم التسجيل، الموضوع، أو الجهة" value="{{ request.args.get('q', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ url_for('messages.list_messages') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Messages Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    {% if message_type %}
                        {% if message_type == 'incoming' %}الرسائل الواردة{% endif %}
                        {% if message_type == 'outgoing' %}الرسائل الصادرة{% endif %}
                        {% if message_type == 'internal' %}المراسلات الداخلية{% endif %}
                    {% else %}
                        جميع الرسائل
                    {% endif %}
                </h5>
                <div>
                    <button type="button" class="btn btn-danger btn-sm me-2" id="deleteSelectedBtn" style="display: none;">
                        <i class="fas fa-trash me-1"></i>
                        حذف المحدد
                    </button>
                    <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        رسالة جديدة
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if messages.items %}
                <div class="table-responsive">
                    <form id="bulkDeleteForm" method="POST" action="{{ url_for('messages.bulk_delete') }}">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>رقم التسجيل</th>
                                    <th>النوع</th>
                                    <th>الموضوع</th>
                                    <th>الجهة</th>
                                    <th>التاريخ</th>
                                    <th>المرفقات</th>
                                    <th>الأولوية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        <tbody>
                            {% for message in messages.items %}
                            <tr>
                                <td>
                                    <input type="checkbox" name="message_ids" value="{{ message.id }}" class="form-check-input message-checkbox">
                                </td>
                                <td>
                                    <strong>{{ message.registration_number }}</strong>
                                </td>
                                <td>
                                    {% if message.is_incoming() %}
                                        <span class="badge bg-success">واردة</span>
                                    {% elif message.is_outgoing() %}
                                        <span class="badge bg-info">صادرة</span>
                                    {% else %}
                                        <span class="badge bg-warning">داخلية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject[:50] }}{% if message.subject|length > 50 %}...{% endif %}
                                    </a>
                                </td>
                                <td>{{ message.destination[:30] }}{% if message.destination|length > 30 %}...{% endif %}</td>
                                <td>{{ message.message_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if message.attachment_name %}
                                        <i class="fas fa-paperclip text-success" title="{{ message.attachment_name }}"></i>
                                    {% else %}
                                        <i class="fas fa-minus text-muted"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if message.priority == 'urgent' %}
                                        <span class="badge bg-danger">عاجل</span>
                                    {% elif message.priority == 'high' %}
                                        <span class="badge bg-warning">مهم</span>
                                    {% else %}
                                        <span class="badge bg-secondary">عادي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if current_user.is_manager() or message.created_by == current_user.id %}
                                        <a href="{{ url_for('messages.edit_message', id=message.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('messages.download_pdf', id=message.id) }}" 
                                           class="btn btn-outline-success" title="تحميل PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    </form>
                </div>
                
                <!-- Pagination -->
                {% if messages.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if messages.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('messages.list_messages', page=messages.prev_num, **request.args) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in messages.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != messages.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('messages.list_messages', page=page_num, **request.args) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if messages.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('messages.list_messages', page=messages.next_num, **request.args) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-inbox fa-4x mb-3"></i>
                    <h4>لا توجد رسائل</h4>
                    <p>لم يتم العثور على أي رسائل تطابق معايير البحث</p>
                    <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء رسالة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
    const bulkDeleteForm = document.getElementById('bulkDeleteForm');

    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            messageCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            toggleDeleteButton();
        });
    }

    // Handle individual checkboxes
    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.message-checkbox:checked').length;
            const totalCount = messageCheckboxes.length;

            // Update select all checkbox state
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === totalCount;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            }

            toggleDeleteButton();
        });
    });

    // Toggle delete button visibility
    function toggleDeleteButton() {
        const checkedCount = document.querySelectorAll('.message-checkbox:checked').length;
        if (deleteSelectedBtn) {
            deleteSelectedBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
        }
    }

    // Handle bulk delete
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', function() {
            const checkedCount = document.querySelectorAll('.message-checkbox:checked').length;
            if (checkedCount === 0) {
                alert('يرجى اختيار رسالة واحدة على الأقل للحذف');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف ${checkedCount} رسالة؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
                bulkDeleteForm.submit();
            }
        });
    }
});
</script>
{% endblock %}
