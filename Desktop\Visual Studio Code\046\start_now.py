#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🚀 بدء تشغيل النظام...")

try:
    print("📦 تحميل Flask...")
    from flask import Flask
    print("✅ Flask جاهز")
    
    print("📱 تحميل التطبيق...")
    from app import create_app
    print("✅ التطبيق جاهز")
    
    print("🔧 إنشاء التطبيق...")
    app = create_app()
    print("✅ تم إنشاء التطبيق")
    
    print("=" * 50)
    print("✅ النظام جاهز للتشغيل!")
    print("🌐 الرابط: http://localhost:8585")
    print("👤 المستخدم: admin")
    print("🔐 كلمة المرور: admin123")
    print("=" * 50)
    print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
    print("=" * 50)
    
    # تشغيل الخادم
    app.run(
        host='127.0.0.1',
        port=8585,
        debug=False,
        use_reloader=False
    )
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 حل المشكلة:")
    print("   pip install flask flask-sqlalchemy flask-login")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    print("💡 تحقق من:")
    print("   • ملفات النظام موجودة")
    print("   • قاعدة البيانات سليمة")
    print("   • المنفذ 8585 غير محجوز")

input("\n⏸️ اضغط Enter للخروج...")
