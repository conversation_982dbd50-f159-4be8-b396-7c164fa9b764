# 🔧 دليل حل مشاكل النظام

## 🎯 المشاكل الشائعة وحلولها

### ❌ **مشكلة: "النظام لا يشتغل"**

#### 🔍 **التشخيص:**
```bash
python diagnose.py
```

#### 💡 **الحلول:**

**1. تحقق من Python:**
```bash
python --version
```
- يجب أن يكون Python 3.8 أو أحدث

**2. تحقق من المكتبات:**
```bash
pip list | findstr flask
```

**3. ثبت المكتبات المفقودة:**
```bash
pip install flask flask-sqlalchemy flask-login
```

**4. تحقق من الملفات:**
- تأكد من وجود `app.py`
- تأكد من وجود `models.py`
- تأكد من وجود مجلد `templates`

---

### ❌ **مشكلة: "خطأ في الاستيراد"**

#### 💡 **الحل:**
```bash
# إعادة تثبيت المكتبات
pip uninstall flask flask-sqlalchemy flask-login
pip install flask flask-sqlalchemy flask-login

# أو تحديث pip
python -m pip install --upgrade pip
pip install flask flask-sqlalchemy flask-login
```

---

### ❌ **مشكلة: "المنفذ محجوز"**

#### 🔍 **التحقق:**
```bash
netstat -an | findstr :8585
```

#### 💡 **الحل:**
1. **إيقاف التطبيق الآخر**
2. **أو تغيير المنفذ في الكود:**
   ```python
   app.run(host='127.0.0.1', port=8586)
   ```

---

### ❌ **مشكلة: "خطأ في قاعدة البيانات"**

#### 💡 **الحل:**
```bash
# إنشاء قاعدة بيانات جديدة
python create_fresh_db.py

# أو حذف القديمة وإعادة إنشائها
del correspondence.db
python init_database.py
```

---

### ❌ **مشكلة: "الملفات لا تظهر"**

#### 💡 **الحل:**
1. **تحقق من مجلد uploads:**
   ```bash
   dir uploads
   ```

2. **إنشاء المجلد إذا لم يكن موجوداً:**
   ```bash
   mkdir uploads
   ```

3. **تحقق من الصلاحيات:**
   - تأكد من صلاحيات الكتابة في المجلد

---

## 🚀 طرق التشغيل المختلفة

### 🎯 **الطريقة الأولى: التشغيل المباشر**
```bash
python تشغيل_النظام.py
```

### 🎯 **الطريقة الثانية: ملف Batch (Windows)**
```bash
تشغيل.bat
```

### 🎯 **الطريقة الثالثة: التشخيص والتشغيل**
```bash
python diagnose.py
```

### 🎯 **الطريقة الرابعة: التشغيل التقليدي**
```bash
python app.py
```

---

## 🔍 أدوات التشخيص

### 📊 **فحص شامل:**
```bash
python diagnose.py
```

### 🧪 **اختبار المكتبات:**
```bash
python -c "import flask, flask_sqlalchemy, flask_login; print('جميع المكتبات متوفرة')"
```

### 📁 **فحص الملفات:**
```bash
dir app.py models.py
dir templates
dir static
```

### 🌐 **اختبار الاتصال:**
```bash
curl http://127.0.0.1:8585
```

---

## 💡 نصائح مهمة

### ✅ **للتشغيل الناجح:**
1. **استخدم Python 3.8+**
2. **ثبت جميع المكتبات المطلوبة**
3. **تأكد من وجود جميع الملفات**
4. **شغل من المجلد الصحيح**
5. **تأكد من عدم حجز المنفذ**

### 🔒 **للأمان:**
1. **غير كلمة مرور admin**
2. **استخدم HTTPS في الإنتاج**
3. **احم قاعدة البيانات**
4. **راقب الملفات المرفوعة**

### 📈 **للأداء:**
1. **استخدم خادم إنتاج (Gunicorn)**
2. **فعل التخزين المؤقت**
3. **ضغط الملفات الكبيرة**
4. **راقب استخدام الذاكرة**

---

## 🆘 الحصول على المساعدة

### 📞 **خطوات الدعم:**
1. **شغل التشخيص:** `python diagnose.py`
2. **احفظ رسائل الخطأ**
3. **تحقق من هذا الدليل**
4. **جرب الحلول المقترحة**

### 📋 **معلومات مفيدة للدعم:**
- إصدار Python
- إصدار Flask
- نظام التشغيل
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

---

## ✅ قائمة التحقق السريع

### قبل التشغيل:
- [ ] Python 3.8+ مثبت
- [ ] Flask مثبت
- [ ] ملف app.py موجود
- [ ] ملف models.py موجود
- [ ] مجلد templates موجود
- [ ] المنفذ 8585 متاح

### بعد التشغيل:
- [ ] النظام يعمل على http://127.0.0.1:8585
- [ ] تسجيل الدخول يعمل (admin/admin123)
- [ ] الصفحات تحمل بدون أخطاء
- [ ] رفع الملفات يعمل
- [ ] عرض الملفات يعمل

---

## 🎉 النظام يعمل؟

إذا كان النظام يعمل بنجاح:

### 🌐 **الروابط المهمة:**
- **الرئيسية:** http://127.0.0.1:8585
- **الرسائل:** http://127.0.0.1:8585/messages
- **الملفات:** http://127.0.0.1:8585/messages/files
- **المستخدمين:** http://127.0.0.1:8585/users
- **الإعدادات:** http://127.0.0.1:8585/settings

### 🔐 **بيانات الدخول:**
- **المستخدم:** admin
- **كلمة المرور:** admin123

### 📄 **اختبار عرض الملفات:**
1. أنشئ رسالة جديدة
2. ارفع ملف (DOCX, PDF, صورة)
3. احفظ الرسالة
4. اضغط "عرض الملف" لمعاينة المرفق

**🎯 النظام جاهز للاستخدام الكامل!**
