#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قسم الإعدادات
"""

import requests
import sys
import subprocess
import time

def test_settings_system():
    """اختبار نظام الإعدادات"""
    
    print("⚙️ اختبار قسم الإعدادات")
    print("=" * 50)
    
    # تشغيل الخادم في الخلفية
    print("🚀 تشغيل الخادم...")
    try:
        server_process = subprocess.Popen([
            'python', '-c', 
            'from app import create_app; app = create_app(); app.run(host="0.0.0.0", port=8585, debug=False)'
        ])
        
        # انتظار تشغيل الخادم
        time.sleep(3)
        
        base_url = "http://localhost:8585"
        
        # إنشاء جلسة
        session = requests.Session()
        
        # تسجيل الدخول
        print("🔐 تسجيل الدخول...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        # اختبار الصفحة الرئيسية للإعدادات
        print("\n⚙️ اختبار الصفحة الرئيسية للإعدادات...")
        response = session.get(f"{base_url}/settings")
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية للإعدادات تعمل")
            
            content = response.text
            if 'إعدادات النظام' in content:
                print("✅ عنوان الصفحة صحيح")
            
            if 'إعدادات الألوان' in content:
                print("✅ قسم الألوان موجود")
                
            if 'إعدادات العرض' in content:
                print("✅ قسم العرض موجود")
                
        else:
            print(f"❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
            return False
        
        # اختبار صفحة إعدادات الألوان
        print("\n🎨 اختبار صفحة إعدادات الألوان...")
        response = session.get(f"{base_url}/settings/colors")
        if response.status_code == 200:
            print("✅ صفحة إعدادات الألوان تعمل")
            
            content = response.text
            if 'إعدادات الألوان' in content:
                print("✅ محتوى الصفحة صحيح")
            
            if 'اللون الأساسي' in content:
                print("✅ حقول الألوان موجودة")
                
        else:
            print(f"❌ فشل في الوصول لصفحة الألوان: {response.status_code}")
            return False
        
        # اختبار صفحة إعدادات العرض
        print("\n📱 اختبار صفحة إعدادات العرض...")
        response = session.get(f"{base_url}/settings/display")
        if response.status_code == 200:
            print("✅ صفحة إعدادات العرض تعمل")
            
            content = response.text
            if 'إعدادات العرض' in content:
                print("✅ محتوى الصفحة صحيح")
            
            if 'عرض الأيام كأرقام فقط' in content:
                print("✅ إعداد الأيام موجود")
                
        else:
            print(f"❌ فشل في الوصول لصفحة العرض: {response.status_code}")
            return False
        
        # اختبار صفحة الإعدادات العامة
        print("\n🏢 اختبار صفحة الإعدادات العامة...")
        response = session.get(f"{base_url}/settings/general")
        if response.status_code == 200:
            print("✅ صفحة الإعدادات العامة تعمل")
            
            content = response.text
            if 'الإعدادات العامة' in content:
                print("✅ محتوى الصفحة صحيح")
                
        else:
            print(f"❌ فشل في الوصول للصفحة العامة: {response.status_code}")
            return False
        
        # اختبار API الألوان
        print("\n🔌 اختبار API الألوان...")
        response = session.get(f"{base_url}/settings/api/colors")
        if response.status_code == 200:
            print("✅ API الألوان يعمل")
            
            try:
                colors = response.json()
                if 'primary_color' in colors:
                    print(f"✅ اللون الأساسي: {colors['primary_color']}")
                if len(colors) >= 10:
                    print(f"✅ تم تحميل {len(colors)} لون")
            except:
                print("⚠️ مشكلة في تحليل JSON")
                
        else:
            print(f"❌ فشل في API الألوان: {response.status_code}")
            return False
        
        # اختبار API العرض
        print("\n📊 اختبار API العرض...")
        response = session.get(f"{base_url}/settings/api/display")
        if response.status_code == 200:
            print("✅ API العرض يعمل")
            
            try:
                display = response.json()
                if 'show_days_only' in display:
                    print(f"✅ عرض الأيام كأرقام: {display['show_days_only']}")
                if 'date_format' in display:
                    print(f"✅ تنسيق التاريخ: {display['date_format']}")
            except:
                print("⚠️ مشكلة في تحليل JSON")
                
        else:
            print(f"❌ فشل في API العرض: {response.status_code}")
            return False
        
        # اختبار تحديث إعدادات العرض
        print("\n🔄 اختبار تحديث إعدادات العرض...")
        update_data = {
            'show_days_only': 'on',
            'date_format': 'dd/mm/yyyy',
            'time_format': '24h',
            'items_per_page': '20'
        }
        
        response = session.post(f"{base_url}/settings/display", data=update_data)
        if response.status_code == 200 or response.status_code == 302:
            print("✅ تحديث إعدادات العرض يعمل")
        else:
            print(f"⚠️ مشكلة في تحديث العرض: {response.status_code}")
        
        # اختبار الداشبورد مع الإعدادات الجديدة
        print("\n📊 اختبار الداشبورد مع الإعدادات الجديدة...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ الداشبورد يعمل مع الإعدادات الجديدة")
            
            content = response.text
            # البحث عن عرض الأيام الجديد
            if 'fw-bold fs-5' in content:
                print("✅ عرض الأيام كأرقام فقط يعمل")
            
        else:
            print(f"⚠️ مشكلة في الداشبورد: {response.status_code}")
        
        print("\n🎉 تم اكتمال اختبار قسم الإعدادات!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False
    
    finally:
        # إيقاف الخادم
        try:
            server_process.terminate()
            server_process.wait(timeout=5)
        except:
            try:
                server_process.kill()
            except:
                pass

if __name__ == "__main__":
    success = test_settings_system()
    
    if success:
        print("\n✅ قسم الإعدادات يعمل بشكل مثالي!")
        print("\n📋 الميزات المختبرة:")
        print("  ✅ الصفحة الرئيسية للإعدادات")
        print("  ✅ إعدادات الألوان مع المعاينة")
        print("  ✅ إعدادات العرض والتاريخ")
        print("  ✅ الإعدادات العامة للنظام")
        print("  ✅ API الألوان والعرض")
        print("  ✅ تحديث الإعدادات")
        print("  ✅ عرض الأيام كأرقام فقط")
        print("\n🌐 الروابط المتاحة:")
        print("  ⚙️ الإعدادات: http://localhost:8585/settings")
        print("  🎨 الألوان: http://localhost:8585/settings/colors")
        print("  📱 العرض: http://localhost:8585/settings/display")
        print("  🏢 العامة: http://localhost:8585/settings/general")
        print("\n🎯 الميزات الجديدة:")
        print("  📊 عرض الأيام كأرقام فقط في الرسائل الحديثة")
        print("  🎨 تخصيص ألوان النظام بالكامل")
        print("  📱 تحكم في تنسيق التاريخ والوقت")
        print("  🏢 إدارة معلومات النظام")
        print("  👁️ معاينة مباشرة للتغييرات")
        print("  🔄 إعادة تعيين للقيم الافتراضية")
        sys.exit(0)
    else:
        print("\n❌ فشل في اختبار قسم الإعدادات!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم")
        print("  • إنشاء جدول الإعدادات")
        print("  • صحة قاعدة البيانات")
        print("  • تسجيل مسارات الإعدادات")
        sys.exit(1)
