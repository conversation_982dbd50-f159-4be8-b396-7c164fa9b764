from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import enum

db = SQLAlchemy()

# جدول ربط الأدوار بالصلاحيات
role_permissions = db.Table('role_permissions',
    db.<PERSON>umn('role_id', db.Integer, db.<PERSON><PERSON>('roles.id'), primary_key=True),
    db.<PERSON>('permission_id', db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('permissions.id'), primary_key=True)
)

# جدول ربط المستخدمين بالصلاحيات الإضافية
user_permissions = db.Table('user_permissions',
    db.Column('user_id', db.Integer, db.<PERSON>ey('users.id'), primary_key=True),
    db.Column('permission_id', db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('permissions.id'), primary_key=True)
)

class UserRole(enum.Enum):
    EMPLOYEE = "EMPLOYEE"          # موظف عادي
    SECRETARY = "SECRETARY"        # سكرتير
    SUPERVISOR = "SUPERVISOR"      # مشرف
    MANAGER = "MANAGER"            # مدير
    DEPARTMENT_HEAD = "DEPARTMENT_HEAD"  # رئيس قسم
    DIRECTOR = "DIRECTOR"          # مدير عام
    ADMIN = "ADMIN"                # مدير النظام

class PermissionType(enum.Enum):
    # صلاحيات الرسائل
    CREATE_MESSAGE = "create_message"
    VIEW_MESSAGE = "view_message"
    EDIT_MESSAGE = "edit_message"
    DELETE_MESSAGE = "delete_message"
    APPROVE_MESSAGE = "approve_message"
    ARCHIVE_MESSAGE = "archive_message"

    # صلاحيات المستخدمين
    CREATE_USER = "create_user"
    VIEW_USER = "view_user"
    EDIT_USER = "edit_user"
    DELETE_USER = "delete_user"
    MANAGE_ROLES = "manage_roles"

    # صلاحيات الأقسام
    CREATE_DEPARTMENT = "create_department"
    VIEW_DEPARTMENT = "view_department"
    EDIT_DEPARTMENT = "edit_department"
    DELETE_DEPARTMENT = "delete_department"

    # صلاحيات النظام
    VIEW_REPORTS = "view_reports"
    MANAGE_SYSTEM = "manage_system"
    VIEW_LOGS = "view_logs"
    BACKUP_SYSTEM = "backup_system"

class MessageType(enum.Enum):
    INCOMING = "incoming"
    OUTGOING = "outgoing"
    INTERNAL = "internal"

class ApprovalStatus(enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    AUTO_APPROVED = "auto_approved"

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.EMPLOYEE)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'))  # الدور المخصص
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))  # القسم التابع له المستخدم

    # حقول التوقيع الإلكتروني
    signature_type = db.Column(db.String(20))  # 'digital', 'image', 'none'
    signature_data = db.Column(db.Text)  # بيانات التوقيع أو مسار الملف
    signature_filename = db.Column(db.String(255))  # اسم ملف التوقيع الأصلي
    signature_created_at = db.Column(db.DateTime)  # تاريخ إنشاء التوقيع

    # حقول مسؤول الاعتماد
    approval_manager_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # مسؤول الاعتماد
    can_approve_others = db.Column(db.Boolean, default=False)  # يمكنه اعتماد رسائل الآخرين

    # Relationships
    created_messages = db.relationship('Message', foreign_keys='Message.created_by', backref='creator', lazy='dynamic')
    assigned_messages = db.relationship('Message', foreign_keys='Message.assigned_to', backref='assigned_user', lazy='dynamic')
    approved_messages = db.relationship('Message', foreign_keys='Message.approved_by', backref='approver', lazy='dynamic')
    department = db.relationship('Department', foreign_keys=[department_id], backref='employees')
    custom_role = db.relationship('Role', foreign_keys=[role_id], backref='users')
    additional_permissions = db.relationship('Permission', secondary=user_permissions,
                                           backref=db.backref('users', lazy='dynamic'))

    # علاقة مسؤول الاعتماد
    approval_manager = db.relationship('User', remote_side=[id], backref='managed_users')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == UserRole.ADMIN

    def is_director(self):
        return self.role == UserRole.DIRECTOR

    def is_department_head(self):
        return self.role == UserRole.DEPARTMENT_HEAD

    def is_manager(self):
        return self.role == UserRole.MANAGER

    def is_supervisor(self):
        return self.role == UserRole.SUPERVISOR

    def is_secretary(self):
        return self.role == UserRole.SECRETARY
    
    def can_manage_users(self):
        return self.role in [UserRole.ADMIN, UserRole.DIRECTOR, UserRole.DEPARTMENT_HEAD, UserRole.MANAGER, UserRole.SECRETARY]

    def can_delete_messages(self):
        return self.role in [UserRole.ADMIN, UserRole.DIRECTOR, UserRole.DEPARTMENT_HEAD, UserRole.MANAGER]

    # نظام الصلاحيات المتقدم
    def has_permission(self, permission_name):
        """التحقق من وجود صلاحية معينة للمستخدم"""
        # التحقق من الصلاحيات الإضافية المباشرة
        if any(p.name == permission_name for p in self.additional_permissions):
            return True

        # التحقق من صلاحيات الدور المخصص
        if self.custom_role and self.custom_role.has_permission(permission_name):
            return True

        # التحقق من الصلاحيات الافتراضية حسب الدور
        return self._has_default_permission(permission_name)

    def _has_default_permission(self, permission_name):
        """الصلاحيات الافتراضية حسب الدور"""
        default_permissions = {
            UserRole.ADMIN: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user', 'delete_user', 'manage_roles',
                'create_department', 'view_department', 'edit_department', 'delete_department',
                'view_reports', 'manage_system', 'view_logs', 'backup_system'
            ],
            UserRole.DIRECTOR: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user', 'delete_user',
                'create_department', 'view_department', 'edit_department', 'delete_department',
                'view_reports', 'manage_system'
            ],
            UserRole.DEPARTMENT_HEAD: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user',
                'view_department', 'edit_department',
                'view_reports'
            ],
            UserRole.MANAGER: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user',
                'view_department', 'edit_department',
                'view_reports'
            ],
            UserRole.SUPERVISOR: [
                'create_message', 'view_message', 'edit_message', 'approve_message', 'archive_message',
                'view_user', 'edit_user',
                'view_department'
            ],
            UserRole.SECRETARY: [
                'create_message', 'view_message', 'edit_message', 'archive_message',
                'view_user', 'edit_user',
                'view_department'
            ],
            UserRole.EMPLOYEE: [
                'create_message', 'view_message', 'edit_message',
                'view_user', 'view_department'
            ]
        }

        user_permissions = default_permissions.get(self.role, [])
        return permission_name in user_permissions

    def add_permission(self, permission):
        """إضافة صلاحية إضافية للمستخدم"""
        if permission not in self.additional_permissions:
            self.additional_permissions.append(permission)

    def remove_permission(self, permission):
        """إزالة صلاحية إضافية من المستخدم"""
        if permission in self.additional_permissions:
            self.additional_permissions.remove(permission)

    def get_all_permissions(self):
        """الحصول على جميع صلاحيات المستخدم"""
        permissions = set()

        # الصلاحيات الافتراضية
        default_permissions = {
            UserRole.ADMIN: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user', 'delete_user', 'manage_roles',
                'create_department', 'view_department', 'edit_department', 'delete_department',
                'view_reports', 'manage_system', 'view_logs', 'backup_system'
            ],
            UserRole.DIRECTOR: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user', 'delete_user',
                'create_department', 'view_department', 'edit_department', 'delete_department',
                'view_reports', 'manage_system'
            ],
            UserRole.DEPARTMENT_HEAD: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user',
                'view_department', 'edit_department',
                'view_reports'
            ],
            UserRole.MANAGER: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user',
                'view_department', 'edit_department',
                'view_reports'
            ],
            UserRole.SUPERVISOR: [
                'create_message', 'view_message', 'edit_message', 'approve_message', 'archive_message',
                'view_user', 'edit_user',
                'view_department'
            ],
            UserRole.SECRETARY: [
                'create_message', 'view_message', 'edit_message', 'archive_message',
                'view_user', 'edit_user',
                'view_department'
            ],
            UserRole.EMPLOYEE: [
                'create_message', 'view_message', 'edit_message',
                'view_user', 'view_department'
            ]
        }

        permissions.update(default_permissions.get(self.role, []))

        # صلاحيات الدور المخصص
        if self.custom_role:
            permissions.update([p.name for p in self.custom_role.permissions])

        # الصلاحيات الإضافية
        permissions.update([p.name for p in self.additional_permissions])

        return list(permissions)

    # دوال التوقيع الإلكتروني
    def has_signature(self):
        """التحقق من وجود توقيع للمستخدم"""
        return self.signature_type and self.signature_type != 'none' and self.signature_data

    def get_signature_data(self):
        """الحصول على بيانات التوقيع"""
        if not self.has_signature():
            return None

        if self.signature_type == 'digital':
            try:
                import json
                return json.loads(self.signature_data)
            except:
                return None
        elif self.signature_type == 'image':
            return self.signature_data

        return None

    def set_digital_signature(self, signature_info):
        """حفظ التوقيع الرقمي"""
        import json
        self.signature_type = 'digital'
        self.signature_data = json.dumps(signature_info)
        self.signature_created_at = datetime.utcnow()

    def set_image_signature(self, file_path, filename):
        """حفظ صورة التوقيع"""
        self.signature_type = 'image'
        self.signature_data = file_path
        self.signature_filename = filename
        self.signature_created_at = datetime.utcnow()

    def remove_signature(self):
        """إزالة التوقيع"""
        self.signature_type = 'none'
        self.signature_data = None
        self.signature_filename = None
        self.signature_created_at = None

    def __repr__(self):
        return f'<User {self.username}>'

class Message(db.Model):
    __tablename__ = 'messages'
    
    id = db.Column(db.Integer, primary_key=True)
    message_type = db.Column(db.Enum(MessageType), nullable=False)
    registration_number = db.Column(db.String(50), unique=True, nullable=False)
    destination = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.Text, nullable=False)
    content = db.Column(db.Text)
    date_created = db.Column(db.DateTime, default=datetime.utcnow)
    message_date = db.Column(db.Date, nullable=False)
    attachment_path = db.Column(db.String(500))
    attachment_name = db.Column(db.String(200))
    
    # Foreign keys
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Additional fields for organization
    year = db.Column(db.Integer, nullable=False)
    department = db.Column(db.String(100))
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    status = db.Column(db.String(20), default='active')    # active, archived, deleted

    # Internal message specific fields
    from_department = db.Column(db.String(100))  # للمراسلات الداخلية
    to_department = db.Column(db.String(100))    # للمراسلات الداخلية
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))  # المكلف بالرد
    due_date = db.Column(db.Date)  # تاريخ الاستحقاق
    is_urgent = db.Column(db.Boolean, default=False)  # عاجل
    requires_response = db.Column(db.Boolean, default=False)  # يتطلب رد

    # Approval system fields
    requires_approval = db.Column(db.Boolean, default=False)  # يتطلب اعتماد
    approval_status = db.Column(db.Enum(ApprovalStatus), default=ApprovalStatus.PENDING)  # حالة الاعتماد
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # معتمد من قبل
    approval_date = db.Column(db.DateTime)  # تاريخ الاعتماد
    approval_notes = db.Column(db.Text)  # ملاحظات الاعتماد
    
    def __init__(self, **kwargs):
        super(Message, self).__init__(**kwargs)
        if self.message_date:
            self.year = self.message_date.year
    
    def get_attachment_url(self):
        if self.attachment_path:
            return f'/uploads/{self.attachment_path}'
        return None
    
    def is_incoming(self):
        return self.message_type == MessageType.INCOMING
    
    def is_outgoing(self):
        return self.message_type == MessageType.OUTGOING

    def is_internal(self):
        return self.message_type == MessageType.INTERNAL

    def is_approved(self):
        return self.approval_status == ApprovalStatus.APPROVED

    def is_pending_approval(self):
        return self.approval_status == ApprovalStatus.PENDING and self.requires_approval

    def can_be_approved_by(self, user):
        """Check if user can approve this message"""
        if not self.requires_approval:
            return False
        if self.approval_status != ApprovalStatus.PENDING:
            return False
        # All management roles can approve
        return (user.is_admin() or user.is_director() or user.is_department_head() or
                user.is_manager() or user.is_supervisor() or user.is_secretary())

    def approve(self, user, notes=None):
        """Approve the message"""
        if self.can_be_approved_by(user):
            self.approval_status = ApprovalStatus.APPROVED
            self.approved_by = user.id
            self.approval_date = datetime.utcnow()
            self.approval_notes = notes
            return True
        return False

    def reject(self, user, notes=None):
        """Reject the message"""
        if self.can_be_approved_by(user):
            self.approval_status = ApprovalStatus.REJECTED
            self.approved_by = user.id
            self.approval_date = datetime.utcnow()
            self.approval_notes = notes
            return True
        return False

    def get_days_since_creation(self):
        """حساب عدد الأيام منذ إنشاء الرسالة"""
        from datetime import datetime
        return (datetime.utcnow() - self.date_created).days

    def get_days_since_message_date(self):
        """حساب عدد الأيام منذ تاريخ الرسالة"""
        from datetime import datetime, date
        if isinstance(self.message_date, date):
            return (date.today() - self.message_date).days
        return 0

    def get_status_display(self):
        """عرض حالة الرسالة بشكل مقروء"""
        status_map = {
            'active': 'نشطة',
            'archived': 'مؤرشفة',
            'deleted': 'محذوفة'
        }
        return status_map.get(self.status, self.status)

    def get_priority_display(self):
        """عرض أولوية الرسالة بشكل مقروء"""
        priority_map = {
            'low': 'منخفضة',
            'normal': 'عادية',
            'high': 'عالية',
            'urgent': 'عاجلة'
        }
        return priority_map.get(self.priority, self.priority)

    def get_approval_status_display(self):
        """عرض حالة الاعتماد بشكل مقروء"""
        if not self.requires_approval:
            return 'لا يتطلب اعتماد'

        status_map = {
            ApprovalStatus.PENDING: 'بانتظار الاعتماد',
            ApprovalStatus.APPROVED: 'معتمدة',
            ApprovalStatus.REJECTED: 'مرفوضة',
            ApprovalStatus.AUTO_APPROVED: 'معتمدة تلقائياً'
        }
        return status_map.get(self.approval_status, str(self.approval_status))

    def get_priority_badge_class(self):
        """الحصول على فئة CSS للأولوية"""
        priority_classes = {
            'low': 'bg-secondary',
            'normal': 'bg-primary',
            'high': 'bg-warning',
            'urgent': 'bg-danger'
        }
        return priority_classes.get(self.priority, 'bg-primary')

    def get_status_badge_class(self):
        """الحصول على فئة CSS للحالة"""
        status_classes = {
            'active': 'bg-success',
            'archived': 'bg-info',
            'deleted': 'bg-danger'
        }
        return status_classes.get(self.status, 'bg-secondary')

    def get_approval_badge_class(self):
        """الحصول على فئة CSS لحالة الاعتماد"""
        if not self.requires_approval:
            return 'bg-secondary'

        approval_classes = {
            ApprovalStatus.PENDING: 'bg-warning',
            ApprovalStatus.APPROVED: 'bg-success',
            ApprovalStatus.REJECTED: 'bg-danger',
            ApprovalStatus.AUTO_APPROVED: 'bg-info'
        }
        return approval_classes.get(self.approval_status, 'bg-secondary')

    def __repr__(self):
        return f'<Message {self.registration_number}>'

class Department(db.Model):
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # مدير القسم

    # Relationships
    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')

    def __repr__(self):
        return f'<Department {self.name}>'



class Permission(db.Model):
    """نموذج الصلاحيات"""
    __tablename__ = 'permissions'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)  # اسم الصلاحية
    display_name = db.Column(db.String(200), nullable=False)  # الاسم المعروض
    description = db.Column(db.Text)  # وصف الصلاحية
    category = db.Column(db.String(50), nullable=False)  # فئة الصلاحية
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Permission {self.name}>'

class Role(db.Model):
    """نموذج الأدوار"""
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)  # اسم الدور
    display_name = db.Column(db.String(200), nullable=False)  # الاسم المعروض
    description = db.Column(db.Text)  # وصف الدور
    is_system_role = db.Column(db.Boolean, default=False)  # دور نظام لا يمكن حذفه
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    permissions = db.relationship('Permission', secondary=role_permissions,
                                backref=db.backref('roles', lazy='dynamic'))
    # users relationship will be defined in User model

    def has_permission(self, permission_name):
        """التحقق من وجود صلاحية معينة في الدور"""
        return any(p.name == permission_name for p in self.permissions)

    def add_permission(self, permission):
        """إضافة صلاحية للدور"""
        if permission not in self.permissions:
            self.permissions.append(permission)

    def remove_permission(self, permission):
        """إزالة صلاحية من الدور"""
        if permission in self.permissions:
            self.permissions.remove(permission)

    def __repr__(self):
        return f'<Role {self.name}>'

class InternalMessageResponse(db.Model):
    __tablename__ = 'internal_message_responses'

    id = db.Column(db.Integer, primary_key=True)
    original_message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)
    response_content = db.Column(db.Text, nullable=False)
    responded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    response_date = db.Column(db.DateTime, default=datetime.utcnow)
    attachment_path = db.Column(db.String(500))
    attachment_name = db.Column(db.String(200))
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected

    # Relationships
    original_message = db.relationship('Message', backref='responses')
    responder = db.relationship('User', backref='message_responses')

    def __repr__(self):
        return f'<InternalMessageResponse {self.id}>'

class ArchiveStatus(enum.Enum):
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"

class Archive(db.Model):
    """نموذج الأرشيف"""
    __tablename__ = 'archives'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)  # اسم الأرشيف
    description = db.Column(db.Text)  # وصف الأرشيف
    archive_type = db.Column(db.String(50), nullable=False)  # نوع الأرشيف (yearly, monthly, custom)
    year = db.Column(db.Integer)  # السنة (للأرشيف السنوي)
    month = db.Column(db.Integer)  # الشهر (للأرشيف الشهري)
    start_date = db.Column(db.Date)  # تاريخ البداية (للأرشيف المخصص)
    end_date = db.Column(db.Date)  # تاريخ النهاية (للأرشيف المخصص)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # العلاقات
    creator = db.relationship('User', backref='created_archives')

    def get_messages_count(self):
        """عدد الرسائل في الأرشيف"""
        return ArchivedMessage.query.filter_by(archive_id=self.id).count()

    def get_size_mb(self):
        """حجم الأرشيف بالميجابايت"""
        # حساب تقريبي بناءً على عدد الرسائل
        return self.get_messages_count() * 0.1  # تقدير 0.1 MB لكل رسالة

    def __repr__(self):
        return f'<Archive {self.name}>'

class Entity(db.Model):
    """نموذج الجهات والمؤسسات"""
    __tablename__ = 'entities'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)  # اسم الجهة
    name_en = db.Column(db.String(200))  # الاسم بالإنجليزية
    entity_type = db.Column(db.String(50), nullable=False)  # نوع الجهة (government, private, individual, etc.)
    category = db.Column(db.String(100))  # فئة الجهة (ministry, company, university, etc.)

    # معلومات الاتصال
    address = db.Column(db.Text)  # العنوان
    phone = db.Column(db.String(50))  # الهاتف
    fax = db.Column(db.String(50))  # الفاكس
    email = db.Column(db.String(100))  # البريد الإلكتروني
    website = db.Column(db.String(200))  # الموقع الإلكتروني

    # معلومات إضافية
    contact_person = db.Column(db.String(100))  # الشخص المسؤول
    contact_title = db.Column(db.String(100))  # منصب الشخص المسؤول
    contact_phone = db.Column(db.String(50))  # هاتف الشخص المسؤول
    contact_email = db.Column(db.String(100))  # بريد الشخص المسؤول

    # معلومات إدارية
    reference_number = db.Column(db.String(50), unique=True)  # الرقم المرجعي
    notes = db.Column(db.Text)  # ملاحظات
    is_active = db.Column(db.Boolean, default=True)  # نشط/غير نشط
    is_government = db.Column(db.Boolean, default=False)  # جهة حكومية
    is_frequent = db.Column(db.Boolean, default=False)  # جهة متكررة التراسل

    # معلومات التتبع
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    creator = db.relationship('User', backref='created_entities')

    def get_type_display(self):
        """عرض نوع الجهة بشكل مقروء"""
        type_map = {
            'government': 'جهة حكومية',
            'private': 'جهة خاصة',
            'individual': 'فرد',
            'ngo': 'منظمة غير ربحية',
            'international': 'جهة دولية',
            'academic': 'جهة أكاديمية',
            'other': 'أخرى'
        }
        return type_map.get(self.entity_type, self.entity_type)

    def get_category_display(self):
        """عرض فئة الجهة بشكل مقروء"""
        category_map = {
            'ministry': 'وزارة',
            'authority': 'هيئة',
            'company': 'شركة',
            'university': 'جامعة',
            'hospital': 'مستشفى',
            'bank': 'بنك',
            'embassy': 'سفارة',
            'court': 'محكمة',
            'municipality': 'بلدية',
            'other': 'أخرى'
        }
        return category_map.get(self.category, self.category)

    def get_status_badge_class(self):
        """الحصول على فئة CSS للحالة"""
        if not self.is_active:
            return 'bg-secondary'
        elif self.is_government:
            return 'bg-primary'
        elif self.is_frequent:
            return 'bg-success'
        else:
            return 'bg-info'

    def get_status_display(self):
        """عرض حالة الجهة"""
        if not self.is_active:
            return 'غير نشط'
        elif self.is_government:
            return 'جهة حكومية'
        elif self.is_frequent:
            return 'متكررة التراسل'
        else:
            return 'نشط'

    def __repr__(self):
        return f'<Entity {self.name}>'

class SystemSettings(db.Model):
    """نموذج إعدادات النظام"""
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(100), unique=True, nullable=False)  # مفتاح الإعداد
    setting_value = db.Column(db.Text)  # قيمة الإعداد
    setting_type = db.Column(db.String(50), default='text')  # نوع الإعداد (text, color, number, boolean)
    category = db.Column(db.String(50), default='general')  # فئة الإعداد
    display_name = db.Column(db.String(200))  # الاسم المعروض
    description = db.Column(db.Text)  # وصف الإعداد
    is_active = db.Column(db.Boolean, default=True)  # نشط/غير نشط

    # معلومات التتبع
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    updater = db.relationship('User', backref='updated_settings')

    @staticmethod
    def get_setting(key, default=None):
        """الحصول على قيمة إعداد"""
        setting = SystemSettings.query.filter_by(setting_key=key, is_active=True).first()
        return setting.setting_value if setting else default

    @staticmethod
    def set_setting(key, value, user_id=None):
        """تعيين قيمة إعداد"""
        setting = SystemSettings.query.filter_by(setting_key=key).first()
        if setting:
            setting.setting_value = str(value)
            setting.updated_by = user_id
            setting.updated_at = datetime.utcnow()
        else:
            setting = SystemSettings(
                setting_key=key,
                setting_value=str(value),
                updated_by=user_id
            )
            db.session.add(setting)
        db.session.commit()
        return setting

    @staticmethod
    def get_color_settings():
        """الحصول على إعدادات الألوان"""
        color_settings = SystemSettings.query.filter_by(
            category='colors',
            is_active=True
        ).all()

        colors = {}
        for setting in color_settings:
            colors[setting.setting_key] = setting.setting_value

        # الألوان الافتراضية
        default_colors = {
            'primary_color': '#0d6efd',
            'secondary_color': '#6c757d',
            'success_color': '#198754',
            'danger_color': '#dc3545',
            'warning_color': '#ffc107',
            'info_color': '#0dcaf0',
            'light_color': '#f8f9fa',
            'dark_color': '#212529',
            'sidebar_bg': '#343a40',
            'navbar_bg': '#0d6efd',
            'card_bg': '#ffffff',
            'text_color': '#212529'
        }

        # دمج الألوان المخصصة مع الافتراضية
        for key, default_value in default_colors.items():
            if key not in colors:
                colors[key] = default_value

        return colors

    @staticmethod
    def get_display_settings():
        """الحصول على إعدادات العرض"""
        display_settings = SystemSettings.query.filter_by(
            category='display',
            is_active=True
        ).all()

        settings = {}
        for setting in display_settings:
            settings[setting.setting_key] = setting.setting_value

        # الإعدادات الافتراضية
        default_settings = {
            'show_days_only': 'true',  # عرض الأيام كأرقام فقط
            'date_format': 'dd/mm/yyyy',
            'time_format': '24h',
            'items_per_page': '20',
            'show_sidebar': 'true',
            'compact_mode': 'false'
        }

        for key, default_value in default_settings.items():
            if key not in settings:
                settings[key] = default_value

        return settings

    def get_typed_value(self):
        """الحصول على القيمة بالنوع الصحيح"""
        if self.setting_type == 'boolean':
            return self.setting_value.lower() in ['true', '1', 'yes', 'on']
        elif self.setting_type == 'number':
            try:
                return int(self.setting_value)
            except (ValueError, TypeError):
                return 0
        elif self.setting_type == 'float':
            try:
                return float(self.setting_value)
            except (ValueError, TypeError):
                return 0.0
        else:
            return self.setting_value

    def __repr__(self):
        return f'<SystemSettings {self.setting_key}>'

class ArchivedMessage(db.Model):
    """نموذج الرسائل المؤرشفة"""
    __tablename__ = 'archived_messages'

    id = db.Column(db.Integer, primary_key=True)
    archive_id = db.Column(db.Integer, db.ForeignKey('archives.id'), nullable=False)
    original_message_id = db.Column(db.Integer)  # معرف الرسالة الأصلية

    # نسخة من بيانات الرسالة الأصلية
    message_number = db.Column(db.String(50))
    source = db.Column(db.String(200))
    destination = db.Column(db.String(200))
    subject = db.Column(db.Text)
    content = db.Column(db.Text)
    message_date = db.Column(db.Date)
    date_created = db.Column(db.DateTime)

    # معلومات المنشئ والمعتمد
    creator_name = db.Column(db.String(100))
    creator_department = db.Column(db.String(100))
    approver_name = db.Column(db.String(100))
    approval_date = db.Column(db.DateTime)
    approval_status = db.Column(db.String(20))

    # معلومات المرفق
    attachment_name = db.Column(db.String(200))
    attachment_path = db.Column(db.String(500))
    attachment_size = db.Column(db.Integer)

    # معلومات الأرشفة
    archived_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    archived_at = db.Column(db.DateTime, default=datetime.utcnow)
    archive_notes = db.Column(db.Text)  # ملاحظات الأرشفة

    # العلاقات
    archive = db.relationship('Archive', backref='messages')
    archiver = db.relationship('User', backref='archived_messages')

    def __repr__(self):
        return f'<ArchivedMessage {self.message_number}>'
