from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import enum

db = SQLAlchemy()

# جدول ربط الأدوار بالصلاحيات
role_permissions = db.Table('role_permissions',
    db.Column('role_id', db.Integer, db.<PERSON><PERSON>('roles.id'), primary_key=True),
    db.<PERSON>umn('permission_id', db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('permissions.id'), primary_key=True)
)

# جدول ربط المستخدمين بالصلاحيات الإضافية
user_permissions = db.Table('user_permissions',
    db.Column('user_id', db.Integer, db.<PERSON>ey('users.id'), primary_key=True),
    db.<PERSON>umn('permission_id', db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('permissions.id'), primary_key=True)
)

class UserRole(enum.Enum):
    EMPLOYEE = "EMPLOYEE"
    SECRETARY = "SECRETARY"
    MANAGER = "MANAGER"
    ADMIN = "ADMIN"  # مدير النظام

class PermissionType(enum.Enum):
    # صلاحيات الرسائل
    CREATE_MESSAGE = "create_message"
    VIEW_MESSAGE = "view_message"
    EDIT_MESSAGE = "edit_message"
    DELETE_MESSAGE = "delete_message"
    APPROVE_MESSAGE = "approve_message"
    ARCHIVE_MESSAGE = "archive_message"

    # صلاحيات المستخدمين
    CREATE_USER = "create_user"
    VIEW_USER = "view_user"
    EDIT_USER = "edit_user"
    DELETE_USER = "delete_user"
    MANAGE_ROLES = "manage_roles"

    # صلاحيات الأقسام
    CREATE_DEPARTMENT = "create_department"
    VIEW_DEPARTMENT = "view_department"
    EDIT_DEPARTMENT = "edit_department"
    DELETE_DEPARTMENT = "delete_department"

    # صلاحيات النظام
    VIEW_REPORTS = "view_reports"
    MANAGE_SYSTEM = "manage_system"
    VIEW_LOGS = "view_logs"
    BACKUP_SYSTEM = "backup_system"

class MessageType(enum.Enum):
    INCOMING = "incoming"
    OUTGOING = "outgoing"
    INTERNAL = "internal"

class ApprovalStatus(enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    AUTO_APPROVED = "auto_approved"

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.EMPLOYEE)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'))  # الدور المخصص
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))  # القسم التابع له المستخدم

    # حقول التوقيع الإلكتروني
    signature_type = db.Column(db.String(20))  # 'digital', 'image', 'none'
    signature_data = db.Column(db.Text)  # بيانات التوقيع أو مسار الملف
    signature_filename = db.Column(db.String(255))  # اسم ملف التوقيع الأصلي
    signature_created_at = db.Column(db.DateTime)  # تاريخ إنشاء التوقيع

    # Relationships
    created_messages = db.relationship('Message', foreign_keys='Message.created_by', backref='creator', lazy='dynamic')
    assigned_messages = db.relationship('Message', foreign_keys='Message.assigned_to', backref='assigned_user', lazy='dynamic')
    approved_messages = db.relationship('Message', foreign_keys='Message.approved_by', backref='approver', lazy='dynamic')
    department = db.relationship('Department', foreign_keys=[department_id], backref='employees')
    custom_role = db.relationship('Role', foreign_keys=[role_id], backref='users')
    additional_permissions = db.relationship('Permission', secondary=user_permissions,
                                           backref=db.backref('users', lazy='dynamic'))
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_manager(self):
        return self.role == UserRole.MANAGER
    
    def is_secretary(self):
        return self.role == UserRole.SECRETARY
    
    def can_manage_users(self):
        return self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.SECRETARY]
    
    def can_delete_messages(self):
        return self.role in [UserRole.ADMIN, UserRole.MANAGER]

    # نظام الصلاحيات المتقدم
    def has_permission(self, permission_name):
        """التحقق من وجود صلاحية معينة للمستخدم"""
        # التحقق من الصلاحيات الإضافية المباشرة
        if any(p.name == permission_name for p in self.additional_permissions):
            return True

        # التحقق من صلاحيات الدور المخصص
        if self.custom_role and self.custom_role.has_permission(permission_name):
            return True

        # التحقق من الصلاحيات الافتراضية حسب الدور
        return self._has_default_permission(permission_name)

    def _has_default_permission(self, permission_name):
        """الصلاحيات الافتراضية حسب الدور"""
        default_permissions = {
            UserRole.ADMIN: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user', 'delete_user', 'manage_roles',
                'create_department', 'view_department', 'edit_department', 'delete_department',
                'view_reports', 'manage_system', 'view_logs', 'backup_system'
            ],
            UserRole.MANAGER: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user',
                'view_department', 'edit_department',
                'view_reports'
            ],
            UserRole.SECRETARY: [
                'create_message', 'view_message', 'edit_message', 'archive_message',
                'view_user', 'edit_user',
                'view_department'
            ],
            UserRole.EMPLOYEE: [
                'create_message', 'view_message', 'edit_message',
                'view_user', 'view_department'
            ]
        }

        user_permissions = default_permissions.get(self.role, [])
        return permission_name in user_permissions

    def add_permission(self, permission):
        """إضافة صلاحية إضافية للمستخدم"""
        if permission not in self.additional_permissions:
            self.additional_permissions.append(permission)

    def remove_permission(self, permission):
        """إزالة صلاحية إضافية من المستخدم"""
        if permission in self.additional_permissions:
            self.additional_permissions.remove(permission)

    def get_all_permissions(self):
        """الحصول على جميع صلاحيات المستخدم"""
        permissions = set()

        # الصلاحيات الافتراضية
        default_permissions = {
            UserRole.ADMIN: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user', 'delete_user', 'manage_roles',
                'create_department', 'view_department', 'edit_department', 'delete_department',
                'view_reports', 'manage_system', 'view_logs', 'backup_system'
            ],
            UserRole.MANAGER: [
                'create_message', 'view_message', 'edit_message', 'delete_message', 'approve_message', 'archive_message',
                'create_user', 'view_user', 'edit_user',
                'view_department', 'edit_department',
                'view_reports'
            ],
            UserRole.SECRETARY: [
                'create_message', 'view_message', 'edit_message', 'archive_message',
                'view_user', 'edit_user',
                'view_department'
            ],
            UserRole.EMPLOYEE: [
                'create_message', 'view_message', 'edit_message',
                'view_user', 'view_department'
            ]
        }

        permissions.update(default_permissions.get(self.role, []))

        # صلاحيات الدور المخصص
        if self.custom_role:
            permissions.update([p.name for p in self.custom_role.permissions])

        # الصلاحيات الإضافية
        permissions.update([p.name for p in self.additional_permissions])

        return list(permissions)

    # دوال التوقيع الإلكتروني
    def has_signature(self):
        """التحقق من وجود توقيع للمستخدم"""
        return self.signature_type and self.signature_type != 'none' and self.signature_data

    def get_signature_data(self):
        """الحصول على بيانات التوقيع"""
        if not self.has_signature():
            return None

        if self.signature_type == 'digital':
            try:
                import json
                return json.loads(self.signature_data)
            except:
                return None
        elif self.signature_type == 'image':
            return self.signature_data

        return None

    def set_digital_signature(self, signature_info):
        """حفظ التوقيع الرقمي"""
        import json
        self.signature_type = 'digital'
        self.signature_data = json.dumps(signature_info)
        self.signature_created_at = datetime.utcnow()

    def set_image_signature(self, file_path, filename):
        """حفظ صورة التوقيع"""
        self.signature_type = 'image'
        self.signature_data = file_path
        self.signature_filename = filename
        self.signature_created_at = datetime.utcnow()

    def remove_signature(self):
        """إزالة التوقيع"""
        self.signature_type = 'none'
        self.signature_data = None
        self.signature_filename = None
        self.signature_created_at = None

    def __repr__(self):
        return f'<User {self.username}>'

class Message(db.Model):
    __tablename__ = 'messages'
    
    id = db.Column(db.Integer, primary_key=True)
    message_type = db.Column(db.Enum(MessageType), nullable=False)
    registration_number = db.Column(db.String(50), unique=True, nullable=False)
    destination = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.Text, nullable=False)
    content = db.Column(db.Text)
    date_created = db.Column(db.DateTime, default=datetime.utcnow)
    message_date = db.Column(db.Date, nullable=False)
    attachment_path = db.Column(db.String(500))
    attachment_name = db.Column(db.String(200))
    
    # Foreign keys
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Additional fields for organization
    year = db.Column(db.Integer, nullable=False)
    department = db.Column(db.String(100))
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    status = db.Column(db.String(20), default='active')    # active, archived, deleted

    # Internal message specific fields
    from_department = db.Column(db.String(100))  # للمراسلات الداخلية
    to_department = db.Column(db.String(100))    # للمراسلات الداخلية
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))  # المكلف بالرد
    due_date = db.Column(db.Date)  # تاريخ الاستحقاق
    is_urgent = db.Column(db.Boolean, default=False)  # عاجل
    requires_response = db.Column(db.Boolean, default=False)  # يتطلب رد

    # Approval system fields
    requires_approval = db.Column(db.Boolean, default=False)  # يتطلب اعتماد
    approval_status = db.Column(db.Enum(ApprovalStatus), default=ApprovalStatus.PENDING)  # حالة الاعتماد
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # معتمد من قبل
    approval_date = db.Column(db.DateTime)  # تاريخ الاعتماد
    approval_notes = db.Column(db.Text)  # ملاحظات الاعتماد
    
    def __init__(self, **kwargs):
        super(Message, self).__init__(**kwargs)
        if self.message_date:
            self.year = self.message_date.year
    
    def get_attachment_url(self):
        if self.attachment_path:
            return f'/uploads/{self.attachment_path}'
        return None
    
    def is_incoming(self):
        return self.message_type == MessageType.INCOMING
    
    def is_outgoing(self):
        return self.message_type == MessageType.OUTGOING

    def is_internal(self):
        return self.message_type == MessageType.INTERNAL

    def is_approved(self):
        return self.approval_status == ApprovalStatus.APPROVED

    def is_pending_approval(self):
        return self.approval_status == ApprovalStatus.PENDING and self.requires_approval

    def can_be_approved_by(self, user):
        """Check if user can approve this message"""
        if not self.requires_approval:
            return False
        if self.approval_status != ApprovalStatus.PENDING:
            return False
        # Only managers and secretaries can approve
        return user.is_manager() or user.is_secretary()

    def approve(self, user, notes=None):
        """Approve the message"""
        if self.can_be_approved_by(user):
            self.approval_status = ApprovalStatus.APPROVED
            self.approved_by = user.id
            self.approval_date = datetime.utcnow()
            self.approval_notes = notes
            return True
        return False

    def reject(self, user, notes=None):
        """Reject the message"""
        if self.can_be_approved_by(user):
            self.approval_status = ApprovalStatus.REJECTED
            self.approved_by = user.id
            self.approval_date = datetime.utcnow()
            self.approval_notes = notes
            return True
        return False

    def __repr__(self):
        return f'<Message {self.registration_number}>'

class Department(db.Model):
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # مدير القسم

    # Relationships
    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')

    def __repr__(self):
        return f'<Department {self.name}>'



class Permission(db.Model):
    """نموذج الصلاحيات"""
    __tablename__ = 'permissions'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)  # اسم الصلاحية
    display_name = db.Column(db.String(200), nullable=False)  # الاسم المعروض
    description = db.Column(db.Text)  # وصف الصلاحية
    category = db.Column(db.String(50), nullable=False)  # فئة الصلاحية
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Permission {self.name}>'

class Role(db.Model):
    """نموذج الأدوار"""
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)  # اسم الدور
    display_name = db.Column(db.String(200), nullable=False)  # الاسم المعروض
    description = db.Column(db.Text)  # وصف الدور
    is_system_role = db.Column(db.Boolean, default=False)  # دور نظام لا يمكن حذفه
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    permissions = db.relationship('Permission', secondary=role_permissions,
                                backref=db.backref('roles', lazy='dynamic'))
    # users relationship will be defined in User model

    def has_permission(self, permission_name):
        """التحقق من وجود صلاحية معينة في الدور"""
        return any(p.name == permission_name for p in self.permissions)

    def add_permission(self, permission):
        """إضافة صلاحية للدور"""
        if permission not in self.permissions:
            self.permissions.append(permission)

    def remove_permission(self, permission):
        """إزالة صلاحية من الدور"""
        if permission in self.permissions:
            self.permissions.remove(permission)

    def __repr__(self):
        return f'<Role {self.name}>'

class InternalMessageResponse(db.Model):
    __tablename__ = 'internal_message_responses'

    id = db.Column(db.Integer, primary_key=True)
    original_message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)
    response_content = db.Column(db.Text, nullable=False)
    responded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    response_date = db.Column(db.DateTime, default=datetime.utcnow)
    attachment_path = db.Column(db.String(500))
    attachment_name = db.Column(db.String(200))
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected

    # Relationships
    original_message = db.relationship('Message', backref='responses')
    responder = db.relationship('User', backref='message_responses')

    def __repr__(self):
        return f'<InternalMessageResponse {self.id}>'

class ArchiveStatus(enum.Enum):
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"

class Archive(db.Model):
    """نموذج الأرشيف"""
    __tablename__ = 'archives'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)  # اسم الأرشيف
    description = db.Column(db.Text)  # وصف الأرشيف
    archive_type = db.Column(db.String(50), nullable=False)  # نوع الأرشيف (yearly, monthly, custom)
    year = db.Column(db.Integer)  # السنة (للأرشيف السنوي)
    month = db.Column(db.Integer)  # الشهر (للأرشيف الشهري)
    start_date = db.Column(db.Date)  # تاريخ البداية (للأرشيف المخصص)
    end_date = db.Column(db.Date)  # تاريخ النهاية (للأرشيف المخصص)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # العلاقات
    creator = db.relationship('User', backref='created_archives')

    def get_messages_count(self):
        """عدد الرسائل في الأرشيف"""
        return ArchivedMessage.query.filter_by(archive_id=self.id).count()

    def get_size_mb(self):
        """حجم الأرشيف بالميجابايت"""
        # حساب تقريبي بناءً على عدد الرسائل
        return self.get_messages_count() * 0.1  # تقدير 0.1 MB لكل رسالة

    def __repr__(self):
        return f'<Archive {self.name}>'

class ArchivedMessage(db.Model):
    """نموذج الرسائل المؤرشفة"""
    __tablename__ = 'archived_messages'

    id = db.Column(db.Integer, primary_key=True)
    archive_id = db.Column(db.Integer, db.ForeignKey('archives.id'), nullable=False)
    original_message_id = db.Column(db.Integer)  # معرف الرسالة الأصلية

    # نسخة من بيانات الرسالة الأصلية
    message_number = db.Column(db.String(50))
    source = db.Column(db.String(200))
    destination = db.Column(db.String(200))
    subject = db.Column(db.Text)
    content = db.Column(db.Text)
    message_date = db.Column(db.Date)
    date_created = db.Column(db.DateTime)

    # معلومات المنشئ والمعتمد
    creator_name = db.Column(db.String(100))
    creator_department = db.Column(db.String(100))
    approver_name = db.Column(db.String(100))
    approval_date = db.Column(db.DateTime)
    approval_status = db.Column(db.String(20))

    # معلومات المرفق
    attachment_name = db.Column(db.String(200))
    attachment_path = db.Column(db.String(500))
    attachment_size = db.Column(db.Integer)

    # معلومات الأرشفة
    archived_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    archived_at = db.Column(db.DateTime, default=datetime.utcnow)
    archive_notes = db.Column(db.Text)  # ملاحظات الأرشفة

    # العلاقات
    archive = db.relationship('Archive', backref='messages')
    archiver = db.relationship('User', backref='archived_messages')

    def __repr__(self):
        return f'<ArchivedMessage {self.message_number}>'
