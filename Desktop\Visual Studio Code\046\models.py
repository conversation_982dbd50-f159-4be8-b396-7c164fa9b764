from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import enum

db = SQLAlchemy()

class UserRole(enum.Enum):
    EMPLOYEE = "employee"
    SECRETARY = "secretary"
    MANAGER = "manager"

class MessageType(enum.Enum):
    INCOMING = "incoming"
    OUTGOING = "outgoing"

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.EMPLOYEE)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.<PERSON>olean, default=True)
    
    # Relationships
    created_messages = db.relationship('Message', backref='creator', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_manager(self):
        return self.role == UserRole.MANAGER
    
    def is_secretary(self):
        return self.role == UserRole.SECRETARY
    
    def can_manage_users(self):
        return self.role in [UserRole.MANAGER, UserRole.SECRETARY]
    
    def can_delete_messages(self):
        return self.role == UserRole.MANAGER
    
    def __repr__(self):
        return f'<User {self.username}>'

class Message(db.Model):
    __tablename__ = 'messages'
    
    id = db.Column(db.Integer, primary_key=True)
    message_type = db.Column(db.Enum(MessageType), nullable=False)
    registration_number = db.Column(db.String(50), unique=True, nullable=False)
    destination = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.Text, nullable=False)
    content = db.Column(db.Text)
    date_created = db.Column(db.DateTime, default=datetime.utcnow)
    message_date = db.Column(db.Date, nullable=False)
    attachment_path = db.Column(db.String(500))
    attachment_name = db.Column(db.String(200))
    
    # Foreign keys
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Additional fields for organization
    year = db.Column(db.Integer, nullable=False)
    department = db.Column(db.String(100))
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    status = db.Column(db.String(20), default='active')    # active, archived, deleted
    
    def __init__(self, **kwargs):
        super(Message, self).__init__(**kwargs)
        if self.message_date:
            self.year = self.message_date.year
    
    def get_attachment_url(self):
        if self.attachment_path:
            return f'/uploads/{self.attachment_path}'
        return None
    
    def is_incoming(self):
        return self.message_type == MessageType.INCOMING
    
    def is_outgoing(self):
        return self.message_type == MessageType.OUTGOING
    
    def __repr__(self):
        return f'<Message {self.registration_number}>'

class Department(db.Model):
    __tablename__ = 'departments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    def __repr__(self):
        return f'<Department {self.name}>'
