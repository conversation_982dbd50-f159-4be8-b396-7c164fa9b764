from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import enum

db = SQLAlchemy()

class UserRole(enum.Enum):
    EMPLOYEE = "employee"
    SECRETARY = "secretary"
    MANAGER = "manager"

class MessageType(enum.Enum):
    INCOMING = "incoming"
    OUTGOING = "outgoing"
    INTERNAL = "internal"

class ApprovalStatus(enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    AUTO_APPROVED = "auto_approved"

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.EMPLOYEE)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))  # القسم التابع له المستخدم

    # Relationships
    created_messages = db.relationship('Message', foreign_keys='Message.created_by', backref='creator', lazy='dynamic')
    assigned_messages = db.relationship('Message', foreign_keys='Message.assigned_to', backref='assigned_user', lazy='dynamic')
    approved_messages = db.relationship('Message', foreign_keys='Message.approved_by', backref='approver', lazy='dynamic')
    department = db.relationship('Department', foreign_keys=[department_id], backref='employees')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_manager(self):
        return self.role == UserRole.MANAGER
    
    def is_secretary(self):
        return self.role == UserRole.SECRETARY
    
    def can_manage_users(self):
        return self.role in [UserRole.MANAGER, UserRole.SECRETARY]
    
    def can_delete_messages(self):
        return self.role == UserRole.MANAGER

    # Temporary signature methods (disabled for now)
    def has_signature(self):
        """Check if user has a signature - temporarily disabled"""
        return False

    def get_signature_data(self):
        """Get signature data - temporarily disabled"""
        return None

    @property
    def signature_type(self):
        """Signature type - temporarily disabled"""
        return 'none'

    @property
    def signature_created_at(self):
        """Signature creation date - temporarily disabled"""
        return None

    def __repr__(self):
        return f'<User {self.username}>'

class Message(db.Model):
    __tablename__ = 'messages'
    
    id = db.Column(db.Integer, primary_key=True)
    message_type = db.Column(db.Enum(MessageType), nullable=False)
    registration_number = db.Column(db.String(50), unique=True, nullable=False)
    destination = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.Text, nullable=False)
    content = db.Column(db.Text)
    date_created = db.Column(db.DateTime, default=datetime.utcnow)
    message_date = db.Column(db.Date, nullable=False)
    attachment_path = db.Column(db.String(500))
    attachment_name = db.Column(db.String(200))
    
    # Foreign keys
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Additional fields for organization
    year = db.Column(db.Integer, nullable=False)
    department = db.Column(db.String(100))
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    status = db.Column(db.String(20), default='active')    # active, archived, deleted

    # Internal message specific fields
    from_department = db.Column(db.String(100))  # للمراسلات الداخلية
    to_department = db.Column(db.String(100))    # للمراسلات الداخلية
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))  # المكلف بالرد
    due_date = db.Column(db.Date)  # تاريخ الاستحقاق
    is_urgent = db.Column(db.Boolean, default=False)  # عاجل
    requires_response = db.Column(db.Boolean, default=False)  # يتطلب رد

    # Approval system fields
    requires_approval = db.Column(db.Boolean, default=False)  # يتطلب اعتماد
    approval_status = db.Column(db.Enum(ApprovalStatus), default=ApprovalStatus.PENDING)  # حالة الاعتماد
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # معتمد من قبل
    approval_date = db.Column(db.DateTime)  # تاريخ الاعتماد
    approval_notes = db.Column(db.Text)  # ملاحظات الاعتماد
    
    def __init__(self, **kwargs):
        super(Message, self).__init__(**kwargs)
        if self.message_date:
            self.year = self.message_date.year
    
    def get_attachment_url(self):
        if self.attachment_path:
            return f'/uploads/{self.attachment_path}'
        return None
    
    def is_incoming(self):
        return self.message_type == MessageType.INCOMING
    
    def is_outgoing(self):
        return self.message_type == MessageType.OUTGOING

    def is_internal(self):
        return self.message_type == MessageType.INTERNAL

    def is_approved(self):
        return self.approval_status == ApprovalStatus.APPROVED

    def is_pending_approval(self):
        return self.approval_status == ApprovalStatus.PENDING and self.requires_approval

    def can_be_approved_by(self, user):
        """Check if user can approve this message"""
        if not self.requires_approval:
            return False
        if self.approval_status != ApprovalStatus.PENDING:
            return False
        # Only managers and secretaries can approve
        return user.is_manager() or user.is_secretary()

    def approve(self, user, notes=None):
        """Approve the message"""
        if self.can_be_approved_by(user):
            self.approval_status = ApprovalStatus.APPROVED
            self.approved_by = user.id
            self.approval_date = datetime.utcnow()
            self.approval_notes = notes
            return True
        return False

    def reject(self, user, notes=None):
        """Reject the message"""
        if self.can_be_approved_by(user):
            self.approval_status = ApprovalStatus.REJECTED
            self.approved_by = user.id
            self.approval_date = datetime.utcnow()
            self.approval_notes = notes
            return True
        return False

    def __repr__(self):
        return f'<Message {self.registration_number}>'

class Department(db.Model):
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # مدير القسم

    # Relationships
    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')

    def __repr__(self):
        return f'<Department {self.name}>'

class InternalMessageResponse(db.Model):
    __tablename__ = 'internal_message_responses'

    id = db.Column(db.Integer, primary_key=True)
    original_message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)
    response_content = db.Column(db.Text, nullable=False)
    responded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    response_date = db.Column(db.DateTime, default=datetime.utcnow)
    attachment_path = db.Column(db.String(500))
    attachment_name = db.Column(db.String(200))
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected

    # Relationships
    original_message = db.relationship('Message', backref='responses')
    responder = db.relationship('User', backref='message_responses')

    def __repr__(self):
        return f'<InternalMessageResponse {self.id}>'
