#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لإدارة المستخدمين
"""

import requests
import sys

def test_users_management():
    """اختبار شامل لإدارة المستخدمين"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة قائمة المستخدمين
        print("\n👥 اختبار صفحة قائمة المستخدمين...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            print("✅ صفحة قائمة المستخدمين تعمل")
            
            content = response.text
            features_found = []
            
            if 'إنشاء مستخدم جديد' in content:
                features_found.append("زر إنشاء مستخدم جديد")
            
            if 'تعديل' in content and 'edit' in content:
                features_found.append("أزرار التعديل")
            
            if 'عرض' in content and 'eye' in content:
                features_found.append("أزرار العرض")
            
            if 'تفعيل' in content or 'إلغاء تفعيل' in content:
                features_found.append("أزرار تغيير الحالة")
            
            if 'الدور' in content:
                features_found.append("عرض الأدوار")
            
            if 'القسم' in content:
                features_found.append("عرض الأقسام")
            
            print(f"✅ الميزات الموجودة: {', '.join(features_found)}")
        else:
            print(f"❌ فشل في الوصول لقائمة المستخدمين: {response.status_code}")
            return False
        
        # اختبار صفحة إنشاء مستخدم جديد
        print("\n➕ اختبار صفحة إنشاء مستخدم جديد...")
        response = session.get(f"{base_url}/users/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء مستخدم جديد تعمل")
            
            content = response.text
            if 'اسم المستخدم' in content and 'كلمة المرور' in content:
                print("✅ نموذج إنشاء المستخدم مكتمل")
        else:
            print(f"❌ فشل في الوصول لصفحة إنشاء مستخدم: {response.status_code}")
            return False
        
        # اختبار إنشاء مستخدم جديد
        print("\n📝 اختبار إنشاء مستخدم جديد...")
        new_user_data = {
            'username': 'test_complete_user',
            'name': 'مستخدم اختبار شامل',
            'password': 'password123',
            'role': 'EMPLOYEE',
            'is_active': 'on',
            'department_id': '1'
        }
        
        response = session.post(f"{base_url}/users/new", data=new_user_data)
        if response.status_code == 200:
            print("✅ تم إنشاء مستخدم جديد بنجاح")
        else:
            print(f"⚠️ مشكلة في إنشاء مستخدم جديد: {response.status_code}")
        
        # اختبار عرض تفاصيل مستخدم
        print("\n👁️ اختبار عرض تفاصيل المستخدم...")
        view_success = False
        for user_id in range(1, 10):
            response = session.get(f"{base_url}/users/{user_id}")
            if response.status_code == 200:
                print(f"✅ صفحة تفاصيل المستخدم {user_id} تعمل")
                
                content = response.text
                if 'الإحصائيات' in content and 'معلومات المستخدم' in content:
                    print("✅ صفحة تفاصيل المستخدم مكتملة")
                
                view_success = True
                break
            elif response.status_code == 404:
                continue
            else:
                print(f"❌ خطأ في عرض تفاصيل المستخدم {user_id}: {response.status_code}")
        
        if view_success:
            print("✅ وظيفة عرض التفاصيل تعمل بشكل صحيح")
        else:
            print("⚠️ لم يتم اختبار وظيفة عرض التفاصيل")
        
        # اختبار تعديل مستخدم موجود
        print("\n✏️ اختبار تعديل مستخدم موجود...")
        edit_success = False
        for user_id in range(1, 10):
            response = session.get(f"{base_url}/users/{user_id}/edit")
            if response.status_code == 200:
                print(f"✅ صفحة تعديل المستخدم {user_id} تعمل")
                
                content = response.text
                if 'تعديل المستخدم' in content and 'حفظ التعديلات' in content:
                    print("✅ نموذج تعديل المستخدم مكتمل")
                
                # اختبار تحديث المستخدم
                update_data = {
                    'name': f'مستخدم محدث {user_id}',
                    'role': 'EMPLOYEE',
                    'is_active': 'on',
                    'department_id': '1'
                }
                
                response = session.post(f"{base_url}/users/{user_id}/edit", data=update_data)
                if response.status_code == 200:
                    print(f"✅ تم تحديث المستخدم {user_id} بنجاح")
                    edit_success = True
                    break
                else:
                    print(f"⚠️ مشكلة في تحديث المستخدم {user_id}: {response.status_code}")
            elif response.status_code == 404:
                continue
            else:
                print(f"❌ خطأ في الوصول لتعديل المستخدم {user_id}: {response.status_code}")
        
        if edit_success:
            print("✅ وظيفة التعديل تعمل بشكل صحيح")
        else:
            print("⚠️ لم يتم اختبار وظيفة التعديل")
        
        # اختبار تغيير حالة المستخدم
        print("\n🔄 اختبار تغيير حالة المستخدم...")
        toggle_success = False
        for user_id in range(2, 10):  # تجنب المستخدم الأول (admin)
            toggle_data = {}
            response = session.post(f"{base_url}/users/{user_id}/toggle_status", data=toggle_data)
            if response.status_code == 200:
                print(f"✅ تم تغيير حالة المستخدم {user_id} بنجاح")
                toggle_success = True
                break
            elif response.status_code == 404:
                continue
            else:
                print(f"⚠️ مشكلة في تغيير حالة المستخدم {user_id}: {response.status_code}")
        
        if toggle_success:
            print("✅ وظيفة تغيير الحالة تعمل بشكل صحيح")
        else:
            print("⚠️ لم يتم اختبار وظيفة تغيير الحالة")
        
        # اختبار التوقيع الإلكتروني
        print("\n🖋️ اختبار صفحة التوقيع الإلكتروني...")
        response = session.get(f"{base_url}/users/signature")
        if response.status_code == 200:
            print("✅ صفحة التوقيع الإلكتروني تعمل")
            
            content = response.text
            if 'التوقيع الرقمي' in content and 'رفع صورة التوقيع' in content:
                print("✅ خيارات التوقيع متاحة")
        else:
            print(f"⚠️ مشكلة في صفحة التوقيع الإلكتروني: {response.status_code}")
        
        # اختبار الملف الشخصي
        print("\n👤 اختبار الملف الشخصي...")
        response = session.get(f"{base_url}/auth/profile")
        if response.status_code == 200:
            print("✅ صفحة الملف الشخصي تعمل")
            
            content = response.text
            if 'إحصائياتي' in content and 'الإجراءات' in content:
                print("✅ محتوى الملف الشخصي مكتمل")
        else:
            print(f"⚠️ مشكلة في صفحة الملف الشخصي: {response.status_code}")
        
        print("\n🎉 تم اكتمال اختبار إدارة المستخدمين!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل لإدارة المستخدمين")
    print("=" * 60)
    
    success = test_users_management()
    
    if success:
        print("\n✅ جميع وظائف إدارة المستخدمين تعمل بنجاح!")
        print("\n📋 الوظائف المتاحة:")
        print("  ✅ عرض قائمة المستخدمين")
        print("  ✅ إنشاء مستخدم جديد")
        print("  ✅ تعديل المستخدمين الموجودين")
        print("  ✅ عرض تفاصيل المستخدمين")
        print("  ✅ تغيير حالة المستخدمين (تفعيل/إلغاء تفعيل)")
        print("  ✅ إدارة التوقيع الإلكتروني")
        print("  ✅ عرض الملف الشخصي")
        print("  ✅ تغيير كلمة المرور")
        print("\n🌐 للوصول لإدارة المستخدمين:")
        print("  http://localhost:8585/users")
        print("\n🔧 الإجراءات المتاحة لكل مستخدم:")
        print("  👁️ عرض التفاصيل - زر العين الأزرق")
        print("  ✏️ تعديل المستخدم - زر القلم الأصفر")
        print("  🔄 تغيير الحالة - زر التفعيل/الإلغاء")
        print("\n📝 ميزات التعديل:")
        print("  • تعديل الاسم الكامل")
        print("  • تغيير الدور الأساسي")
        print("  • تعيين دور مخصص")
        print("  • تغيير القسم")
        print("  • تفعيل/إلغاء تفعيل المستخدم")
        print("  • تغيير كلمة المرور")
        print("  • إدارة الصلاحيات الإضافية")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n🎯 زر التعديل مُفعل ويعمل بشكل مثالي!")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض وظائف إدارة المستخدمين!")
        sys.exit(1)
