#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الملف الشخصي
"""

import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Message, Department

def test_profile_functionality():
    """اختبار وظائف الملف الشخصي"""
    
    app = create_app()
    with app.app_context():
        print("🧪 اختبار الملف الشخصي...")
        print("=" * 50)
        
        # البحث عن المدير
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            print("❌ لم يتم العثور على المدير")
            return
        
        print(f"👤 اختبار الملف الشخصي للمستخدم: {admin.name}")
        print()
        
        # اختبار الدوال الجديدة
        print("🔍 اختبار الدوال الجديدة:")
        print(f"   is_admin(): {admin.is_admin()}")
        print(f"   can_manage_users(): {admin.can_manage_users()}")
        print(f"   can_delete_messages(): {admin.can_delete_messages()}")
        print()
        
        # اختبار إحصائيات المستخدم
        print("📊 إحصائيات المستخدم:")
        total_messages = admin.created_messages.count()
        incoming_messages = admin.created_messages.filter_by(message_type='incoming').count()
        outgoing_messages = admin.created_messages.filter_by(message_type='outgoing').count()
        internal_messages = admin.created_messages.filter_by(message_type='internal').count()
        
        print(f"   إجمالي الرسائل: {total_messages}")
        print(f"   رسائل واردة: {incoming_messages}")
        print(f"   رسائل صادرة: {outgoing_messages}")
        print(f"   رسائل داخلية: {internal_messages}")
        print()
        
        # اختبار معلومات القسم
        print("🏢 معلومات القسم:")
        if admin.department:
            print(f"   اسم القسم: {admin.department.name}")
            print(f"   وصف القسم: {admin.department.description}")
            print(f"   مدير القسم: {admin.department.manager.name if admin.department.manager else 'غير محدد'}")
            print(f"   عدد الموظفين: {len(admin.department.employees)}")
        else:
            print("   لا يوجد قسم محدد للمستخدم")
        print()
        
        # اختبار الصلاحيات
        print("🔐 اختبار الصلاحيات:")
        user_permissions = admin.get_all_permissions()
        print(f"   إجمالي الصلاحيات: {len(user_permissions)}")
        print("   أول 10 صلاحيات:")
        for i, perm in enumerate(user_permissions[:10], 1):
            print(f"   {i:2d}. {perm}")
        if len(user_permissions) > 10:
            print(f"   ... و {len(user_permissions) - 10} صلاحية أخرى")
        print()
        
        # اختبار الرسائل الحديثة
        print("📧 الرسائل الحديثة:")
        recent_messages = admin.created_messages.order_by(
            Message.date_created.desc()
        ).limit(5).all()
        
        if recent_messages:
            print(f"   عدد الرسائل الحديثة: {len(recent_messages)}")
            for msg in recent_messages:
                print(f"   - {msg.registration_number}: {msg.subject[:30]}...")
        else:
            print("   لا توجد رسائل")
        print()
        
        # اختبار الرسائل المكلف بها
        print("📋 الرسائل المكلف بها:")
        assigned_messages = admin.assigned_messages.filter_by(
            requires_response=True
        ).limit(5).all()
        
        if assigned_messages:
            print(f"   عدد الرسائل المكلف بها: {len(assigned_messages)}")
            for msg in assigned_messages:
                print(f"   - {msg.registration_number}: {msg.subject[:30]}...")
        else:
            print("   لا توجد رسائل مكلف بها")
        print()
        
        # اختبار التوقيع الإلكتروني
        print("🖋️ التوقيع الإلكتروني:")
        print(f"   يوجد توقيع: {admin.has_signature()}")
        print(f"   نوع التوقيع: {admin.signature_type or 'لا يوجد'}")
        if admin.signature_created_at:
            print(f"   تاريخ إنشاء التوقيع: {admin.signature_created_at}")
        print()
        
        # اختبار الروابط المتاحة
        print("🔗 الروابط المتاحة للمستخدم:")
        available_routes = []
        
        # الروابط الأساسية
        available_routes.append("الملف الشخصي: /auth/profile")
        available_routes.append("تعديل الملف الشخصي: /auth/edit_profile")
        available_routes.append("تغيير كلمة المرور: /auth/change_password")
        available_routes.append("التوقيع الإلكتروني: /users/signature")
        
        # روابط الرسائل
        available_routes.append("رسالة جديدة: /messages/new")
        available_routes.append("جميع الرسائل: /messages")
        
        # روابط الإدارة (حسب الصلاحيات)
        if admin.can_manage_users():
            available_routes.append("إدارة المستخدمين: /users")
            available_routes.append("إدارة الأقسام: /departments")
        
        if admin.role.value == 'ADMIN':
            available_routes.append("إدارة الصلاحيات: /permissions")
        
        for route in available_routes:
            print(f"   ✅ {route}")
        
        print()
        print("✅ تم الانتهاء من اختبار الملف الشخصي")
        print()
        print("🌐 يمكنك الآن زيارة الملف الشخصي على:")
        print("   http://localhost:8585/auth/profile")

if __name__ == '__main__':
    test_profile_functionality()
