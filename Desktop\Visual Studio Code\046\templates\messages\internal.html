{% extends "base.html" %}

{% block title %}المراسلات الداخلية - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-building me-2"></i>
                المراسلات الداخلية
            </h1>
            <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                مراسلة داخلية جديدة
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ messages.total }}</h3>
                <p class="mb-0">إجمالي المراسلات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h3>{{ messages.items|selectattr('is_urgent', 'equalto', true)|list|length }}</h3>
                <p class="mb-0">عاجلة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ messages.items|selectattr('requires_response', 'equalto', true)|list|length }}</h3>
                <p class="mb-0">تتطلب رد</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h3>{{ messages.items|selectattr('due_date')|selectattr('due_date', 'lt', moment().date())|list|length }}</h3>
                <p class="mb-0">متأخرة</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="urgent" {% if request.args.get('status') == 'urgent' %}selected{% endif %}>عاجلة</option>
                            <option value="pending" {% if request.args.get('status') == 'pending' %}selected{% endif %}>بانتظار الرد</option>
                            <option value="overdue" {% if request.args.get('status') == 'overdue' %}selected{% endif %}>متأخرة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">القسم</label>
                        <select name="department" class="form-select">
                            <option value="">جميع الأقسام</option>
                            {% for dept in departments %}
                            <option value="{{ dept.name }}" {% if request.args.get('department') == dept.name %}selected{% endif %}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" name="q" class="form-control" placeholder="رقم التسجيل أو الموضوع" value="{{ request.args.get('q', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ url_for('messages.internal_messages') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Messages Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if messages.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>الموضوع</th>
                                <th>من القسم</th>
                                <th>إلى القسم</th>
                                <th>المكلف</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in messages.items %}
                            <tr class="{% if message.is_urgent %}table-danger{% elif message.due_date and message.due_date < moment().date() %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ message.registration_number }}</strong>
                                    {% if message.is_urgent %}
                                        <i class="fas fa-exclamation-triangle text-danger ms-1" title="عاجل"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject[:40] }}{% if message.subject|length > 40 %}...{% endif %}
                                    </a>
                                </td>
                                <td>{{ message.from_department or '-' }}</td>
                                <td>{{ message.to_department or '-' }}</td>
                                <td>
                                    {% if message.assigned_to %}
                                        {{ message.assigned_user.name if message.assigned_user else 'غير محدد' }}
                                    {% else %}
                                        <span class="text-muted">غير مكلف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if message.due_date %}
                                        {{ message.due_date.strftime('%Y-%m-%d') }}
                                        {% if message.due_date < moment().date() %}
                                            <i class="fas fa-clock text-danger ms-1" title="متأخر"></i>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if message.requires_response %}
                                        {% if message.responses %}
                                            <span class="badge bg-success">تم الرد</span>
                                        {% else %}
                                            <span class="badge bg-warning">بانتظار الرد</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-info">للعلم</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if message.requires_response and not message.responses and (current_user.id == message.assigned_to or current_user.is_manager()) %}
                                        <a href="{{ url_for('messages.respond_internal', id=message.id) }}" 
                                           class="btn btn-outline-success" title="رد">
                                            <i class="fas fa-reply"></i>
                                        </a>
                                        {% endif %}
                                        {% if current_user.is_manager() or message.created_by == current_user.id %}
                                        <a href="{{ url_for('messages.edit_message', id=message.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if messages.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if messages.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('messages.internal_messages', page=messages.prev_num, **request.args) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in messages.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != messages.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('messages.internal_messages', page=page_num, **request.args) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if messages.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('messages.internal_messages', page=messages.next_num, **request.args) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-building fa-4x mb-3"></i>
                    <h4>لا توجد مراسلات داخلية</h4>
                    <p>لم يتم العثور على أي مراسلات داخلية تطابق معايير البحث</p>
                    <a href="{{ url_for('messages.new_message') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء مراسلة داخلية جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
