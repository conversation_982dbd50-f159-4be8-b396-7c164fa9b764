{% extends "base.html" %}

{% block title %}إدارة الصلاحيات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-shield-alt me-2"></i>
                    إدارة الصلاحيات
                </h2>
                <div>
                    <a href="{{ url_for('permissions.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للرئيسية
                    </a>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي الصلاحيات</h6>
                                    <h3 class="mb-0">{{ grouped_permissions.values() | map('length') | sum }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-key fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">فئات الصلاحيات</h6>
                                    <h3 class="mb-0">{{ grouped_permissions.keys() | list | length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-layer-group fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">صلاحيات الرسائل</h6>
                                    <h3 class="mb-0">{{ grouped_permissions.get('messages', []) | length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">صلاحيات النظام</h6>
                                    <h3 class="mb-0">{{ grouped_permissions.get('system', []) | length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-cogs fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة الصلاحيات مجمعة حسب الفئة -->
            {% for category, permissions in grouped_permissions.items() %}
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        {% if category == 'messages' %}
                            <i class="fas fa-envelope me-2 text-primary"></i>صلاحيات الرسائل
                        {% elif category == 'users' %}
                            <i class="fas fa-users me-2 text-success"></i>صلاحيات المستخدمين
                        {% elif category == 'departments' %}
                            <i class="fas fa-building me-2 text-info"></i>صلاحيات الأقسام
                        {% elif category == 'system' %}
                            <i class="fas fa-cogs me-2 text-warning"></i>صلاحيات النظام
                        {% else %}
                            <i class="fas fa-key me-2 text-secondary"></i>{{ category }}
                        {% endif %}
                        <span class="badge bg-secondary ms-2">{{ permissions | length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for permission in permissions %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="border rounded p-3 h-100">
                                <h6 class="text-primary mb-2">
                                    <i class="fas fa-key me-1"></i>
                                    {{ permission.display_name }}
                                </h6>
                                <p class="text-muted small mb-2">
                                    <strong>الاسم التقني:</strong> {{ permission.name }}
                                </p>
                                {% if permission.description %}
                                <p class="text-muted small mb-0">
                                    {{ permission.description }}
                                </p>
                                {% endif %}
                                <div class="mt-2">
                                    {% if permission.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}

            {% if not grouped_permissions %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-key fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد صلاحيات محددة</h5>
                    <p class="text-muted">يمكنك إنشاء صلاحيات جديدة من خلال إدارة الأدوار</p>
                    <a href="{{ url_for('permissions.list_roles') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إدارة الأدوار
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.border {
    border-color: #e9ecef !important;
    transition: border-color 0.2s;
}

.border:hover {
    border-color: #007bff !important;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
