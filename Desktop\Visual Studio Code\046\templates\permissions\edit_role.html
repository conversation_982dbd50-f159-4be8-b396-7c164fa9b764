{% extends "base.html" %}

{% block title %}تعديل الدور: {{ role.display_name }} - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-edit me-2"></i>
                تعديل الدور: {{ role.display_name }}
            </h1>
            <div>
                <a href="{{ url_for('permissions.list_roles') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للأدوار
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدور
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الدور (تقني)</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="{{ role.name }}" readonly
                               style="background-color: #f8f9fa;">
                        <div class="form-text">الاسم التقني لا يمكن تعديله</div>
                    </div>

                    <div class="mb-3">
                        <label for="display_name" class="form-label">الاسم المعروض *</label>
                        <input type="text" class="form-control" id="display_name" name="display_name" 
                               value="{{ role.display_name }}" required
                               placeholder="مثال: مدير مخصص">
                        <div class="form-text">الاسم الذي سيظهر للمستخدمين</div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label">وصف الدور</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف مختصر لمهام ومسؤوليات هذا الدور">{{ role.description or '' }}</textarea>
                    </div>

                    <div class="mb-4">
                        <h6>الصلاحيات</h6>
                        <div class="form-text mb-3">اختر الصلاحيات التي تريد منحها لهذا الدور</div>
                        
                        {% if permissions %}
                        <!-- Group permissions by category -->
                        {% set categories = permissions | groupby('category') %}
                        {% set role_permission_ids = role.permissions | map(attribute='id') | list %}
                        
                        {% for category, category_permissions in categories %}
                        <div class="card mb-3">
                            <div class="card-header">
                                <div class="form-check">
                                    <input class="form-check-input category-checkbox" type="checkbox" 
                                           id="category_{{ loop.index }}" 
                                           data-category="{{ category }}">
                                    <label class="form-check-label fw-bold" for="category_{{ loop.index }}">
                                        {{ category }}
                                    </label>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for permission in category_permissions %}
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" 
                                                   type="checkbox" 
                                                   name="permissions" 
                                                   value="{{ permission.id }}" 
                                                   id="permission_{{ permission.id }}"
                                                   data-category="{{ category }}"
                                                   {% if permission.id in role_permission_ids %}checked{% endif %}>
                                            <label class="form-check-label" for="permission_{{ permission.id }}">
                                                <strong>{{ permission.display_name }}</strong>
                                                {% if permission.description %}
                                                <br><small class="text-muted">{{ permission.description }}</small>
                                                {% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لا توجد صلاحيات متاحة. يرجى تهيئة الصلاحيات الافتراضية أولاً.
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('permissions.list_roles') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدور
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>نوع الدور:</strong>
                    {% if role.is_system_role %}
                        <span class="badge bg-warning">دور نظام</span>
                    {% else %}
                        <span class="badge bg-secondary">دور مخصص</span>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    {% if role.is_active %}
                        <span class="badge bg-success">نشط</span>
                    {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <strong>عدد المستخدمين:</strong>
                    <span class="badge bg-info">{{ role.users|length }}</span>
                </div>
                <div class="mb-3">
                    <strong>تاريخ الإنشاء:</strong>
                    <br>{{ role.created_at.strftime('%Y-%m-%d %H:%M') }}
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    المستخدمون المرتبطون
                </h6>
            </div>
            <div class="card-body">
                {% if role.users|length > 0 %}
                <div class="list-group list-group-flush">
                    {% for user in role.users[:5] %}
                    <div class="list-group-item px-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ user.name }}</strong>
                                <br><small class="text-muted">{{ user.username }}</small>
                            </div>
                            <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                {{ 'نشط' if user.is_active else 'غير نشط' }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if role.users|length > 5 %}
                <div class="text-center mt-2">
                    <small class="text-muted">و {{ role.users|length - 5 }} مستخدمين آخرين</small>
                </div>
                {% endif %}
                {% else %}
                <p class="text-muted mb-0">لا يوجد مستخدمون مرتبطون بهذا الدور</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        راجع الصلاحيات بعناية قبل الحفظ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من منح الصلاحيات الضرورية فقط
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        التغييرات ستؤثر على جميع المستخدمين
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle category checkboxes
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    
    categoryCheckboxes.forEach(function(categoryCheckbox) {
        categoryCheckbox.addEventListener('change', function() {
            const category = this.dataset.category;
            const permissionCheckboxes = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);
            
            permissionCheckboxes.forEach(function(permissionCheckbox) {
                permissionCheckbox.checked = categoryCheckbox.checked;
            });
        });
    });
    
    // Update category checkboxes when individual permissions change
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
    
    permissionCheckboxes.forEach(function(permissionCheckbox) {
        permissionCheckbox.addEventListener('change', function() {
            updateCategoryCheckbox(this.dataset.category);
        });
    });
    
    // Initialize category checkboxes based on current state
    const categories = [...new Set(Array.from(permissionCheckboxes).map(cb => cb.dataset.category))];
    categories.forEach(updateCategoryCheckbox);
    
    function updateCategoryCheckbox(category) {
        const categoryCheckbox = document.querySelector(`.category-checkbox[data-category="${category}"]`);
        const categoryPermissions = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);
        
        let checkedCount = 0;
        categoryPermissions.forEach(function(cb) {
            if (cb.checked) checkedCount++;
        });
        
        if (checkedCount === 0) {
            categoryCheckbox.checked = false;
            categoryCheckbox.indeterminate = false;
        } else if (checkedCount === categoryPermissions.length) {
            categoryCheckbox.checked = true;
            categoryCheckbox.indeterminate = false;
        } else {
            categoryCheckbox.checked = false;
            categoryCheckbox.indeterminate = true;
        }
    }
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.form-check-input:indeterminate {
    background-color: #6c757d;
    border-color: #6c757d;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

.card-header .form-check {
    margin-bottom: 0;
}

.permission-checkbox + label {
    cursor: pointer;
}

.category-checkbox + label {
    cursor: pointer;
    font-weight: bold;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}
