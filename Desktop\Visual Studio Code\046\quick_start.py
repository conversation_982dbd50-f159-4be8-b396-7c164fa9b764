#!/usr/bin/env python3
"""
Quick start with login page - Electronic Correspondence System
تشغيل سريع مع صفحة الدخول
"""

from flask import Flask, render_template_string, redirect, url_for, request, flash, session
import os

app = Flask(__name__)
app.secret_key = 'demo-secret-key-2024'

# Demo users
DEMO_USERS = {
    'admin': 'admin123',
    'user': 'user123'
}

@app.route('/')
def home():
    if 'logged_in' in session:
        return dashboard()
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username in DEMO_USERS and DEMO_USERS[username] == password:
            session['logged_in'] = True
            session['username'] = username
            flash(f'مرحباً {username}! تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - نظام المراسلات الإلكترونية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .login-container {
                background: white;
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                overflow: hidden;
                max-width: 450px;
                width: 100%;
            }
            .login-header {
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                padding: 40px 30px;
                text-align: center;
            }
            .login-body {
                padding: 40px;
            }
            .form-control {
                border-radius: 10px;
                border: 2px solid #e9ecef;
                padding: 15px;
                margin-bottom: 20px;
                font-size: 16px;
            }
            .form-control:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
            }
            .btn-login {
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
                width: 100%;
                color: white;
                transition: transform 0.2s;
                font-size: 16px;
            }
            .btn-login:hover {
                transform: translateY(-2px);
                color: white;
            }
            .demo-info {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin-top: 25px;
                border-left: 4px solid #28a745;
            }
            .input-group-text {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-left: none;
            }
            .alert {
                border-radius: 10px;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="login-container">
                        <div class="login-header">
                            <h2><i class="fas fa-envelope me-3"></i>تسجيل الدخول</h2>
                            <p class="mb-0">نظام المراسلات الإلكترونية</p>
                        </div>
                        <div class="login-body">
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">اسم المستخدم</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" name="username" placeholder="أدخل اسم المستخدم" required>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <label class="form-label fw-bold">كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" name="password" placeholder="أدخل كلمة المرور" required>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول
                                </button>
                            </form>
                            
                            <div class="demo-info">
                                <h6><i class="fas fa-info-circle text-success me-2"></i>بيانات تجريبية</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <p class="mb-1"><strong>مدير:</strong></p>
                                        <small>admin / admin123</small>
                                    </div>
                                    <div class="col-6">
                                        <p class="mb-1"><strong>مستخدم:</strong></p>
                                        <small>user / user123</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''')

@app.route('/dashboard')
def dashboard():
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    username = session.get('username', 'مستخدم')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم - نظام المراسلات الإلكترونية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: #f8f9fa;
            }
            .navbar { 
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .hero { 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                color: white; 
                padding: 60px 0; 
            }
            .feature-card { 
                transition: transform 0.3s;
                border: none;
                border-radius: 15px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            }
            .feature-card:hover { 
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            }
            .stats-card {
                background: white;
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-envelope me-2"></i>
                    نظام المراسلات الإلكترونية
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>{{ username }}
                    </span>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="hero text-center">
            <div class="container">
                <h1 class="display-5 mb-4">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    لوحة التحكم
                </h1>
                <p class="lead">مرحباً {{ username }}، أهلاً بك في نظام المراسلات الإلكترونية</p>
                
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="row justify-content-center mt-4">
                            <div class="col-md-6">
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                {% endwith %}
            </div>
        </section>

        <!-- Statistics -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                            <h3>150</h3>
                            <p class="text-muted">إجمالي الرسائل</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-inbox fa-3x text-success mb-3"></i>
                            <h3>45</h3>
                            <p class="text-muted">رسائل واردة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-paper-plane fa-3x text-warning mb-3"></i>
                            <h3>32</h3>
                            <p class="text-muted">رسائل صادرة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <i class="fas fa-archive fa-3x text-info mb-3"></i>
                            <h3>73</h3>
                            <p class="text-muted">رسائل مؤرشفة</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="py-5 bg-light">
            <div class="container">
                <h2 class="text-center mb-5">الميزات المتاحة</h2>
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                                <h5>إدارة الرسائل</h5>
                                <p>إنشاء وتعديل الرسائل الواردة والصادرة والداخلية</p>
                                <button class="btn btn-primary">دخول</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-signature fa-3x text-success mb-3"></i>
                                <h5>التوقيع الإلكتروني</h5>
                                <p>إنشاء وإدارة التوقيعات الرقمية والصور</p>
                                <button class="btn btn-success">دخول</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-archive fa-3x text-info mb-3"></i>
                                <h5>نظام الأرشيف</h5>
                                <p>أرشفة منظمة للرسائل حسب السنة والشهر</p>
                                <button class="btn btn-info">دخول</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-rocket me-2"></i>للنظام الكامل</h5>
                        <p class="mb-2">لتشغيل النظام الكامل مع جميع الميزات:</p>
                        <code>python app.py</code>
                    </div>
                </div>
            </div>
        </section>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''', username=username)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/test')
def test():
    return {
        'status': 'success',
        'message': 'النظام يعمل مع صفحة الدخول',
        'login_url': '/login',
        'demo_users': list(DEMO_USERS.keys())
    }

if __name__ == '__main__':
    print("🚀 تشغيل النظام مع صفحة الدخول...")
    print("🔐 صفحة الدخول: http://localhost:8585/login")
    print("🏠 الصفحة الرئيسية: http://localhost:8585")
    print("🧪 اختبار API: http://localhost:8585/test")
    print("=" * 50)
    print("بيانات الدخول:")
    print("👤 مدير: admin / admin123")
    print("👤 مستخدم: user / user123")
    print("=" * 50)

    app.run(host='127.0.0.1', port=8585, debug=True)
