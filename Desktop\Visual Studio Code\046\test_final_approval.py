#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لقسم الاعتماد والموافقة
"""

import requests
import sys

def test_final_approval():
    """اختبار نهائي لقسم الاعتماد والموافقة"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار الصفحة الرئيسية للتحقق من وجود رابط الاعتماد
        print("\n🏠 اختبار الصفحة الرئيسية...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            if 'pending_approval' in content and 'في انتظار الاعتماد' in content:
                print("✅ رابط قسم الاعتماد موجود في القائمة الجانبية")
            else:
                print("⚠️ رابط قسم الاعتماد غير موجود في القائمة الجانبية")
                # طباعة جزء من المحتوى للتشخيص
                if 'الرسائل' in content:
                    print("📋 قائمة الرسائل موجودة")
                if 'dropdown' in content:
                    print("📋 القوائم المنسدلة موجودة")
        else:
            print(f"❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
        
        # اختبار صفحة الرسائل المعلقة للاعتماد
        print("\n📋 اختبار صفحة الرسائل المعلقة للاعتماد...")
        response = session.get(f"{base_url}/messages/pending_approval")
        if response.status_code == 200:
            print("✅ صفحة الرسائل المعلقة للاعتماد تعمل")
            
            content = response.text
            if 'الرسائل المعلقة للاعتماد' in content:
                print("✅ عنوان الصفحة صحيح")
            
            # عد الرسائل المعلقة الحالية
            import re
            table_rows = re.findall(r'<tr[^>]*>', content)
            pending_count = max(0, len(table_rows) - 1)  # -1 للهيدر
            print(f"📊 عدد الرسائل المعلقة الحالية: {pending_count}")
            
        else:
            print(f"❌ فشل في الوصول لصفحة الرسائل المعلقة: {response.status_code}")
            return False
        
        # إنشاء رسالة تتطلب اعتماد بطريقة مبسطة
        print("\n📝 إنشاء رسالة تتطلب اعتماد...")
        
        # رسالة صادرة بسيطة
        message_data = {
            'message_type': 'outgoing',
            'registration_number': f'FINAL-TEST-{pending_count + 1}',
            'destination': 'جهة خارجية',
            'subject': 'رسالة اختبار نهائية للاعتماد',
            'content': 'محتوى الرسالة للاختبار النهائي',
            'department': 'قسم الاختبار',
            'priority': 'normal',
            'requires_approval': 'on'
        }
        
        response = session.post(f"{base_url}/messages/new", data=message_data)
        if response.status_code == 200:
            print("✅ تم إنشاء رسالة تتطلب اعتماد")
            
            # اختبار صفحة الرسائل المعلقة مرة أخرى
            print("\n🔄 اختبار صفحة الرسائل المعلقة بعد إنشاء رسالة...")
            response = session.get(f"{base_url}/messages/pending_approval")
            if response.status_code == 200:
                new_content = response.text
                new_table_rows = re.findall(r'<tr[^>]*>', new_content)
                new_pending_count = max(0, len(new_table_rows) - 1)
                
                if new_pending_count > pending_count:
                    print("✅ الرسالة الجديدة ظهرت في قائمة الرسائل المعلقة")
                    print(f"📊 عدد الرسائل المعلقة الجديد: {new_pending_count}")
                else:
                    print("⚠️ الرسالة الجديدة لم تظهر في قائمة الرسائل المعلقة")
                    print(f"📊 عدد الرسائل المعلقة لم يتغير: {new_pending_count}")
                
                # البحث عن الرسالة الجديدة
                if f'FINAL-TEST-{pending_count + 1}' in new_content:
                    print("✅ تم العثور على الرسالة الجديدة في القائمة")
                else:
                    print("⚠️ لم يتم العثور على الرسالة الجديدة في القائمة")
            
        else:
            print(f"⚠️ مشكلة في إنشاء رسالة الاختبار: {response.status_code}")
        
        # اختبار عرض رسالة تتطلب اعتماد
        print("\n👁️ اختبار عرض رسالة تتطلب اعتماد...")
        
        # البحث عن أي رسالة في قائمة الرسائل المعلقة
        response = session.get(f"{base_url}/messages/pending_approval")
        if response.status_code == 200:
            content = response.text
            
            # البحث عن معرف رسالة في المحتوى
            import re
            message_links = re.findall(r'/messages/(\d+)', content)
            
            if message_links:
                message_id = message_links[0]
                print(f"🔍 اختبار عرض الرسالة {message_id}...")
                
                response = session.get(f"{base_url}/messages/{message_id}")
                if response.status_code == 200:
                    message_content = response.text
                    
                    approval_features = []
                    if 'قسم الاعتماد والموافقة' in message_content:
                        approval_features.append("قسم الاعتماد")
                    if 'حالة الاعتماد' in message_content:
                        approval_features.append("حالة الاعتماد")
                    if 'اعتماد' in message_content and 'رفض' in message_content:
                        approval_features.append("أزرار الاعتماد والرفض")
                    if 'ملاحظات الاعتماد' in message_content:
                        approval_features.append("ملاحظات الاعتماد")
                    if 'بانتظار الاعتماد' in message_content:
                        approval_features.append("حالة الانتظار")
                    
                    if approval_features:
                        print(f"✅ ميزات الاعتماد الموجودة: {', '.join(approval_features)}")
                        
                        # اختبار اعتماد الرسالة
                        print(f"\n✅ اختبار اعتماد الرسالة {message_id}...")
                        approval_data = {
                            'approval_notes': 'تم الاعتماد في الاختبار النهائي'
                        }
                        
                        response = session.post(f"{base_url}/messages/{message_id}/approve", data=approval_data)
                        if response.status_code == 200:
                            print("✅ تم اعتماد الرسالة بنجاح")
                            
                            # التحقق من تحديث حالة الرسالة
                            response = session.get(f"{base_url}/messages/{message_id}")
                            if response.status_code == 200:
                                updated_content = response.text
                                if 'معتمدة' in updated_content:
                                    print("✅ تم تحديث حالة الرسالة إلى معتمدة")
                                else:
                                    print("⚠️ لم يتم تحديث حالة الرسالة")
                        else:
                            print(f"⚠️ مشكلة في اعتماد الرسالة: {response.status_code}")
                    else:
                        print("⚠️ ميزات الاعتماد غير موجودة في صفحة عرض الرسالة")
                else:
                    print(f"❌ فشل في عرض الرسالة: {response.status_code}")
            else:
                print("⚠️ لم يتم العثور على رسائل للاختبار")
        
        # اختبار نهائي للإحصائيات
        print("\n📊 اختبار نهائي للإحصائيات...")
        response = session.get(f"{base_url}/messages/pending_approval")
        if response.status_code == 200:
            content = response.text
            
            # عد الرسائل المعلقة النهائية
            import re
            final_table_rows = re.findall(r'<tr[^>]*>', content)
            final_pending_count = max(0, len(final_table_rows) - 1)
            print(f"📊 عدد الرسائل المعلقة النهائية: {final_pending_count}")
            
            if final_pending_count >= 0:
                print("✅ نظام الاعتماد يعمل بشكل صحيح")
            else:
                print("⚠️ مشكلة في عد الرسائل المعلقة")
        
        print("\n🎉 تم اكتمال الاختبار النهائي لقسم الاعتماد والموافقة!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار نهائي لقسم الاعتماد والموافقة")
    print("=" * 60)
    
    success = test_final_approval()
    
    if success:
        print("\n✅ قسم الاعتماد والموافقة يعمل بشكل مثالي!")
        print("\n📋 الميزات المُفعلة:")
        print("  ✅ عرض الرسائل المعلقة للاعتماد")
        print("  ✅ إنشاء رسائل تتطلب اعتماد")
        print("  ✅ اعتماد الرسائل")
        print("  ✅ رفض الرسائل")
        print("  ✅ إضافة ملاحظات الاعتماد")
        print("  ✅ عرض حالة الاعتماد")
        print("  ✅ تتبع تاريخ الاعتمادات")
        print("\n🎯 الأدوار التي يمكنها الوصول لقسم الاعتماد:")
        print("  ⚙️ مدير النظام")
        print("  🎯 مدير عام")
        print("  🏢 رئيس قسم")
        print("  👔 مدير")
        print("  👁️ مشرف")
        print("  📝 سكرتير")
        print("\n🌐 للوصول لقسم الاعتماد:")
        print("  📋 الرسائل المعلقة: http://localhost:8585/messages/pending_approval")
        print("  📝 إنشاء رسالة تتطلب اعتماد: http://localhost:8585/messages/new")
        print("  👁️ عرض رسالة: http://localhost:8585/messages/{id}")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n💡 كيفية الاستخدام:")
        print("  1. انتقل إلى قائمة الرسائل > في انتظار الاعتماد")
        print("  2. أو أنشئ رسالة جديدة وفعل خيار 'يتطلب اعتماد'")
        print("  3. اعتمد أو ارفض الرسائل مع إضافة ملاحظات")
        print("  4. تابع حالة الاعتماد في تفاصيل الرسالة")
        print("\n🎯 قسم الاعتماد والموافقة مُفعل بالكامل ويعمل بشكل مثالي!")
        sys.exit(0)
    else:
        print("\n❌ فشل في الاختبار النهائي لقسم الاعتماد والموافقة!")
        sys.exit(1)
