{% extends "base.html" %}

{% block title %}مستخدم جديد - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-plus me-2"></i>
                إنشاء مستخدم جديد
            </h1>
            <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الاسم الكامل
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المستخدم
                            </div>
                            <div class="form-text">
                                يجب أن يكون اسم المستخدم فريداً
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <div class="invalid-feedback">
                                كلمة المرور يجب أن تكون 6 أحرف على الأقل
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback">
                                يرجى تأكيد كلمة المرور
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="employee">موظف</option>
                                <option value="secretary">سكرتير</option>
                                {% if current_user.is_manager() %}
                                <option value="manager">مدير</option>
                                {% endif %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار دور المستخدم
                            </div>
                            <div class="form-text">
                                <strong>الموظف:</strong> يمكنه عرض وإنشاء الرسائل<br>
                                <strong>السكرتير:</strong> يمكنه إدارة المستخدمين والرسائل<br>
                                <strong>المدير:</strong> جميع الصلاحيات
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="">اختر القسم (اختياري)</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}">{{ department.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                يمكن تعيين المستخدم لقسم معين لتسهيل إدارة المراسلات الداخلية
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إنشاء المستخدم
                        </button>
                        <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    var passwordField = document.getElementById('password');
    var confirmPasswordField = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (passwordField.value !== confirmPasswordField.value) {
            confirmPasswordField.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            confirmPasswordField.setCustomValidity('');
        }
    }
    
    passwordField.addEventListener('input', validatePasswords);
    confirmPasswordField.addEventListener('input', validatePasswords);
    
    // Username validation
    var usernameField = document.getElementById('username');
    usernameField.addEventListener('input', function() {
        // Remove spaces and special characters
        this.value = this.value.replace(/[^a-zA-Z0-9_]/g, '');
    });
});
</script>
{% endblock %}
