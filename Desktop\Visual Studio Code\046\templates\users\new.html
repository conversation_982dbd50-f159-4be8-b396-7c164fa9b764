{% extends "base.html" %}

{% block title %}مستخدم جديد - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-plus me-2"></i>
                إنشاء مستخدم جديد
            </h1>
            <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الاسم الكامل
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المستخدم
                            </div>
                            <div class="form-text">
                                يجب أن يكون اسم المستخدم فريداً
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <div class="invalid-feedback">
                                كلمة المرور يجب أن تكون 6 أحرف على الأقل
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback">
                                يرجى تأكيد كلمة المرور
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="EMPLOYEE">موظف</option>
                                <option value="SECRETARY">سكرتير</option>
                                <option value="SUPERVISOR">مشرف</option>
                                {% if current_user.is_manager() or current_user.is_admin() %}
                                <option value="MANAGER">مدير</option>
                                <option value="DEPARTMENT_HEAD">رئيس قسم</option>
                                {% endif %}
                                {% if current_user.is_admin() %}
                                <option value="DIRECTOR">مدير عام</option>
                                <option value="ADMIN">مدير النظام</option>
                                {% endif %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار دور المستخدم
                            </div>
                            <div class="form-text">
                                <strong>الموظف:</strong> يمكنه عرض وإنشاء الرسائل الأساسية<br>
                                <strong>السكرتير:</strong> يمكنه إدارة المستخدمين والرسائل وأرشفتها<br>
                                <strong>مدير القسم:</strong> يمكنه إدارة قسمه وحذف الرسائل واعتمادها<br>
                                <strong>مدير النظام:</strong> جميع الصلاحيات والتحكم الكامل في النظام
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="">اختر القسم (اختياري)</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}">{{ department.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                يمكن تعيين المستخدم لقسم معين لتسهيل إدارة المراسلات الداخلية
                            </div>
                        </div>
                    </div>

                    <!-- مسؤول الاعتماد وصلاحيات الاعتماد -->
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label for="approval_manager_id" class="form-label">مسؤول الاعتماد</label>
                            <select class="form-select" id="approval_manager_id" name="approval_manager_id">
                                <option value="">اختر مسؤول الاعتماد (اختياري)</option>
                                {% for manager in managers %}
                                <option value="{{ manager.id }}">{{ manager.name }} - {{ manager.department.name if manager.department else 'بدون قسم' }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                <i class="fas fa-user-check me-1"></i>
                                المسؤول الذي سيعتمد رسائل هذا المستخدم
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <label for="can_approve_others" class="form-label">صلاحيات الاعتماد</label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" id="can_approve_others" name="can_approve_others">
                                <label class="form-check-label" for="can_approve_others">
                                    يمكنه اعتماد رسائل الآخرين
                                </label>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-shield-check me-1"></i>
                                فعل هذا الخيار إذا كان المستخدم مسؤولاً عن اعتماد رسائل مستخدمين آخرين
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إنشاء المستخدم
                        </button>
                        <a href="{{ url_for('users.list_users') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    var passwordField = document.getElementById('password');
    var confirmPasswordField = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (passwordField.value !== confirmPasswordField.value) {
            confirmPasswordField.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            confirmPasswordField.setCustomValidity('');
        }
    }
    
    passwordField.addEventListener('input', validatePasswords);
    confirmPasswordField.addEventListener('input', validatePasswords);
    
    // Username validation
    var usernameField = document.getElementById('username');
    usernameField.addEventListener('input', function() {
        // Remove spaces and special characters
        this.value = this.value.replace(/[^a-zA-Z0-9_]/g, '');
    });
});
</script>
{% endblock %}
