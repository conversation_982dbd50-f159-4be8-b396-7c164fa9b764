{% extends "base.html" %}

{% block title %}الصفحة غير موجودة - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="text-center py-5">
            <div class="error-icon mb-4">
                <i class="fas fa-exclamation-triangle fa-5x text-warning"></i>
            </div>
            
            <h1 class="display-4 text-muted">404</h1>
            <h2 class="mb-3">الصفحة غير موجودة</h2>
            
            <p class="lead text-muted mb-4">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
            </p>
            
            <div class="d-flex justify-content-center gap-3">
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>
                    العودة للرئيسية
                </a>
                
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للخلف
                </button>
                
                <a href="{{ url_for('search') }}" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>
                    البحث
                </a>
            </div>
            
            <div class="mt-5">
                <h5>روابط مفيدة:</h5>
                <div class="list-group list-group-horizontal justify-content-center">
                    <a href="{{ url_for('messages.list_messages') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-envelope me-2"></i>الرسائل
                    </a>
                    {% if current_user.is_authenticated and current_user.can_manage_users() %}
                    <a href="{{ url_for('users.list_users') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i>المستخدمون
                    </a>
                    {% endif %}
                    <a href="{{ url_for('messages.new_message') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus me-2"></i>رسالة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.error-icon {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}
</style>
{% endblock %}
