#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل للأدوار الجديدة في النظام
"""

import requests
import sys

def test_complete_roles():
    """اختبار شامل للأدوار الجديدة"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار قائمة المستخدمين مع الأدوار الجديدة
        print("\n👥 اختبار قائمة المستخدمين مع الأدوار الجديدة...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            print("✅ صفحة قائمة المستخدمين تعمل")
            
            content = response.text
            roles_displayed = []
            
            if 'مدير النظام' in content:
                roles_displayed.append("مدير النظام")
            if 'مدير عام' in content:
                roles_displayed.append("مدير عام")
            if 'رئيس قسم' in content:
                roles_displayed.append("رئيس قسم")
            if 'مشرف' in content:
                roles_displayed.append("مشرف")
            
            print(f"✅ الأدوار المعروضة في القائمة: {', '.join(roles_displayed) if roles_displayed else 'الأدوار الأساسية فقط'}")
            
            # التحقق من الإحصائيات
            stats_found = []
            if 'مدراء النظام' in content:
                stats_found.append("إحصائيات مدراء النظام")
            if 'مدراء عامون' in content:
                stats_found.append("إحصائيات مدراء عامون")
            if 'رؤساء أقسام' in content:
                stats_found.append("إحصائيات رؤساء أقسام")
            if 'مشرفون' in content:
                stats_found.append("إحصائيات مشرفون")
            
            print(f"✅ الإحصائيات المعروضة: {', '.join(stats_found) if stats_found else 'الإحصائيات الأساسية فقط'}")
        else:
            print(f"❌ فشل في الوصول لقائمة المستخدمين: {response.status_code}")
            return False
        
        # اختبار إنشاء مستخدمين بجميع الأدوار الجديدة
        print("\n➕ اختبار إنشاء مستخدمين بجميع الأدوار...")
        
        all_roles = [
            ('test_employee_new', 'موظف جديد', 'EMPLOYEE'),
            ('test_secretary_new', 'سكرتير جديد', 'SECRETARY'),
            ('test_supervisor_new', 'مشرف جديد', 'SUPERVISOR'),
            ('test_manager_new', 'مدير جديد', 'MANAGER'),
            ('test_dept_head_new', 'رئيس قسم جديد', 'DEPARTMENT_HEAD'),
            ('test_director_new', 'مدير عام جديد', 'DIRECTOR')
        ]
        
        created_count = 0
        for username, name, role in all_roles:
            user_data = {
                'username': username,
                'name': name,
                'password': 'password123',
                'role': role,
                'is_active': 'on',
                'department_id': '1'
            }
            
            response = session.post(f"{base_url}/users/new", data=user_data)
            if response.status_code == 200:
                print(f"✅ تم إنشاء {name} بدور {role}")
                created_count += 1
            else:
                print(f"⚠️ مشكلة في إنشاء {name}: {response.status_code}")
        
        print(f"✅ تم إنشاء {created_count} من أصل {len(all_roles)} مستخدمين")
        
        # اختبار تعديل الأدوار
        print("\n✏️ اختبار تعديل أدوار المستخدمين...")
        
        # جرب تعديل عدة مستخدمين
        role_changes = [
            (2, 'SUPERVISOR', 'مشرف'),
            (3, 'MANAGER', 'مدير'),
            (4, 'DEPARTMENT_HEAD', 'رئيس قسم')
        ]
        
        edit_success = 0
        for user_id, new_role, role_name in role_changes:
            update_data = {
                'name': f'مستخدم محدث {user_id}',
                'role': new_role,
                'is_active': 'on',
                'department_id': '1'
            }
            
            response = session.post(f"{base_url}/users/{user_id}/edit", data=update_data)
            if response.status_code == 200:
                print(f"✅ تم تحديث المستخدم {user_id} إلى {role_name}")
                edit_success += 1
            else:
                print(f"⚠️ مشكلة في تحديث المستخدم {user_id}: {response.status_code}")
        
        print(f"✅ تم تحديث {edit_success} من أصل {len(role_changes)} مستخدمين")
        
        # اختبار صفحة تعديل المستخدم
        print("\n🔧 اختبار صفحة تعديل المستخدم...")
        response = session.get(f"{base_url}/users/1/edit")
        if response.status_code == 200:
            content = response.text
            
            # التحقق من وجود جميع الأدوار
            roles_in_edit = []
            if 'value="EMPLOYEE"' in content:
                roles_in_edit.append("موظف")
            if 'value="SECRETARY"' in content:
                roles_in_edit.append("سكرتير")
            if 'value="SUPERVISOR"' in content:
                roles_in_edit.append("مشرف")
            if 'value="MANAGER"' in content:
                roles_in_edit.append("مدير")
            if 'value="DEPARTMENT_HEAD"' in content:
                roles_in_edit.append("رئيس قسم")
            if 'value="DIRECTOR"' in content:
                roles_in_edit.append("مدير عام")
            if 'value="ADMIN"' in content:
                roles_in_edit.append("مدير النظام")
            
            print(f"✅ الأدوار المتاحة في التعديل: {', '.join(roles_in_edit)}")
            
            # التحقق من أوصاف الأدوار
            descriptions_found = []
            if 'المشرف:' in content:
                descriptions_found.append("وصف المشرف")
            if 'رئيس القسم:' in content:
                descriptions_found.append("وصف رئيس القسم")
            if 'المدير العام:' in content:
                descriptions_found.append("وصف المدير العام")
            
            print(f"✅ أوصاف الأدوار الجديدة: {', '.join(descriptions_found) if descriptions_found else 'غير موجودة'}")
        else:
            print(f"❌ فشل في الوصول لصفحة تعديل المستخدم: {response.status_code}")
        
        # اختبار الإحصائيات النهائية
        print("\n📊 اختبار الإحصائيات النهائية...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            content = response.text
            print("✅ تم تحديث الإحصائيات بنجاح")
            
            # عد الأدوار في المحتوى
            role_counts = {}
            if 'مدير النظام' in content:
                role_counts['مدير النظام'] = content.count('مدير النظام')
            if 'مدير عام' in content:
                role_counts['مدير عام'] = content.count('مدير عام')
            if 'رئيس قسم' in content:
                role_counts['رئيس قسم'] = content.count('رئيس قسم')
            if 'مشرف' in content:
                role_counts['مشرف'] = content.count('مشرف')
            
            if role_counts:
                print("✅ الأدوار الجديدة معروضة في النظام")
            else:
                print("⚠️ الأدوار الجديدة غير معروضة بوضوح")
        
        print("\n🎉 تم اكتمال الاختبار الشامل للأدوار الجديدة!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل للأدوار الجديدة في النظام")
    print("=" * 70)
    
    success = test_complete_roles()
    
    if success:
        print("\n✅ جميع الأدوار الجديدة تعمل بشكل مثالي!")
        print("\n📋 الأدوار المتاحة في النظام:")
        print("  👤 موظف (EMPLOYEE) - الدور الأساسي للموظفين العاديين")
        print("  📝 سكرتير (SECRETARY) - إدارة المستخدمين والرسائل وأرشفتها")
        print("  👁️ مشرف (SUPERVISOR) - اعتماد الرسائل وإدارة المستخدمين")
        print("  👔 مدير (MANAGER) - جميع الصلاحيات بما في ذلك حذف الرسائل والتقارير")
        print("  🏢 رئيس قسم (DEPARTMENT_HEAD) - إدارة كاملة للقسم والمستخدمين والرسائل")
        print("  🎯 مدير عام (DIRECTOR) - إدارة جميع الأقسام والمستخدمين والنظام")
        print("  ⚙️ مدير النظام (ADMIN) - جميع صلاحيات النظام والإدارة والنسخ الاحتياطي")
        print("\n🔐 الصلاحيات حسب الدور:")
        print("  📊 التقارير: مدير، رئيس قسم، مدير عام، مدير النظام")
        print("  🗑️ حذف الرسائل: مدير، رئيس قسم، مدير عام، مدير النظام")
        print("  ✅ اعتماد الرسائل: مشرف، مدير، رئيس قسم، مدير عام، مدير النظام")
        print("  👥 إدارة المستخدمين: سكرتير، مشرف، مدير، رئيس قسم، مدير عام، مدير النظام")
        print("  🏗️ إدارة الأقسام: رئيس قسم، مدير عام، مدير النظام")
        print("  ⚙️ إدارة النظام: مدير عام، مدير النظام")
        print("\n🎨 ألوان الأدوار في الواجهة:")
        print("  🖤 مدير النظام - أسود")
        print("  🟣 مدير عام - بنفسجي")
        print("  🔵 رئيس قسم - أزرق")
        print("  🔴 مدير - أحمر")
        print("  🟢 مشرف - أخضر")
        print("  🟡 سكرتير - أصفر")
        print("  🔵 موظف - أزرق فاتح")
        print("\n🌐 للوصول لإدارة المستخدمين:")
        print("  📋 قائمة المستخدمين: http://localhost:8585/users")
        print("  ➕ إنشاء مستخدم: http://localhost:8585/users/new")
        print("  ✏️ تعديل مستخدم: http://localhost:8585/users/{id}/edit")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n🎯 تم إضافة 3 أدوار جديدة بنجاح:")
        print("  ✨ مشرف (SUPERVISOR)")
        print("  ✨ رئيس قسم (DEPARTMENT_HEAD)")
        print("  ✨ مدير عام (DIRECTOR)")
        print("\n🚀 النظام الآن يدعم 7 أدوار مختلفة مع صلاحيات متدرجة!")
        sys.exit(0)
    else:
        print("\n❌ فشل في اختبار الأدوار الجديدة!")
        sys.exit(1)
