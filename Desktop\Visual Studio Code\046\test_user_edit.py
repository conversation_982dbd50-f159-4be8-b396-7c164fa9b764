#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تعديل المستخدمين
"""

import requests
import sys

def test_user_edit():
    """اختبار تعديل المستخدمين"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة قائمة المستخدمين
        print("\n👥 اختبار صفحة قائمة المستخدمين...")
        response = session.get(f"{base_url}/users")
        if response.status_code == 200:
            print("✅ صفحة قائمة المستخدمين تعمل")
            
            content = response.text
            if 'تعديل' in content and 'edit' in content:
                print("✅ تم العثور على أزرار التعديل في القائمة")
            else:
                print("⚠️ لم يتم العثور على أزرار التعديل")
                
            # البحث عن روابط التعديل
            if '/users/' in content and '/edit' in content:
                print("✅ تم العثور على روابط التعديل")
            else:
                print("⚠️ لم يتم العثور على روابط التعديل")
        else:
            print(f"❌ فشل في الوصول لقائمة المستخدمين: {response.status_code}")
            return False
        
        # اختبار تعديل مستخدم موجود
        print("\n✏️ اختبار صفحة تعديل المستخدم...")
        
        # جرب تعديل المستخدمين من 1 إلى 5
        edit_success = False
        for user_id in range(1, 6):
            response = session.get(f"{base_url}/users/{user_id}/edit")
            if response.status_code == 200:
                print(f"✅ صفحة تعديل المستخدم {user_id} تعمل")
                
                content = response.text
                if 'تعديل المستخدم' in content:
                    print("✅ تم العثور على محتوى صفحة التعديل")
                    
                if 'name' in content and 'اسم المستخدم' in content:
                    print("✅ تم العثور على حقل الاسم")
                    
                if 'role' in content and 'الدور' in content:
                    print("✅ تم العثور على حقل الدور")
                    
                if 'department' in content and 'القسم' in content:
                    print("✅ تم العثور على حقل القسم")
                    
                if 'حفظ التغييرات' in content or 'تحديث' in content:
                    print("✅ تم العثور على زر الحفظ")
                
                edit_success = True
                
                # اختبار تحديث المستخدم
                print(f"\n💾 اختبار تحديث المستخدم {user_id}...")
                update_data = {
                    'name': f'مستخدم محدث {user_id}',
                    'role': 'USER',
                    'is_active': 'on',
                    'department_id': '1'
                }
                
                response = session.post(f"{base_url}/users/{user_id}/edit", data=update_data)
                if response.status_code == 200:
                    print(f"✅ تم تحديث المستخدم {user_id} بنجاح")
                else:
                    print(f"⚠️ مشكلة في تحديث المستخدم {user_id}: {response.status_code}")
                
                break
            elif response.status_code == 404:
                print(f"⚠️ المستخدم {user_id} غير موجود")
            else:
                print(f"❌ خطأ في الوصول لتعديل المستخدم {user_id}: {response.status_code}")
        
        if not edit_success:
            print("❌ لم يتم العثور على أي مستخدم قابل للتعديل")
            return False
        
        # اختبار إنشاء مستخدم جديد للتأكد من أن التعديل سيعمل عليه
        print("\n➕ اختبار إنشاء مستخدم جديد...")
        new_user_data = {
            'username': 'test_edit_user',
            'name': 'مستخدم اختبار التعديل',
            'password': 'password123',
            'role': 'USER',
            'is_active': 'on',
            'department_id': '1'
        }
        
        response = session.post(f"{base_url}/users/new", data=new_user_data)
        if response.status_code == 200:
            print("✅ تم إنشاء مستخدم جديد للاختبار")
            
            # الآن نحاول تعديل المستخدم الجديد
            print("\n✏️ اختبار تعديل المستخدم الجديد...")
            
            # نحتاج للعثور على معرف المستخدم الجديد
            response = session.get(f"{base_url}/users")
            if response.status_code == 200:
                content = response.text
                # البحث عن المستخدم الجديد في المحتوى
                if 'test_edit_user' in content:
                    print("✅ تم العثور على المستخدم الجديد في القائمة")
                    
                    # محاولة تعديل المستخدم الجديد (نجرب معرفات مختلفة)
                    for test_user_id in range(1, 20):
                        response = session.get(f"{base_url}/users/{test_user_id}/edit")
                        if response.status_code == 200 and 'test_edit_user' in response.text:
                            print(f"✅ تم العثور على صفحة تعديل المستخدم الجديد (معرف: {test_user_id})")
                            
                            # اختبار تحديث المستخدم الجديد
                            update_data = {
                                'name': 'مستخدم اختبار التعديل - محدث',
                                'role': 'MANAGER',
                                'is_active': 'on',
                                'department_id': '2'
                            }
                            
                            response = session.post(f"{base_url}/users/{test_user_id}/edit", data=update_data)
                            if response.status_code == 200:
                                print("✅ تم تحديث المستخدم الجديد بنجاح")
                            else:
                                print(f"⚠️ مشكلة في تحديث المستخدم الجديد: {response.status_code}")
                            break
        else:
            print(f"⚠️ مشكلة في إنشاء مستخدم جديد: {response.status_code}")
        
        # اختبار تغيير كلمة المرور
        print("\n🔑 اختبار تغيير كلمة المرور...")
        for user_id in range(1, 6):
            response = session.get(f"{base_url}/users/{user_id}/edit")
            if response.status_code == 200:
                content = response.text
                if 'new_password' in content or 'كلمة المرور الجديدة' in content:
                    print("✅ حقل تغيير كلمة المرور متاح")
                    
                    # اختبار تغيير كلمة المرور
                    update_data = {
                        'name': f'مستخدم {user_id}',
                        'role': 'USER',
                        'is_active': 'on',
                        'department_id': '1',
                        'new_password': 'newpassword123'
                    }
                    
                    response = session.post(f"{base_url}/users/{user_id}/edit", data=update_data)
                    if response.status_code == 200:
                        print("✅ تم تغيير كلمة المرور بنجاح")
                    else:
                        print(f"⚠️ مشكلة في تغيير كلمة المرور: {response.status_code}")
                    break
                else:
                    print("⚠️ حقل تغيير كلمة المرور غير موجود")
                break
        
        print("\n🎉 تم اكتمال اختبار تعديل المستخدمين!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار تعديل المستخدمين")
    print("=" * 60)
    
    success = test_user_edit()
    
    if success:
        print("\n✅ اختبار تعديل المستخدمين نجح!")
        print("\n📋 النتائج:")
        print("  • صفحة قائمة المستخدمين تعمل")
        print("  • أزرار التعديل موجودة ومرئية")
        print("  • صفحة تعديل المستخدم تعمل")
        print("  • نموذج التعديل مكتمل")
        print("  • عملية التحديث تعمل")
        print("  • تغيير كلمة المرور يعمل")
        print("\n🌐 للوصول لإدارة المستخدمين:")
        print("  http://localhost:8585/users")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        sys.exit(0)
    else:
        print("\n❌ فشل في اختبار تعديل المستخدمين!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم")
        print("  • صحة routes التعديل")
        print("  • وجود قوالب التعديل")
        sys.exit(1)
