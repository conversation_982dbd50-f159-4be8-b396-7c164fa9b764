{% extends "base.html" %}

{% block title %}معاينة الملف - {{ file_info.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="fas fa-eye me-2"></i>
                        معاينة الملف
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('index') }}">الرئيسية</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('messages.list_messages') }}">الرسائل</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('messages.view_message', id=file_info.message_id) }}">
                                    {{ file_info.message_subject[:50] }}...
                                </a>
                            </li>
                            <li class="breadcrumb-item active">معاينة الملف</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <a href="{{ url_for('messages.view_message', id=file_info.message_id) }}" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للرسالة
                    </a>
                    <a href="{{ url_for('messages.download_attachment', filename=filename) }}" 
                       class="btn btn-success">
                        <i class="fas fa-download me-1"></i>تحميل
                    </a>
                    <a href="{{ url_for('messages.view_attachment', filename=filename) }}" 
                       class="btn btn-primary" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>فتح في نافذة جديدة
                    </a>
                </div>
            </div>

            <!-- File Information Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الملف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="fas {{ get_file_icon(file_info.name) }} fa-4x text-primary mb-3"></i>
                                <h6 class="text-muted">نوع الملف</h6>
                                <span class="badge bg-primary">
                                    {{ file_info.name.split('.')[-1].upper() if '.' in file_info.name else 'غير محدد' }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">اسم الملف:</td>
                                            <td>{{ file_info.name }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">الحجم:</td>
                                            <td>{{ file_info.size_formatted }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">تاريخ الرفع:</td>
                                            <td>{{ file_info.upload_date }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">الرسالة:</td>
                                            <td>
                                                <a href="{{ url_for('messages.view_message', id=file_info.message_id) }}" 
                                                   class="text-decoration-none">
                                                    {{ file_info.message_subject[:30] }}...
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">معرف الرسالة:</td>
                                            <td>#{{ file_info.message_id }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">الحالة:</td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>متاح
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Preview Card -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        معاينة المحتوى
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleFullscreen()">
                            <i class="fas fa-expand" id="fullscreen-icon"></i>
                            <span id="fullscreen-text">ملء الشاشة</span>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="printPreview()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
                <div class="card-body p-0" id="preview-container">
                    <div class="preview-content" style="min-height: 400px; max-height: 800px; overflow-y: auto;">
                        {{ content|safe }}
                    </div>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <a href="{{ url_for('messages.download_attachment', filename=filename) }}" 
                               class="btn btn-success btn-lg w-100">
                                <i class="fas fa-download fa-2x d-block mb-2"></i>
                                تحميل الملف
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('messages.view_attachment', filename=filename) }}" 
                               class="btn btn-primary btn-lg w-100" target="_blank">
                                <i class="fas fa-external-link-alt fa-2x d-block mb-2"></i>
                                فتح في نافذة جديدة
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-info btn-lg w-100" onclick="shareFile()">
                                <i class="fas fa-share-alt fa-2x d-block mb-2"></i>
                                مشاركة الرابط
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('messages.view_message', id=file_info.message_id) }}" 
                               class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-arrow-left fa-2x d-block mb-2"></i>
                                العودة للرسالة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-share-alt me-2"></i>مشاركة الملف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">رابط المعاينة:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="previewLink" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyLink()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">رابط التحميل:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="downloadLink" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyDownloadLink()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    هذه الروابط تتطلب تسجيل الدخول للوصول إليها.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.preview-content {
    padding: 20px;
    background: #fff;
}

.document-viewer {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.document-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 15px;
}

.paragraph {
    line-height: 1.6;
    margin-bottom: 10px;
}

.page-content {
    background: #f8f9fa;
}

.image-container img {
    transition: transform 0.3s ease;
}

.image-container img:hover {
    transform: scale(1.05);
}

.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
    overflow-y: auto;
}

.fullscreen .preview-content {
    max-height: none;
    height: 100vh;
}

@media print {
    .card-header, .btn-group, .modal, nav {
        display: none !important;
    }
    
    .preview-content {
        max-height: none !important;
        overflow: visible !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let isFullscreen = false;

function toggleFullscreen() {
    const container = document.getElementById('preview-container');
    const icon = document.getElementById('fullscreen-icon');
    const text = document.getElementById('fullscreen-text');
    
    if (!isFullscreen) {
        container.classList.add('fullscreen');
        icon.className = 'fas fa-compress';
        text.textContent = 'إغلاق ملء الشاشة';
        isFullscreen = true;
    } else {
        container.classList.remove('fullscreen');
        icon.className = 'fas fa-expand';
        text.textContent = 'ملء الشاشة';
        isFullscreen = false;
    }
}

function printPreview() {
    window.print();
}

function shareFile() {
    const previewUrl = window.location.href;
    const downloadUrl = "{{ url_for('messages.download_attachment', filename=filename, _external=True) }}";
    
    document.getElementById('previewLink').value = previewUrl;
    document.getElementById('downloadLink').value = downloadUrl;
    
    const modal = new bootstrap.Modal(document.getElementById('shareModal'));
    modal.show();
}

function copyLink() {
    const linkInput = document.getElementById('previewLink');
    linkInput.select();
    document.execCommand('copy');
    
    // Show success message
    showToast('تم نسخ رابط المعاينة', 'success');
}

function copyDownloadLink() {
    const linkInput = document.getElementById('downloadLink');
    linkInput.select();
    document.execCommand('copy');
    
    // Show success message
    showToast('تم نسخ رابط التحميل', 'success');
}

function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // F11 for fullscreen
    if (e.key === 'F11') {
        e.preventDefault();
        toggleFullscreen();
    }
    
    // Ctrl+P for print
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        printPreview();
    }
    
    // Escape to exit fullscreen
    if (e.key === 'Escape' && isFullscreen) {
        toggleFullscreen();
    }
});
</script>
{% endblock %}
