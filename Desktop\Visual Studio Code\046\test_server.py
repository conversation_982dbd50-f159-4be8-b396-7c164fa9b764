#!/usr/bin/env python3
"""
Simple test server to verify everything works
خادم اختبار بسيط للتأكد من عمل كل شيء
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>اختبار الخادم</title>
        <style>
            body { 
                font-family: Arial; 
                text-align: center; 
                padding: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
                margin: 0;
            }
            .container {
                background: white;
                color: black;
                padding: 40px;
                border-radius: 15px;
                max-width: 600px;
                margin: 0 auto;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            }
            .success { color: #28a745; font-size: 24px; margin: 20px 0; }
            .info { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
            a { color: #007bff; text-decoration: none; font-weight: bold; }
            a:hover { text-decoration: underline; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 الخادم يعمل بنجاح!</h1>
            <div class="success">✅ Flask Server is Running</div>
            
            <div class="info">
                <h3>🔗 الروابط المتاحة:</h3>
                <p><a href="/login">🔐 صفحة الدخول</a></p>
                <p><a href="/test">🧪 اختبار API</a></p>
                <p><a href="/info">ℹ️ معلومات النظام</a></p>
            </div>
            
            <div class="info">
                <h3>📋 بيانات الدخول:</h3>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
            
            <p>🌐 الخادم يعمل على: <strong>http://localhost:8585</strong></p>
        </div>
    </body>
    </html>
    '''

@app.route('/login')
def login():
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تسجيل الدخول</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
            }
            .login-card {
                background: white;
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                overflow: hidden;
                max-width: 400px;
                width: 100%;
            }
            .login-header {
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="login-card">
                        <div class="login-header">
                            <h3>🔐 تسجيل الدخول</h3>
                            <p class="mb-0">نظام المراسلات الإلكترونية</p>
                        </div>
                        <div class="p-4">
                            <form>
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" value="admin" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" value="admin123" readonly>
                                </div>
                                <button type="button" class="btn btn-primary w-100" onclick="alert('هذه صفحة تجريبية!')">
                                    دخول
                                </button>
                            </form>
                            <div class="text-center mt-3">
                                <a href="/">← العودة للصفحة الرئيسية</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/test')
def test():
    return {
        'status': 'success',
        'message': 'الخادم يعمل بنجاح',
        'server': 'Flask Test Server',
        'port': 8585,
        'routes': [
            '/ - الصفحة الرئيسية',
            '/login - صفحة الدخول',
            '/test - اختبار API',
            '/info - معلومات النظام'
        ]
    }

@app.route('/info')
def info():
    import sys
    import flask
    return f'''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>معلومات النظام</title>
        <style>
            body {{ font-family: Arial; padding: 20px; background: #f8f9fa; }}
            .info-card {{ background: white; padding: 30px; border-radius: 10px; margin: 20px 0; }}
            .success {{ color: #28a745; }}
        </style>
    </head>
    <body>
        <div class="info-card">
            <h2>ℹ️ معلومات النظام</h2>
            <p><strong>Python Version:</strong> <span class="success">{sys.version}</span></p>
            <p><strong>Flask Version:</strong> <span class="success">{flask.__version__}</span></p>
            <p><strong>Server Status:</strong> <span class="success">Running ✅</span></p>
            <p><strong>Port:</strong> <span class="success">8585</span></p>
            <p><strong>Host:</strong> <span class="success">localhost</span></p>
        </div>
        <div class="info-card">
            <h3>🔗 الروابط:</h3>
            <p><a href="/">الصفحة الرئيسية</a></p>
            <p><a href="/login">صفحة الدخول</a></p>
            <p><a href="/test">اختبار API</a></p>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 تشغيل خادم الاختبار...")
    print("🌐 الرابط: http://localhost:8585")
    print("🔐 صفحة الدخول: http://localhost:8585/login")
    print("🧪 اختبار: http://localhost:8585/test")
    print("ℹ️ معلومات: http://localhost:8585/info")
    print("=" * 50)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)

    app.run(host='127.0.0.1', port=8585, debug=True)
