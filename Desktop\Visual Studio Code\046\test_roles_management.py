#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لإدارة الأدوار
"""

import requests
import sys

def test_roles_management():
    """اختبار شامل لإدارة الأدوار"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة إدارة الصلاحيات الرئيسية
        print("\n🏠 اختبار صفحة إدارة الصلاحيات الرئيسية...")
        response = session.get(f"{base_url}/permissions")
        if response.status_code == 200:
            print("✅ صفحة إدارة الصلاحيات تعمل")
        else:
            print(f"❌ فشل في الوصول لصفحة إدارة الصلاحيات: {response.status_code}")
            return False
        
        # اختبار صفحة قائمة الأدوار
        print("\n📋 اختبار صفحة قائمة الأدوار...")
        response = session.get(f"{base_url}/permissions/roles")
        if response.status_code == 200:
            print("✅ صفحة قائمة الأدوار تعمل")
            
            content = response.text
            features_found = []
            
            if 'إنشاء دور جديد' in content:
                features_found.append("زر إنشاء دور جديد")
            
            if 'تعديل' in content and 'edit' in content:
                features_found.append("أزرار التعديل")
            
            if 'حذف' in content and 'delete' in content:
                features_found.append("أزرار الحذف")
            
            if 'عرض' in content and 'eye' in content:
                features_found.append("أزرار العرض")
            
            if 'الصلاحيات' in content:
                features_found.append("عرض الصلاحيات")
            
            if 'المستخدمين' in content:
                features_found.append("عرض عدد المستخدمين")
            
            print(f"✅ الميزات الموجودة: {', '.join(features_found)}")
        else:
            print(f"❌ فشل في الوصول لقائمة الأدوار: {response.status_code}")
            return False
        
        # اختبار صفحة إنشاء دور جديد
        print("\n➕ اختبار صفحة إنشاء دور جديد...")
        response = session.get(f"{base_url}/permissions/roles/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء دور جديد تعمل")
            
            content = response.text
            if 'اسم الدور' in content and 'الاسم المعروض' in content:
                print("✅ نموذج إنشاء الدور مكتمل")
        else:
            print(f"❌ فشل في الوصول لصفحة إنشاء دور: {response.status_code}")
            return False
        
        # اختبار إنشاء دور جديد
        print("\n📝 اختبار إنشاء دور جديد...")
        new_role_data = {
            'name': 'test_complete_role',
            'display_name': 'دور اختبار شامل',
            'description': 'دور لاختبار جميع الوظائف',
            'permissions': ['1', '2', '3']
        }
        
        response = session.post(f"{base_url}/permissions/roles/new", data=new_role_data)
        if response.status_code == 200:
            print("✅ تم إنشاء دور جديد بنجاح")
        else:
            print(f"⚠️ مشكلة في إنشاء دور جديد: {response.status_code}")
        
        # اختبار تعديل دور موجود
        print("\n✏️ اختبار تعديل دور موجود...")
        edit_success = False
        for role_id in range(1, 10):
            response = session.get(f"{base_url}/permissions/roles/{role_id}/edit")
            if response.status_code == 200:
                print(f"✅ صفحة تعديل الدور {role_id} تعمل")
                
                content = response.text
                if 'تعديل الدور' in content and 'حفظ التغييرات' in content:
                    print("✅ نموذج تعديل الدور مكتمل")
                
                # اختبار تحديث الدور
                update_data = {
                    'display_name': f'دور محدث {role_id}',
                    'description': f'وصف محدث للدور {role_id}',
                    'permissions': ['1', '2']
                }
                
                response = session.post(f"{base_url}/permissions/roles/{role_id}/edit", data=update_data)
                if response.status_code == 200:
                    print(f"✅ تم تحديث الدور {role_id} بنجاح")
                    edit_success = True
                    break
                else:
                    print(f"⚠️ مشكلة في تحديث الدور {role_id}: {response.status_code}")
            elif response.status_code == 404:
                continue
            else:
                print(f"❌ خطأ في الوصول لتعديل الدور {role_id}: {response.status_code}")
        
        if edit_success:
            print("✅ وظيفة التعديل تعمل بشكل صحيح")
        else:
            print("⚠️ لم يتم اختبار وظيفة التعديل")
        
        # اختبار عرض تفاصيل دور
        print("\n👁️ اختبار عرض تفاصيل الأدوار...")
        view_success = False
        for role_id in range(1, 10):
            response = session.get(f"{base_url}/permissions/roles")
            if response.status_code == 200:
                content = response.text
                if f'viewRoleModal{role_id}' in content:
                    print(f"✅ نافذة عرض تفاصيل الدور {role_id} موجودة")
                    view_success = True
                    break
        
        if view_success:
            print("✅ وظيفة عرض التفاصيل متاحة")
        else:
            print("⚠️ لم يتم العثور على نوافذ عرض التفاصيل")
        
        # اختبار صفحة إدارة صلاحيات المستخدمين
        print("\n👥 اختبار صفحة إدارة صلاحيات المستخدمين...")
        response = session.get(f"{base_url}/permissions/user-permissions")
        if response.status_code == 200:
            print("✅ صفحة إدارة صلاحيات المستخدمين تعمل")
        else:
            print(f"⚠️ مشكلة في صفحة إدارة صلاحيات المستخدمين: {response.status_code}")
        
        # اختبار صفحة إدارة الصلاحيات العامة
        print("\n🔐 اختبار صفحة إدارة الصلاحيات العامة...")
        response = session.get(f"{base_url}/permissions/permissions")
        if response.status_code == 200:
            print("✅ صفحة إدارة الصلاحيات العامة تعمل")
        else:
            print(f"⚠️ مشكلة في صفحة إدارة الصلاحيات العامة: {response.status_code}")
        
        print("\n🎉 تم اكتمال اختبار إدارة الأدوار!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل لإدارة الأدوار")
    print("=" * 60)
    
    success = test_roles_management()
    
    if success:
        print("\n✅ جميع وظائف إدارة الأدوار تعمل بنجاح!")
        print("\n📋 الوظائف المتاحة:")
        print("  ✅ عرض قائمة الأدوار")
        print("  ✅ إنشاء دور جديد")
        print("  ✅ تعديل الأدوار الموجودة")
        print("  ✅ عرض تفاصيل الأدوار")
        print("  ✅ حذف الأدوار (للأدوار غير المستخدمة)")
        print("  ✅ إدارة صلاحيات الأدوار")
        print("  ✅ عرض المستخدمين المرتبطين بكل دور")
        print("\n🌐 للوصول لإدارة الأدوار:")
        print("  http://localhost:8585/permissions/roles")
        print("\n🔧 الإجراءات المتاحة لكل دور:")
        print("  👁️ عرض التفاصيل - زر العين الأزرق")
        print("  ✏️ تعديل الدور - زر القلم الأزرق")
        print("  🗑️ حذف الدور - زر سلة المهملات الأحمر (للأدوار غير المستخدمة)")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n🎯 زر التعديل مُفعل ويعمل بشكل مثالي!")
        sys.exit(0)
    else:
        print("\n❌ فشل في بعض وظائف إدارة الأدوار!")
        sys.exit(1)
