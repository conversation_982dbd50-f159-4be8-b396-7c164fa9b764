#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي شامل لجميع الإصلاحات والتحديثات
"""

import requests
import sys

def test_final_complete():
    """اختبار نهائي شامل"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # قائمة جميع الصفحات للاختبار
        test_pages = [
            ('/', 'لوحة التحكم الرئيسية'),
            ('/messages', 'قائمة الرسائل'),
            ('/messages/new', 'إنشاء رسالة جديدة'),
            ('/messages/internal', 'المراسلات الداخلية'),
            ('/users', 'قائمة المستخدمين'),
            ('/users/new', 'إنشاء مستخدم جديد'),
            ('/departments', 'قائمة الجهات'),
            ('/departments/new', 'إنشاء جهة جديدة'),
            ('/permissions', 'إدارة الصلاحيات'),
            ('/archive', 'نظام الأرشيف'),
            ('/users/signature', 'التوقيع الإلكتروني'),
            ('/search', 'البحث')
        ]
        
        print(f"\n🧪 اختبار {len(test_pages)} صفحة...")
        
        failed_pages = []
        
        for url, name in test_pages:
            try:
                response = session.get(f"{base_url}{url}")
                if response.status_code == 200:
                    print(f"✅ {name}")
                else:
                    print(f"❌ {name} - كود الخطأ: {response.status_code}")
                    failed_pages.append((name, response.status_code))
            except Exception as e:
                print(f"❌ {name} - خطأ: {str(e)}")
                failed_pages.append((name, str(e)))
        
        # اختبار APIs
        print(f"\n🔌 اختبار APIs...")
        
        api_tests = [
            ('/departments/api/departments', 'API الجهات'),
            ('/departments/api/users', 'API المستخدمين')
        ]
        
        for url, name in api_tests:
            try:
                response = session.get(f"{base_url}{url}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ {name} - {len(data)} عنصر")
                else:
                    print(f"❌ {name} - كود الخطأ: {response.status_code}")
                    failed_pages.append((name, response.status_code))
            except Exception as e:
                print(f"❌ {name} - خطأ: {str(e)}")
                failed_pages.append((name, str(e)))
        
        # اختبار الفلاتر المختلفة
        print(f"\n🔍 اختبار الفلاتر...")
        
        filter_tests = [
            ('/messages/internal?status=pending', 'فلتر المراسلات المعلقة'),
            ('/messages/internal?status=urgent', 'فلتر المراسلات العاجلة'),
            ('/messages?type=incoming', 'فلتر الرسائل الواردة'),
            ('/messages?type=outgoing', 'فلتر الرسائل الصادرة'),
            ('/departments?status=active', 'فلتر الجهات النشطة')
        ]
        
        for url, name in filter_tests:
            try:
                response = session.get(f"{base_url}{url}")
                if response.status_code == 200:
                    print(f"✅ {name}")
                else:
                    print(f"❌ {name} - كود الخطأ: {response.status_code}")
                    failed_pages.append((name, response.status_code))
            except Exception as e:
                print(f"❌ {name} - خطأ: {str(e)}")
                failed_pages.append((name, str(e)))
        
        # اختبار إنشاء البيانات
        print(f"\n📝 اختبار إنشاء البيانات...")
        
        # إنشاء رسالة عادية
        message_data = {
            'message_type': 'incoming',
            'registration_number': 'FINAL-TEST-001',
            'destination': 'جهة الاختبار النهائي',
            'subject': 'رسالة اختبار نهائي',
            'content': 'محتوى اختبار نهائي',
            'message_date': '2025-01-15',
            'priority': 'normal'
        }
        
        response = session.post(f"{base_url}/messages/new", data=message_data)
        if response.status_code == 200:
            print("✅ إنشاء رسالة عادية")
        else:
            print(f"❌ إنشاء رسالة عادية - كود الخطأ: {response.status_code}")
            failed_pages.append(('إنشاء رسالة عادية', response.status_code))
        
        # إنشاء رسالة عاجلة
        urgent_message_data = {
            'message_type': 'outgoing',
            'registration_number': 'URGENT-FINAL-001',
            'destination': 'جهة عاجلة',
            'subject': 'رسالة عاجلة نهائية',
            'content': 'محتوى عاجل',
            'message_date': '2025-01-15',
            'priority': 'urgent'
        }
        
        response = session.post(f"{base_url}/messages/new", data=urgent_message_data)
        if response.status_code == 200:
            print("✅ إنشاء رسالة عاجلة")
        else:
            print(f"❌ إنشاء رسالة عاجلة - كود الخطأ: {response.status_code}")
            failed_pages.append(('إنشاء رسالة عاجلة', response.status_code))
        
        # إنشاء جهة جديدة
        department_data = {
            'name': 'قسم الاختبار النهائي',
            'description': 'قسم للاختبار النهائي',
            'is_active': 'on'
        }
        
        response = session.post(f"{base_url}/departments/new", data=department_data)
        if response.status_code == 200:
            print("✅ إنشاء جهة جديدة")
        else:
            print(f"❌ إنشاء جهة جديدة - كود الخطأ: {response.status_code}")
            failed_pages.append(('إنشاء جهة جديدة', response.status_code))
        
        # تقرير النتائج
        print(f"\n📊 تقرير النتائج:")
        total_tests = len(test_pages) + len(api_tests) + len(filter_tests) + 3  # 3 for data creation tests
        passed_tests = total_tests - len(failed_pages)
        
        print(f"✅ نجح: {passed_tests}/{total_tests}")
        print(f"❌ فشل: {len(failed_pages)}/{total_tests}")
        
        if failed_pages:
            print(f"\n❌ الصفحات التي فشلت:")
            for name, error in failed_pages:
                print(f"  • {name}: {error}")
            return False
        else:
            print(f"\n🎉 جميع الاختبارات نجحت!")
            return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 الاختبار النهائي الشامل")
    print("=" * 60)
    
    success = test_final_complete()
    
    if success:
        print("\n🎉 النظام مكتمل وجاهز للاستخدام!")
        print("\n✅ جميع الإصلاحات والتحديثات مطبقة:")
        print("  🔧 إصلاح خطأ NameError: name 'Role' is not defined")
        print("  🔧 إصلاح خطأ TypeError: AppenderQuery has no len()")
        print("  🆕 إضافة عمود الحالة والأيام في لوحة التحكم")
        print("  🆕 إضافة عمود الحالة والأيام في قائمة الرسائل")
        print("  🆕 تحسين عرض الأولوية والاعتماد")
        print("  🆕 تحسين التصميم المتجاوب")
        print("  🏢 قسم إدارة الجهات مكتمل")
        print("\n🌐 النظام متاح على:")
        print("  http://localhost:8585")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n🎯 جميع الأقسام تعمل بشكل مثالي!")
        sys.exit(0)
    else:
        print("\n❌ يوجد بعض المشاكل في النظام!")
        print("يرجى مراجعة الأخطاء أعلاه وإصلاحها.")
        sys.exit(1)
