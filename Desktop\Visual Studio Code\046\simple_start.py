#!/usr/bin/env python3
"""
Simple startup script - Electronic Correspondence System
"""

print("🚀 بدء تشغيل نظام المراسلات الإلكترونية...")
print("=" * 50)

try:
    # Import Flask app
    from app import create_app
    
    print("✓ تم تحميل التطبيق بنجاح")
    
    # Create app instance
    app = create_app()
    
    print("✓ تم إنشاء التطبيق")
    print("📍 الخادم متاح على: http://localhost:8585")
    print("🔑 بيانات الدخول: admin / admin123")
    print("=" * 50)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)

    # Start server
    app.run(
        host='0.0.0.0',
        port=8585,
        debug=False,
        use_reloader=False
    )
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت المتطلبات: pip install flask flask-sqlalchemy flask-login")
    
except Exception as e:
    print(f"❌ خطأ في التشغيل: {e}")
    print("💡 جرب تشغيل: python init_database.py أولاً")

input("\nاضغط Enter للخروج...")
