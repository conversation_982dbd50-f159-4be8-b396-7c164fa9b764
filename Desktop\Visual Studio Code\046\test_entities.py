#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قسم الجهات والمؤسسات
"""

import requests
import sys

def test_entities_system():
    """اختبار نظام إدارة الجهات"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار قائمة الجهات
        print("\n📋 اختبار قائمة الجهات...")
        response = session.get(f"{base_url}/entities")
        if response.status_code == 200:
            print("✅ صفحة قائمة الجهات تعمل")
            
            content = response.text
            if 'إدارة الجهات والمؤسسات' in content:
                print("✅ عنوان الصفحة صحيح")
            
            if 'إجمالي الجهات' in content:
                print("✅ الإحصائيات تظهر بشكل صحيح")
                
        else:
            print(f"❌ فشل في الوصول لقائمة الجهات: {response.status_code}")
            return False
        
        # اختبار صفحة إنشاء جهة جديدة
        print("\n➕ اختبار صفحة إنشاء جهة جديدة...")
        response = session.get(f"{base_url}/entities/new")
        if response.status_code == 200:
            print("✅ صفحة إنشاء جهة جديدة تعمل")
            
            content = response.text
            if 'إضافة جهة جديدة' in content:
                print("✅ محتوى الصفحة صحيح")
                
        else:
            print(f"❌ فشل في الوصول لصفحة إنشاء جهة: {response.status_code}")
            return False
        
        # اختبار إنشاء جهة جديدة
        print("\n🏢 اختبار إنشاء جهة جديدة...")
        new_entity_data = {
            'name': 'جهة اختبار',
            'name_en': 'Test Entity',
            'entity_type': 'government',
            'category': 'ministry',
            'address': 'عنوان اختبار',
            'phone': '123456789',
            'email': '<EMAIL>',
            'contact_person': 'شخص اختبار',
            'contact_title': 'مدير اختبار',
            'is_government': 'on',
            'is_frequent': 'on'
        }
        
        response = session.post(f"{base_url}/entities/new", data=new_entity_data)
        if response.status_code == 200 or response.status_code == 302:
            print("✅ تم إنشاء جهة جديدة بنجاح")
        else:
            print(f"⚠️ مشكلة في إنشاء جهة جديدة: {response.status_code}")
        
        # اختبار البحث في الجهات
        print("\n🔍 اختبار البحث في الجهات...")
        search_params = {
            'search': 'وزارة',
            'type': 'government',
            'status': 'active'
        }
        
        response = session.get(f"{base_url}/entities", params=search_params)
        if response.status_code == 200:
            print("✅ البحث في الجهات يعمل")
            
            content = response.text
            if 'وزارة' in content:
                print("✅ نتائج البحث تظهر بشكل صحيح")
                
        else:
            print(f"⚠️ مشكلة في البحث: {response.status_code}")
        
        # اختبار API البحث
        print("\n🔌 اختبار API البحث...")
        response = session.get(f"{base_url}/entities/api/search", params={'q': 'وزارة'})
        if response.status_code == 200:
            print("✅ API البحث يعمل")
            
            try:
                data = response.json()
                if isinstance(data, list):
                    print(f"✅ تم العثور على {len(data)} نتيجة")
                else:
                    print("⚠️ تنسيق البيانات غير متوقع")
            except:
                print("⚠️ مشكلة في تحليل JSON")
                
        else:
            print(f"⚠️ مشكلة في API البحث: {response.status_code}")
        
        # اختبار إحصائيات الجهات
        print("\n📊 اختبار إحصائيات الجهات...")
        response = session.get(f"{base_url}/entities/stats")
        if response.status_code == 200:
            print("✅ API الإحصائيات يعمل")
            
            try:
                stats = response.json()
                if 'total' in stats:
                    print(f"✅ إجمالي الجهات: {stats['total']}")
                if 'government' in stats:
                    print(f"✅ الجهات الحكومية: {stats['government']}")
                if 'frequent' in stats:
                    print(f"✅ متكررة التراسل: {stats['frequent']}")
            except:
                print("⚠️ مشكلة في تحليل إحصائيات JSON")
                
        else:
            print(f"⚠️ مشكلة في API الإحصائيات: {response.status_code}")
        
        # اختبار عرض تفاصيل جهة
        print("\n👁️ اختبار عرض تفاصيل جهة...")
        response = session.get(f"{base_url}/entities/1")
        if response.status_code == 200:
            print("✅ صفحة عرض تفاصيل الجهة تعمل")
            
            content = response.text
            if 'معلومات الاتصال' in content:
                print("✅ تفاصيل الجهة تظهر بشكل صحيح")
                
        else:
            print(f"⚠️ مشكلة في عرض تفاصيل الجهة: {response.status_code}")
        
        # اختبار صفحة تعديل جهة
        print("\n✏️ اختبار صفحة تعديل جهة...")
        response = session.get(f"{base_url}/entities/1/edit")
        if response.status_code == 200:
            print("✅ صفحة تعديل الجهة تعمل")
            
            content = response.text
            if 'تعديل' in content and 'المعلومات الأساسية' in content:
                print("✅ نموذج التعديل يظهر بشكل صحيح")
                
        else:
            print(f"⚠️ مشكلة في صفحة تعديل الجهة: {response.status_code}")
        
        # اختبار الفلاتر المختلفة
        print("\n🔧 اختبار الفلاتر...")
        filters = [
            {'type': 'government'},
            {'type': 'private'},
            {'category': 'ministry'},
            {'category': 'company'},
            {'status': 'active'},
            {'status': 'government'},
            {'status': 'frequent'}
        ]
        
        for filter_params in filters:
            response = session.get(f"{base_url}/entities", params=filter_params)
            if response.status_code == 200:
                filter_name = list(filter_params.keys())[0]
                filter_value = list(filter_params.values())[0]
                print(f"✅ فلتر {filter_name}={filter_value} يعمل")
            else:
                print(f"⚠️ مشكلة في فلتر {filter_params}")
        
        print("\n🎉 تم اكتمال اختبار قسم الجهات!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار قسم الجهات والمؤسسات")
    print("=" * 55)
    
    success = test_entities_system()
    
    if success:
        print("\n✅ قسم الجهات يعمل بشكل مثالي!")
        print("\n📋 الميزات المختبرة:")
        print("  ✅ قائمة الجهات مع الإحصائيات")
        print("  ✅ إنشاء جهة جديدة")
        print("  ✅ عرض تفاصيل الجهة")
        print("  ✅ تعديل الجهة")
        print("  ✅ البحث والفلترة")
        print("  ✅ API البحث")
        print("  ✅ API الإحصائيات")
        print("  ✅ جميع أنواع الفلاتر")
        print("\n🌐 الروابط المتاحة:")
        print("  📋 قائمة الجهات: http://localhost:8585/entities")
        print("  ➕ إضافة جهة: http://localhost:8585/entities/new")
        print("  🔍 API البحث: http://localhost:8585/entities/api/search?q=كلمة")
        print("  📊 الإحصائيات: http://localhost:8585/entities/stats")
        print("\n🏢 أنواع الجهات المدعومة:")
        print("  🏛️ جهة حكومية")
        print("  🏢 جهة خاصة")
        print("  👤 فرد")
        print("  🤝 منظمة غير ربحية")
        print("  🌍 جهة دولية")
        print("  🎓 جهة أكاديمية")
        print("\n📂 فئات الجهات:")
        print("  🏛️ وزارة")
        print("  🏢 هيئة")
        print("  🏭 شركة")
        print("  🎓 جامعة")
        print("  🏥 مستشفى")
        print("  🏦 بنك")
        print("  🏛️ سفارة")
        print("  ⚖️ محكمة")
        print("  🏘️ بلدية")
        sys.exit(0)
    else:
        print("\n❌ فشل في اختبار قسم الجهات!")
        print("\n🔧 تحقق من:")
        print("  • تشغيل الخادم")
        print("  • إنشاء جدول الجهات")
        print("  • صحة قاعدة البيانات")
        print("  • تسجيل مسارات الجهات")
        sys.exit(1)
