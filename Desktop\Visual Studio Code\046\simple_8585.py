#!/usr/bin/env python3
"""
Simple server on port 8585 - No database issues
خادم بسيط على المنفذ 8585 - بدون مشاكل قاعدة البيانات
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>نظام المراسلات الإلكترونية - المنفذ 8585</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-align: center;
                padding: 50px;
                margin: 0;
                min-height: 100vh;
            }
            .container {
                background: rgba(255,255,255,0.95);
                color: #333;
                padding: 50px;
                border-radius: 20px;
                max-width: 800px;
                margin: 0 auto;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            }
            .success {
                background: #28a745;
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                font-size: 18px;
                font-weight: bold;
            }
            .info {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                border-left: 5px solid #007bff;
            }
            .btn {
                background: #007bff;
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 25px;
                text-decoration: none;
                font-weight: bold;
                margin: 10px;
                display: inline-block;
                transition: background 0.3s;
            }
            .btn:hover {
                background: #0056b3;
                color: white;
                text-decoration: none;
            }
            .port-info {
                background: #17a2b8;
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                font-size: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 نظام المراسلات الإلكترونية</h1>
            
            <div class="success">
                ✅ الخادم يعمل بنجاح!
            </div>
            
            <div class="port-info">
                🌐 المنفذ: <strong>8585</strong>
                <br>
                📍 الرابط: <strong>http://localhost:8585</strong>
            </div>
            
            <div class="info">
                <h3>🔗 الصفحات المتاحة:</h3>
                <p><a href="/login" class="btn">🔐 صفحة الدخول</a></p>
                <p><a href="/test" class="btn">🧪 اختبار النظام</a></p>
                <p><a href="/info" class="btn">ℹ️ معلومات الخادم</a></p>
            </div>
            
            <div class="info">
                <h3>📋 بيانات الدخول التجريبية:</h3>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
            
            <div class="info">
                <h3>🚀 للنظام الكامل:</h3>
                <p>لتشغيل النظام الكامل مع جميع الميزات:</p>
                <code style="background: #333; color: white; padding: 10px; border-radius: 5px;">python app.py</code>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/login')
def login():
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تسجيل الدخول - المنفذ 8585</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
            }
            .login-box {
                background: white;
                padding: 40px;
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                max-width: 400px;
                width: 100%;
                text-align: center;
            }
            .login-header {
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                padding: 20px;
                margin: -40px -40px 30px -40px;
                border-radius: 15px 15px 0 0;
            }
            .form-group {
                margin: 20px 0;
                text-align: right;
            }
            .form-control {
                width: 100%;
                padding: 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 16px;
                box-sizing: border-box;
            }
            .btn-login {
                background: #007bff;
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                width: 100%;
                cursor: pointer;
            }
            .btn-login:hover {
                background: #0056b3;
            }
            .demo-info {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-top: 20px;
                border-left: 4px solid #28a745;
            }
            .back-link {
                margin-top: 20px;
            }
            .back-link a {
                color: #007bff;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="login-box">
            <div class="login-header">
                <h2>🔐 تسجيل الدخول</h2>
                <p>المنفذ: 8585</p>
            </div>
            
            <form>
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" class="form-control" placeholder="أدخل اسم المستخدم">
                </div>
                
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" class="form-control" placeholder="أدخل كلمة المرور">
                </div>
                
                <button type="button" class="btn-login" onclick="showDemo()">
                    دخول
                </button>
            </form>
            
            <div class="demo-info">
                <h4>بيانات تجريبية:</h4>
                <p><strong>المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
            
            <div class="back-link">
                <a href="/">← العودة للصفحة الرئيسية</a>
            </div>
        </div>
        
        <script>
            function showDemo() {
                alert('تم تسجيل الدخول بنجاح!\\n\\nهذه صفحة تجريبية على المنفذ 8585\\n\\nللنظام الكامل: python app.py');
            }
        </script>
    </body>
    </html>
    '''

@app.route('/test')
def test():
    return {
        'status': 'success',
        'message': 'الخادم يعمل بنجاح على المنفذ 8585',
        'port': 8585,
        'host': 'localhost',
        'url': 'http://localhost:8585',
        'timestamp': '2024-12-19',
        'routes': {
            'home': 'http://localhost:8585/',
            'login': 'http://localhost:8585/login',
            'test': 'http://localhost:8585/test',
            'info': 'http://localhost:8585/info'
        }
    }

@app.route('/info')
def info():
    import sys
    import platform
    return f'''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>معلومات الخادم</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                background: #f8f9fa;
                padding: 20px;
                margin: 0;
            }}
            .info-container {{
                background: white;
                padding: 30px;
                border-radius: 10px;
                max-width: 800px;
                margin: 0 auto;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            }}
            .status-good {{
                color: #28a745;
                font-weight: bold;
            }}
            .info-row {{
                padding: 10px 0;
                border-bottom: 1px solid #eee;
            }}
        </style>
    </head>
    <body>
        <div class="info-container">
            <h2>ℹ️ معلومات الخادم</h2>
            
            <div class="info-row">
                <strong>حالة الخادم:</strong> <span class="status-good">يعمل ✅</span>
            </div>
            
            <div class="info-row">
                <strong>المنفذ:</strong> <span class="status-good">8585</span>
            </div>
            
            <div class="info-row">
                <strong>الرابط:</strong> <span class="status-good">http://localhost:8585</span>
            </div>
            
            <div class="info-row">
                <strong>Python Version:</strong> <span class="status-good">{sys.version.split()[0]}</span>
            </div>
            
            <div class="info-row">
                <strong>Platform:</strong> <span class="status-good">{platform.system()}</span>
            </div>
            
            <div class="info-row">
                <strong>Flask Status:</strong> <span class="status-good">Running</span>
            </div>
            
            <h3>🔗 الروابط المتاحة:</h3>
            <ul>
                <li><a href="/">الصفحة الرئيسية</a></li>
                <li><a href="/login">صفحة الدخول</a></li>
                <li><a href="/test">اختبار API</a></li>
            </ul>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 تشغيل الخادم البسيط على المنفذ 8585")
    print("=" * 50)
    print("🌐 الرابط: http://localhost:8585")
    print("🔐 صفحة الدخول: http://localhost:8585/login")
    print("🧪 اختبار: http://localhost:8585/test")
    print("ℹ️ معلومات: http://localhost:8585/info")
    print("=" * 50)
    print("✅ بدون قاعدة بيانات - بدون مشاكل!")
    print("🛑 لإيقاف الخادم: Ctrl+C")
    print("=" * 50)
    
    try:
        app.run(host='127.0.0.1', port=8585, debug=True)
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        print("💡 جرب منفذ آخر أو تحقق من أن المنفذ 8585 غير مستخدم")
