/* Custom styles for Arabic RTL layout */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #000000 !important;
}

/* Force black text color for all elements */
* {
    color: #000000 !important;
}

/* Override Bootstrap text colors */
.text-muted {
    color: #000000 !important;
}

.text-secondary {
    color: #000000 !important;
}

/* Table text */
table, th, td {
    color: #000000 !important;
}

/* Form elements */
input, textarea, select, label {
    color: #000000 !important;
}

/* Links - keep them visible but black */
a {
    color: #000000 !important;
    text-decoration: underline;
}

a:hover {
    color: #333333 !important;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card.bg-primary .card-footer,
.card.bg-success .card-footer,
.card.bg-info .card-footer,
.card.bg-warning .card-footer {
    background-color: rgba(255, 255, 255, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}

/* Forms */
.form-control,
.form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
}

.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Badges */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* File upload */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

/* Message priority indicators */
.priority-urgent {
    border-left: 4px solid #dc3545;
}

.priority-high {
    border-left: 4px solid #ffc107;
}

.priority-normal {
    border-left: 4px solid #6c757d;
}

/* Search results highlighting */
.search-highlight {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

/* Loading spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    .pagination,
    .card-footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    body {
        background-color: white !important;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .btn-group-sm > .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Arabic text improvements */
.arabic-text {
    line-height: 1.8;
    text-align: right;
}

/* Status indicators */
.status-active {
    color: #198754;
}

.status-inactive {
    color: #6c757d;
}

.status-archived {
    color: #fd7e14;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
