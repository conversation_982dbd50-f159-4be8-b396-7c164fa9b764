#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الميزات الجديدة
"""

import os
import sys
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, UserRole, Permission, Role, Department

def test_all_features():
    """اختبار شامل لجميع الميزات"""
    
    app = create_app()
    with app.app_context():
        print("🧪 اختبار شامل لجميع الميزات الجديدة...")
        print("=" * 60)
        
        # 1. اختبار الأدوار الجديدة
        print("1️⃣ اختبار الأدوار الجديدة:")
        print("-" * 30)
        
        users = User.query.all()
        for user in users:
            role_name = {
                UserRole.ADMIN: 'مدير النظام',
                UserRole.MANAGER: 'مدير',
                UserRole.SECRETARY: 'سكرتير',
                UserRole.EMPLOYEE: 'موظف'
            }.get(user.role, 'غير محدد')
            
            print(f"   👤 {user.name} ({role_name})")
            print(f"      🔐 اسم المستخدم: {user.username}")
            print(f"      🏢 القسم: {user.department.name if user.department else 'غير محدد'}")
            print(f"      ✅ يمكنه الاعتماد: {'نعم' if user.can_approve_others else 'لا'}")
            if user.approval_manager:
                print(f"      👨‍💼 مسؤول الاعتماد: {user.approval_manager.name}")
            print()
        
        # 2. اختبار مسؤولي الاعتماد
        print("2️⃣ اختبار مسؤولي الاعتماد:")
        print("-" * 30)
        
        managers = User.query.filter_by(can_approve_others=True).all()
        print(f"   📊 عدد المسؤولين عن الاعتماد: {len(managers)}")
        for manager in managers:
            managed_users = User.query.filter_by(approval_manager_id=manager.id).all()
            print(f"   👨‍💼 {manager.name} يدير {len(managed_users)} مستخدم:")
            for user in managed_users:
                print(f"      - {user.name} ({user.role.value})")
        print()
        
        # 3. اختبار رفع الملفات
        print("3️⃣ اختبار رفع الملفات:")
        print("-" * 30)
        
        # التحقق من الامتدادات المدعومة
        allowed_extensions = app.config.get('ALLOWED_EXTENSIONS', set())
        docx_supported = 'docx' in allowed_extensions
        pdf_supported = 'pdf' in allowed_extensions
        
        print(f"   📄 ملفات DOCX مدعومة: {'✅ نعم' if docx_supported else '❌ لا'}")
        print(f"   📄 ملفات PDF مدعومة: {'✅ نعم' if pdf_supported else '❌ لا'}")
        print(f"   📊 إجمالي الامتدادات المدعومة: {len(allowed_extensions)}")
        
        # التحقق من مجلد الرفع
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        upload_exists = os.path.exists(upload_folder)
        print(f"   📁 مجلد الرفع موجود: {'✅ نعم' if upload_exists else '❌ لا'}")
        
        if upload_exists:
            # التحقق من المجلدات الفرعية
            current_year = datetime.now().year
            year_folder = os.path.join(upload_folder, str(current_year))
            year_exists = os.path.exists(year_folder)
            print(f"   📅 مجلد السنة الحالية: {'✅ نعم' if year_exists else '❌ لا'}")
            
            signatures_folder = os.path.join(upload_folder, 'signatures')
            signatures_exists = os.path.exists(signatures_folder)
            print(f"   🖋️ مجلد التوقيعات: {'✅ نعم' if signatures_exists else '❌ لا'}")
        
        print()
        
        # 4. اختبار الصلاحيات
        print("4️⃣ اختبار الصلاحيات:")
        print("-" * 30)
        
        admin = User.query.filter_by(username='admin').first()
        if admin:
            admin_permissions = admin.get_all_permissions()
            print(f"   👑 صلاحيات المدير: {len(admin_permissions)}")
            
            # اختبار صلاحيات محددة
            key_permissions = [
                'create_user', 'edit_user', 'delete_user',
                'create_message', 'delete_message', 'approve_message',
                'manage_system', 'backup_system'
            ]
            
            for perm in key_permissions:
                has_perm = admin.has_permission(perm)
                status = "✅" if has_perm else "❌"
                print(f"      {status} {perm}")
        
        print()
        
        # 5. اختبار الأقسام
        print("5️⃣ اختبار الأقسام:")
        print("-" * 30)
        
        departments = Department.query.all()
        print(f"   🏢 إجمالي الأقسام: {len(departments)}")
        
        for dept in departments:
            employees_count = len(dept.employees)
            manager_name = dept.manager.name if dept.manager else 'غير محدد'
            status = 'نشط' if dept.is_active else 'غير نشط'
            print(f"   📋 {dept.name} ({status})")
            print(f"      👥 عدد الموظفين: {employees_count}")
            print(f"      👨‍💼 المدير: {manager_name}")
        
        print()
        
        # 6. اختبار الملف الشخصي
        print("6️⃣ اختبار الملف الشخصي:")
        print("-" * 30)
        
        for user in users[:2]:  # اختبار أول مستخدمين فقط
            print(f"   👤 {user.name}:")
            print(f"      🔍 is_admin(): {user.is_admin()}")
            print(f"      🔍 is_manager(): {user.is_manager()}")
            print(f"      🔍 can_manage_users(): {user.can_manage_users()}")
            print(f"      🔍 can_delete_messages(): {user.can_delete_messages()}")
            print(f"      📧 عدد الرسائل: {user.created_messages.count()}")
            print()
        
        # 7. ملخص النتائج
        print("7️⃣ ملخص النتائج:")
        print("-" * 30)
        
        results = {
            'أدوار متعددة': len(set(user.role for user in users)) >= 4,
            'مسؤولو اعتماد': len(managers) >= 2,
            'ملفات DOCX': docx_supported,
            'مجلد الرفع': upload_exists,
            'صلاحيات المدير': len(admin_permissions) >= 15 if admin else False,
            'أقسام متعددة': len(departments) >= 4
        }
        
        passed = sum(results.values())
        total = len(results)
        
        print(f"   📊 النتائج: {passed}/{total} اختبار نجح")
        
        for test_name, result in results.items():
            status = "✅" if result else "❌"
            print(f"      {status} {test_name}")
        
        print()
        
        if passed == total:
            print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        else:
            print(f"⚠️ {total - passed} اختبار فشل. يرجى مراجعة النتائج أعلاه")
        
        print()
        print("🌐 روابط النظام:")
        print("   🏠 الرئيسية: http://localhost:8585")
        print("   👥 إدارة المستخدمين: http://localhost:8585/users")
        print("   📧 إدارة الرسائل: http://localhost:8585/messages")
        print("   🏢 إدارة الأقسام: http://localhost:8585/departments")
        print("   🔐 إدارة الصلاحيات: http://localhost:8585/permissions")

if __name__ == '__main__':
    test_all_features()
