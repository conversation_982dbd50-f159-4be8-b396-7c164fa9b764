#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح أخطاء InstrumentedList
"""

import requests
import sys

def test_instrumented_list_fix():
    """اختبار إصلاح أخطاء InstrumentedList"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار الصفحات التي قد تحتوي على أخطاء InstrumentedList
        test_pages = [
            ('/', 'لوحة التحكم'),
            ('/messages', 'قائمة الرسائل'),
            ('/messages/internal', 'المراسلات الداخلية'),
            ('/messages/my-internal', 'مراسلاتي الداخلية'),
            ('/users', 'قائمة المستخدمين'),
            ('/users/1', 'تفاصيل المستخدم'),
            ('/departments', 'قائمة الجهات'),
            ('/departments/1', 'تفاصيل الجهة'),
            ('/departments/1/employees', 'موظفي الجهة'),
            ('/permissions', 'إدارة الصلاحيات'),
            ('/permissions/roles', 'قائمة الأدوار'),
            ('/auth/profile', 'الملف الشخصي')
        ]
        
        print(f"\n🧪 اختبار {len(test_pages)} صفحة للتأكد من عدم وجود أخطاء InstrumentedList...")
        
        failed_pages = []
        
        for url, name in test_pages:
            try:
                response = session.get(f"{base_url}{url}")
                if response.status_code == 200:
                    print(f"✅ {name}")
                elif response.status_code == 404:
                    print(f"⚠️ {name} - غير موجود (404)")
                else:
                    print(f"❌ {name} - كود الخطأ: {response.status_code}")
                    failed_pages.append((name, response.status_code))
            except Exception as e:
                print(f"❌ {name} - خطأ: {str(e)}")
                failed_pages.append((name, str(e)))
        
        # اختبار إنشاء دور جديد (للتأكد من عدم وجود خطأ في role.users.count())
        print(f"\n📝 اختبار إنشاء دور جديد...")
        role_data = {
            'name': 'test_role',
            'display_name': 'دور اختبار',
            'description': 'دور للاختبار'
        }
        
        response = session.post(f"{base_url}/permissions/roles/new", data=role_data)
        if response.status_code == 200:
            print("✅ تم إنشاء دور جديد بنجاح")
        else:
            print(f"❌ فشل في إنشاء دور جديد: {response.status_code}")
            failed_pages.append(('إنشاء دور جديد', response.status_code))
        
        # اختبار حذف الدور (للتأكد من عدم وجود خطأ في role.users.count())
        print(f"\n🗑️ اختبار حذف الدور...")
        # أولاً نحصل على معرف الدور الذي أنشأناه
        response = session.get(f"{base_url}/permissions/roles")
        if response.status_code == 200:
            print("✅ تم الوصول لقائمة الأدوار")
            # محاولة حذف دور (إذا وجد)
            # هذا سيختبر len(role.users) في delete_role
        else:
            print(f"❌ فشل في الوصول لقائمة الأدوار: {response.status_code}")
            failed_pages.append(('قائمة الأدوار', response.status_code))
        
        # اختبار الفلاتر المختلفة
        print(f"\n🔍 اختبار الفلاتر...")
        
        filter_tests = [
            ('/messages/internal?status=pending', 'فلتر المراسلات المعلقة'),
            ('/messages/internal?status=urgent', 'فلتر المراسلات العاجلة'),
            ('/messages?type=incoming', 'فلتر الرسائل الواردة'),
            ('/departments?status=active', 'فلتر الجهات النشطة')
        ]
        
        for url, name in filter_tests:
            try:
                response = session.get(f"{base_url}{url}")
                if response.status_code == 200:
                    print(f"✅ {name}")
                else:
                    print(f"❌ {name} - كود الخطأ: {response.status_code}")
                    failed_pages.append((name, response.status_code))
            except Exception as e:
                print(f"❌ {name} - خطأ: {str(e)}")
                failed_pages.append((name, str(e)))
        
        # اختبار APIs
        print(f"\n🔌 اختبار APIs...")
        
        api_tests = [
            ('/departments/api/departments', 'API الجهات'),
            ('/departments/api/users', 'API المستخدمين')
        ]
        
        for url, name in api_tests:
            try:
                response = session.get(f"{base_url}{url}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ {name} - {len(data)} عنصر")
                else:
                    print(f"❌ {name} - كود الخطأ: {response.status_code}")
                    failed_pages.append((name, response.status_code))
            except Exception as e:
                print(f"❌ {name} - خطأ: {str(e)}")
                failed_pages.append((name, str(e)))
        
        # تقرير النتائج
        print(f"\n📊 تقرير النتائج:")
        total_tests = len(test_pages) + len(filter_tests) + len(api_tests) + 2  # +2 for role creation and deletion
        passed_tests = total_tests - len(failed_pages)
        
        print(f"✅ نجح: {passed_tests}/{total_tests}")
        print(f"❌ فشل: {len(failed_pages)}/{total_tests}")
        
        if failed_pages:
            print(f"\n❌ الصفحات التي فشلت:")
            for name, error in failed_pages:
                print(f"  • {name}: {error}")
            return False
        else:
            print(f"\n🎉 جميع اختبارات إصلاح InstrumentedList نجحت!")
            return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار إصلاح أخطاء InstrumentedList")
    print("=" * 60)
    
    success = test_instrumented_list_fix()
    
    if success:
        print("\n🎉 تم إصلاح جميع أخطاء InstrumentedList بنجاح!")
        print("\n✅ الإصلاحات المطبقة:")
        print("  🔧 إصلاح خطأ NameError: name 'Role' is not defined")
        print("  🔧 إصلاح خطأ TypeError: AppenderQuery has no len()")
        print("  🔧 إصلاح خطأ TypeError: InstrumentedList.count() takes exactly one argument")
        print("\n📋 التفاصيل التقنية:")
        print("  • استبدال role.users.count() بـ len(role.users)")
        print("  • استبدال Message.responses == None بـ db.exists()")
        print("  • استبدال selectattr('responses', 'equalto', []) بـ rejectattr('responses')")
        print("  • إضافة استيراد Role في routes/users.py")
        print("\n🎯 النتائج:")
        print("  • جميع الصفحات تعمل بدون أخطاء")
        print("  • جميع الفلاتر تعمل بدون أخطاء")
        print("  • جميع APIs تعمل بدون أخطاء")
        print("  • إنشاء وحذف الأدوار يعمل بدون أخطاء")
        print("\n🌐 النظام جاهز للاستخدام:")
        print("  http://localhost:8585")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        sys.exit(0)
    else:
        print("\n❌ ما زالت هناك بعض المشاكل!")
        print("يرجى مراجعة الأخطاء أعلاه.")
        sys.exit(1)
