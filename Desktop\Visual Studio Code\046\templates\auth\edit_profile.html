{% extends "base.html" %}

{% block title %}تعديل الملف الشخصي - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user-edit me-2"></i>
                    تعديل الملف الشخصي
                </h2>
                <div>
                    <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للملف الشخصي
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>معلوماتي الشخصية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-user me-1"></i>الاسم الكامل
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="name" 
                                           name="name" 
                                           value="{{ current_user.name }}" 
                                           required>
                                    <div class="form-text">يمكنك تعديل اسمك الكامل هنا</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-at me-1"></i>اسم المستخدم
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="username" 
                                           value="{{ current_user.username }}" 
                                           readonly>
                                    <div class="form-text text-muted">لا يمكن تغيير اسم المستخدم</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">
                                        <i class="fas fa-user-tag me-1"></i>الدور
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="role" 
                                           value="{% if current_user.role.value == 'ADMIN' %}مدير النظام{% elif current_user.role.value == 'MANAGER' %}مدير{% elif current_user.role.value == 'SECRETARY' %}سكرتير{% else %}موظف{% endif %}" 
                                           readonly>
                                    <div class="form-text text-muted">يتم تحديد الدور من قبل المدير</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="department" class="form-label">
                                        <i class="fas fa-building me-1"></i>القسم
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="department" 
                                           value="{{ current_user.department.name if current_user.department else 'غير محدد' }}" 
                                           readonly>
                                    <div class="form-text text-muted">يتم تحديد القسم من قبل المدير</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="created_at" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>تاريخ الانضمام
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="created_at" 
                                           value="{{ current_user.created_at.strftime('%Y-%m-%d %H:%M') }}" 
                                           readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-check-circle me-1"></i>الحالة
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="status" 
                                           value="{% if current_user.is_active %}نشط{% else %}غير نشط{% endif %}" 
                                           readonly>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                            <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-warning">
                                <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">الصلاحيات الحالية:</h6>
                            <div class="mb-3">
                                {% if current_user.role.value == 'ADMIN' %}
                                    <span class="badge bg-danger me-1">جميع الصلاحيات</span>
                                    <span class="badge bg-primary me-1">إدارة النظام</span>
                                    <span class="badge bg-primary me-1">إدارة المستخدمين</span>
                                    <span class="badge bg-primary me-1">إدارة الأقسام</span>
                                {% elif current_user.role.value == 'MANAGER' %}
                                    <span class="badge bg-warning me-1">إدارة المستخدمين</span>
                                    <span class="badge bg-info me-1">إدارة الرسائل</span>
                                    <span class="badge bg-info me-1">حذف الرسائل</span>
                                    <span class="badge bg-info me-1">عرض التقارير</span>
                                {% elif current_user.role.value == 'SECRETARY' %}
                                    <span class="badge bg-success me-1">إدارة المستخدمين</span>
                                    <span class="badge bg-info me-1">إدارة الرسائل</span>
                                    <span class="badge bg-info me-1">أرشفة الرسائل</span>
                                {% else %}
                                    <span class="badge bg-secondary me-1">عرض الرسائل</span>
                                    <span class="badge bg-secondary me-1">إنشاء الرسائل</span>
                                    <span class="badge bg-secondary me-1">تعديل رسائلي</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">إحصائيات سريعة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-envelope text-primary me-2"></i>إجمالي الرسائل: <strong>{{ current_user.created_messages.count() }}</strong></li>
                                <li><i class="fas fa-inbox text-success me-2"></i>رسائل واردة: <strong>{{ current_user.created_messages.filter_by(message_type='incoming').count() }}</strong></li>
                                <li><i class="fas fa-paper-plane text-info me-2"></i>رسائل صادرة: <strong>{{ current_user.created_messages.filter_by(message_type='outgoing').count() }}</strong></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.form-control[readonly] {
    background-color: #f8f9fa;
    border-color: #e9ecef;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}
</style>
{% endblock %}
