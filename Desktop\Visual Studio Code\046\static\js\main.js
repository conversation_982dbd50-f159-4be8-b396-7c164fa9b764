// Main JavaScript file for the correspondence system

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // File upload drag and drop
    setupFileUpload();
    
    // Form validation
    setupFormValidation();
    
    // Search functionality
    setupSearch();
    
    // Confirmation dialogs
    setupConfirmationDialogs();
});

function setupFileUpload() {
    var fileUploadAreas = document.querySelectorAll('.file-upload-area');
    
    fileUploadAreas.forEach(function(area) {
        var fileInput = area.querySelector('input[type="file"]');
        
        if (!fileInput) return;
        
        // Drag and drop events
        area.addEventListener('dragover', function(e) {
            e.preventDefault();
            area.classList.add('dragover');
        });
        
        area.addEventListener('dragleave', function(e) {
            e.preventDefault();
            area.classList.remove('dragover');
        });
        
        area.addEventListener('drop', function(e) {
            e.preventDefault();
            area.classList.remove('dragover');
            
            var files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateFileInfo(fileInput, files[0]);
            }
        });
        
        // Click to upload
        area.addEventListener('click', function() {
            fileInput.click();
        });
        
        // File input change
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                updateFileInfo(this, this.files[0]);
            }
        });
    });
}

function updateFileInfo(input, file) {
    var area = input.closest('.file-upload-area');
    var info = area.querySelector('.file-info');
    
    if (!info) {
        info = document.createElement('div');
        info.className = 'file-info mt-2';
        area.appendChild(info);
    }
    
    var fileSize = formatFileSize(file.size);
    var fileIcon = getFileIcon(file.name);
    
    info.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas ${fileIcon} me-2"></i>
            <div>
                <div class="fw-bold">${file.name}</div>
                <small class="text-muted">${fileSize}</small>
            </div>
        </div>
    `;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    var k = 1024;
    var sizes = ['Bytes', 'KB', 'MB', 'GB'];
    var i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileIcon(filename) {
    var extension = filename.split('.').pop().toLowerCase();
    var iconMap = {
        'pdf': 'fa-file-pdf',
        'doc': 'fa-file-word',
        'docx': 'fa-file-word',
        'xls': 'fa-file-excel',
        'xlsx': 'fa-file-excel',
        'ppt': 'fa-file-powerpoint',
        'pptx': 'fa-file-powerpoint',
        'jpg': 'fa-file-image',
        'jpeg': 'fa-file-image',
        'png': 'fa-file-image',
        'gif': 'fa-file-image',
        'txt': 'fa-file-text',
        'zip': 'fa-file-archive',
        'rar': 'fa-file-archive'
    };
    
    return iconMap[extension] || 'fa-file';
}

function setupFormValidation() {
    var forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
}

function setupSearch() {
    var searchInput = document.querySelector('#search-input');
    var searchResults = document.querySelector('#search-results');
    
    if (!searchInput || !searchResults) return;
    
    var searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        var query = this.value.trim();
        
        if (query.length < 2) {
            searchResults.innerHTML = '';
            return;
        }
        
        searchTimeout = setTimeout(function() {
            performSearch(query);
        }, 300);
    });
}

function performSearch(query) {
    // This would typically make an AJAX request to search endpoint
    // For now, we'll just redirect to the search page
    if (query.length >= 2) {
        window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
}

function setupConfirmationDialogs() {
    var deleteButtons = document.querySelectorAll('[data-confirm]');
    
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            var message = this.getAttribute('data-confirm') || 'هل أنت متأكد من هذا الإجراء؟';
            
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
}

// Utility functions
function showLoading() {
    var overlay = document.createElement('div');
    overlay.className = 'spinner-overlay';
    overlay.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    `;
    document.body.appendChild(overlay);
}

function hideLoading() {
    var overlay = document.querySelector('.spinner-overlay');
    if (overlay) {
        overlay.remove();
    }
}

function showAlert(message, type = 'info') {
    var alertContainer = document.querySelector('.alert-container') || document.querySelector('main');
    var alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alert, alertContainer.firstChild);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        var bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
}

// Export functions for use in other scripts
window.CorrespondenceSystem = {
    showLoading: showLoading,
    hideLoading: hideLoading,
    showAlert: showAlert,
    formatFileSize: formatFileSize,
    getFileIcon: getFileIcon
};
