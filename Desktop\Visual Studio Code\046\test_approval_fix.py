#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح قسم الاعتماد والموافقة
"""

import requests
import sys

def test_approval_fix():
    """اختبار إصلاح قسم الاعتماد والموافقة"""
    base_url = "http://localhost:8585"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f"{base_url}/auth/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار الصفحة الرئيسية للتحقق من وجود رابط الاعتماد
        print("\n🏠 اختبار الصفحة الرئيسية بعد الإصلاح...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            if 'pending_approval' in content:
                print("✅ رابط قسم الاعتماد موجود في القائمة الجانبية")
            else:
                print("⚠️ رابط قسم الاعتماد غير موجود في القائمة الجانبية")
        else:
            print(f"❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
        
        # اختبار صفحة الرسائل المعلقة للاعتماد
        print("\n📋 اختبار صفحة الرسائل المعلقة للاعتماد...")
        response = session.get(f"{base_url}/messages/pending_approval")
        if response.status_code == 200:
            print("✅ صفحة الرسائل المعلقة للاعتماد تعمل")
            
            content = response.text
            if 'الرسائل المعلقة للاعتماد' in content:
                print("✅ عنوان الصفحة صحيح")
            
            # عد الرسائل المعلقة الحالية
            import re
            pending_count = len(re.findall(r'<tr[^>]*>', content)) - 1  # -1 للهيدر
            print(f"📊 عدد الرسائل المعلقة الحالية: {pending_count}")
            
        else:
            print(f"❌ فشل في الوصول لصفحة الرسائل المعلقة: {response.status_code}")
            return False
        
        # إنشاء رسالة تتطلب اعتماد بطريقة صحيحة
        print("\n📝 إنشاء رسالة تتطلب اعتماد...")
        
        # أولاً، احصل على صفحة إنشاء رسالة جديدة للحصول على CSRF token إذا كان موجوداً
        response = session.get(f"{base_url}/messages/new")
        if response.status_code != 200:
            print(f"❌ فشل في الوصول لصفحة إنشاء رسالة: {response.status_code}")
            return False
        
        # إنشاء رسالة صادرة تتطلب اعتماد
        message_data = {
            'message_type': 'outgoing',
            'registration_number': 'TEST-APPROVAL-FIX-001',
            'destination': 'جهة خارجية للاختبار',
            'subject': 'رسالة اختبار الاعتماد المحدثة',
            'content': 'هذه رسالة لاختبار نظام الاعتماد والموافقة بعد الإصلاح',
            'department': 'قسم الاختبار',
            'priority': 'high',
            'requires_approval': 'on'  # هذا مهم جداً
        }
        
        response = session.post(f"{base_url}/messages/new", data=message_data)
        if response.status_code == 200:
            print("✅ تم إنشاء رسالة تتطلب اعتماد")
            
            # اختبار صفحة الرسائل المعلقة مرة أخرى
            print("\n🔄 اختبار صفحة الرسائل المعلقة بعد إنشاء رسالة...")
            response = session.get(f"{base_url}/messages/pending_approval")
            if response.status_code == 200:
                content = response.text
                if 'TEST-APPROVAL-FIX-001' in content:
                    print("✅ الرسالة الجديدة ظهرت في قائمة الرسائل المعلقة")
                else:
                    print("⚠️ الرسالة الجديدة لم تظهر في قائمة الرسائل المعلقة")
                    
                    # تحقق من جميع الرسائل لمعرفة ما حدث
                    print("\n🔍 البحث عن الرسالة في جميع الرسائل...")
                    response = session.get(f"{base_url}/messages")
                    if response.status_code == 200:
                        all_content = response.text
                        if 'TEST-APPROVAL-FIX-001' in all_content:
                            print("✅ الرسالة موجودة في قائمة جميع الرسائل")
                            
                            # البحث عن معرف الرسالة
                            import re
                            match = re.search(r'/messages/(\d+)[^/]*TEST-APPROVAL-FIX-001', all_content)
                            if match:
                                message_id = match.group(1)
                                print(f"🔍 معرف الرسالة: {message_id}")
                                
                                # عرض تفاصيل الرسالة
                                response = session.get(f"{base_url}/messages/{message_id}")
                                if response.status_code == 200:
                                    message_content = response.text
                                    if 'يتطلب اعتماد' in message_content or 'requires_approval' in message_content:
                                        print("✅ الرسالة تتطلب اعتماد فعلاً")
                                    else:
                                        print("⚠️ الرسالة لا تتطلب اعتماد")
                        else:
                            print("❌ الرسالة غير موجودة في أي مكان")
            
        else:
            print(f"⚠️ مشكلة في إنشاء رسالة الاختبار: {response.status_code}")
        
        # إنشاء رسالة داخلية تتطلب اعتماد
        print("\n📝 إنشاء رسالة داخلية تتطلب اعتماد...")
        
        internal_message_data = {
            'message_type': 'internal',
            'registration_number': 'INT-APPROVAL-001',
            'subject': 'رسالة داخلية تتطلب اعتماد',
            'content': 'هذه رسالة داخلية لاختبار نظام الاعتماد',
            'from_department': 'قسم الاختبار',
            'to_department': 'قسم آخر',
            'priority': 'normal',
            'requires_approval': 'on',
            'requires_response': 'on'
        }
        
        response = session.post(f"{base_url}/messages/new", data=internal_message_data)
        if response.status_code == 200:
            print("✅ تم إنشاء رسالة داخلية تتطلب اعتماد")
        else:
            print(f"⚠️ مشكلة في إنشاء رسالة داخلية: {response.status_code}")
        
        # اختبار نهائي لصفحة الرسائل المعلقة
        print("\n📊 اختبار نهائي لصفحة الرسائل المعلقة...")
        response = session.get(f"{base_url}/messages/pending_approval")
        if response.status_code == 200:
            content = response.text
            
            # عد الرسائل المعلقة النهائية
            import re
            final_pending_count = len(re.findall(r'<tr[^>]*>', content)) - 1  # -1 للهيدر
            print(f"📊 عدد الرسائل المعلقة النهائية: {final_pending_count}")
            
            if 'TEST-APPROVAL-FIX-001' in content or 'INT-APPROVAL-001' in content:
                print("✅ تم العثور على رسائل الاختبار في قائمة الرسائل المعلقة")
            else:
                print("⚠️ لم يتم العثور على رسائل الاختبار في قائمة الرسائل المعلقة")
        
        print("\n🎉 تم اكتمال اختبار إصلاح قسم الاعتماد والموافقة!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ: لا يمكن الاتصال بالخادم. تأكد من تشغيل التطبيق على المنفذ 8585")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار إصلاح قسم الاعتماد والموافقة")
    print("=" * 60)
    
    success = test_approval_fix()
    
    if success:
        print("\n✅ تم إصلاح قسم الاعتماد والموافقة بنجاح!")
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ تحديث شروط عرض رابط الاعتماد في القائمة الجانبية")
        print("  ✅ تحديث صلاحيات الوصول لصفحة الرسائل المعلقة")
        print("  ✅ إضافة جميع الأدوار الجديدة لصلاحيات الاعتماد")
        print("\n🎯 الأدوار التي يمكنها الوصول لقسم الاعتماد:")
        print("  👤 مدير النظام")
        print("  🎯 مدير عام")
        print("  🏢 رئيس قسم")
        print("  👔 مدير")
        print("  👁️ مشرف")
        print("  📝 سكرتير")
        print("\n🌐 للوصول لقسم الاعتماد:")
        print("  📋 الرسائل المعلقة: http://localhost:8585/messages/pending_approval")
        print("  📝 إنشاء رسالة تتطلب اعتماد: http://localhost:8585/messages/new")
        print("\n🔑 بيانات الدخول:")
        print("  👤 اسم المستخدم: admin")
        print("  🔐 كلمة المرور: admin123")
        print("\n💡 ملاحظات:")
        print("  • رابط قسم الاعتماد متاح الآن في القائمة الجانبية")
        print("  • جميع الأدوار الإدارية يمكنها الوصول لقسم الاعتماد")
        print("  • الرسائل التي تتطلب اعتماد تظهر في قائمة منفصلة")
        sys.exit(0)
    else:
        print("\n❌ فشل في إصلاح قسم الاعتماد والموافقة!")
        sys.exit(1)
