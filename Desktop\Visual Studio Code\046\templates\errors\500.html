{% extends "base.html" %}

{% block title %}خطأ في الخادم - نظام المراسلات الإلكترونية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="text-center py-5">
            <div class="error-icon mb-4">
                <i class="fas fa-server fa-5x text-danger"></i>
            </div>
            
            <h1 class="display-4 text-muted">500</h1>
            <h2 class="mb-3">خطأ في الخادم</h2>
            
            <p class="lead text-muted mb-4">
                عذراً، حدث خطأ غير متوقع في الخادم. يرجى المحاولة مرة أخرى لاحقاً.
            </p>
            
            <div class="alert alert-warning">
                <i class="fas fa-info-circle me-2"></i>
                إذا استمر هذا الخطأ، يرجى التواصل مع مدير النظام.
            </div>
            
            <div class="d-flex justify-content-center gap-3">
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>
                    العودة للرئيسية
                </a>
                
                <button onclick="location.reload()" class="btn btn-secondary">
                    <i class="fas fa-redo me-2"></i>
                    إعادة المحاولة
                </button>
                
                <button onclick="history.back()" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للخلف
                </button>
            </div>
            
            <div class="mt-5">
                <h5>ماذا يمكنك فعله:</h5>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>تحديث الصفحة</li>
                    <li><i class="fas fa-check text-success me-2"></i>العودة للصفحة الرئيسية</li>
                    <li><i class="fas fa-check text-success me-2"></i>المحاولة مرة أخرى بعد قليل</li>
                    <li><i class="fas fa-check text-success me-2"></i>التواصل مع الدعم الفني</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.error-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
</style>
{% endblock %}
